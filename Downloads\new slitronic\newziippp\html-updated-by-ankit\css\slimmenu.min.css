.menu-collapser{
    position:absolute;
    right: 0;
    top: 0;
    height:48px;
    color:#FFF;
    box-sizing:border-box;
    -moz-box-sizing:border-box;
    -webkit-box-sizing:border-box
}
.collapse-button{
    position:absolute;
    right:15px;
    top:26px;
    width:35px;
    padding:9px;
    color:#FFFFFF;
    font-size:14px;
    text-align:center;
    background-color:#7B65EA;
    border-radius:2px;
    cursor:pointer;
    box-sizing:border-box;
    -moz-box-sizing:border-box;
    -webkit-box-sizing:border-box
}
.collapse-button:hover,.collapse-button:focus{
    color:#FFF;
    background-color:#7B65EA;
}
.collapse-button .icon-bar{
    display:block;
    height:2px;
    width:100%;
    margin:2px 0;
    background-color:#F5F5F5;
    border-radius:1px
}
ul.slimmenu{
    width:100%;
    margin:0;
    padding:0;
    list-style-type:none
}
ul.slimmenu:before,ul.slimmenu:after{
    content:'';
    display:table
}
ul.slimmenu:after{
    clear:both
}
ul.slimmenu.collapsed li{
    display:block;
    width:100%;
    box-sizing:border-box;
    -moz-box-sizing:border-box;
    -webkit-box-sizing:border-box
}
ul.slimmenu.collapsed li>ul{
    position:static;
    display:none
}
ul.slimmenu.collapsed li a{
    display:block;
    box-sizing:border-box;
    -moz-box-sizing:border-box;
    -webkit-box-sizing:border-box
}
ul.slimmenu.collapsed li .sub-toggle{
    height:40px
}
ul.slimmenu li
{
    position:relative;
    float:left;
    display:inline-block;
    text-transform: uppercase;
    background:url(../images/horizontal-divider.png) no-repeat center bottom #000;
}
ul.slimmenu li ul{
    margin:0;
    list-style-type:none
}
ul.slimmenu li ul li{
}
ul.slimmenu li>ul{
    position:absolute;
    left:0;
    top:100%;
    z-index:999;
    display:none;
    width:100%
}
ul.slimmenu li>ul>li ul{
    position:absolute;
    left:100%;
    top:0;
    z-index:999;
    display:none;
    width:100%
}
ul.slimmenu li a{
    display:block;
    padding:12px 0 12px 0;
    color:#fff;
    font-size:16px;
    font-weight:400;
    transition:background-color 0.5s ease-out;
    -o-transition:background-color 0.5s ease-out;
    -moz-transition:background-color 0.5s ease-out;
    -webkit-transition:background-color 0.5s ease-out
}
ul.slimmenu li a:hover{
    text-decoration:none;
    background: url(../images/hoverbg.png) no-repeat center center;
}
ul.slimmenu li .sub-toggle{
    background:none repeat scroll 0 0 rgba(0,0,0,0.075);
    position:absolute;
    right:0;
    top:0;
    z-index:999;
    width:48px;
    height:100%;
    text-align:center;
    cursor:pointer
}
ul.slimmenu li .sub-toggle:before{
    content:'';
    display:inline-block;
    height:100%;
    margin-right:-0.25em;
    vertical-align:middle
}
ul.slimmenu li .sub-toggle>i{
    display:inline-block;
    color:#333;
    font-size:18px;
    vertical-align:middle
}
ul.slimmenu>li{
}
ul.slimmenu>li:first-child,ul.slimmenu>li.has-submenu+li{
    border-left:0
}