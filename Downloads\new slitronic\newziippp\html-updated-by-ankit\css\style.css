@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Regular.eot');
    src: url('../fonts/SplineSansMono-Regular.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Regular.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Regular.woff') format('woff'),
         url('../fonts/SplineSansMono-Regular.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Regular.svg#SplineSansMono-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Medium.eot');
    src: url('../fonts/SplineSansMono-Medium.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Medium.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Medium.woff') format('woff'),
         url('../fonts/SplineSansMono-Medium.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Medium.svg#SplineSansMono-Medium') format('svg');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Bold.eot');
    src: url('../fonts/SplineSansMono-Bold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Bold.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Bold.woff') format('woff'),
         url('../fonts/SplineSansMono-Bold.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Bold.svg#SplineSansMono-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Titillium Web';
    src: url('../fonts/TitilliumWeb-Bold.eot');
    src: url('../fonts/TitilliumWeb-Bold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/TitilliumWeb-Bold.woff2') format('woff2'),
         url('../fonts/TitilliumWeb-Bold.woff') format('woff'),
         url('../fonts/TitilliumWeb-Bold.ttf') format('truetype'),
         url('../fonts/TitilliumWeb-Bold.svg#TitilliumWeb-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Titillium Web';
    src: url('../fonts/TitilliumWeb-SemiBold.eot');
    src: url('../fonts/TitilliumWeb-SemiBold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/TitilliumWeb-SemiBold.woff2') format('woff2'),
         url('../fonts/TitilliumWeb-SemiBold.woff') format('woff'),
         url('../fonts/TitilliumWeb-SemiBold.ttf') format('truetype'),
         url('../fonts/TitilliumWeb-SemiBold.svg#TitilliumWeb-SemiBold') format('svg');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
*
{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
html {
    scroll-behavior: smooth;
  }
  html, body {
  overflow-x: hidden;
}
h1, h2, h3, h4, h5, h6, p
{
    margin: 0;
    padding: 0;
}
ul, ol, li
{
    margin: 0;
    padding: 0;
    list-style-type: none;
}
a, a:hover, a:focus
{
    text-decoration: none;
    border: 0;
    outline: 0;
}
img
{
    max-width: 100%;
    height: auto;
}
body
{
    font-family: 'Spline Sans Mono';
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
    color: #000;
}
.container
{
    max-width: 1600px;
    padding: 0 15px;
    margin: 0 auto;
}
.headersec
{
    width: 100%;
    overflow: hidden;
    padding: 10px 0;
    background: #000;
    height:129px;
}
.sticky
{
    position: fixed;
    width: 100%;
    z-index: 99;
    left: 0;
    top: 0;
}

.logonnavsec{
    padding-top: 40px ;
    display: flex;

    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.logo img{
    width: 361.8px;
height: 57.13px;
top: 48.43px;
left: 115px;
padding-top:10px;

}
.navsec ul li
{
    float: left;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    text-transform:uppercase;
    margin:0 0 0 25px;
}
.navsec ul li:first-child
{
    margin: 0;
}
.navsec ul li a
{
    color: #999999;
}
.navsec ul li a:hover, .navsec ul li a.active
{
    color: #fff;
}
.bannersec
{
    width: 100%;
    height: 976px;
    overflow: hidden;
    position: relative;
}
.bannersec .slidersec, .bannersec .slidersec .owl-stage-outer, .bannersec .slidersec .owl-stage-outer .owl-stage, .bannersec .slidersec .owl-stage-outer .owl-stage .owl-item, .bannersec .slidersec .owl-stage-outer .owl-stage .owl-item .item
{
    height: 100%;
}
.slidersec img, .slidersec video
{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.slidersec .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 20px;
    z-index: 1;
    line-height: 1;
}
.slidersec .owl-dots .owl-dot
{
    margin: 0 5px;
}
.slidersec .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #A188EB;
}
.slidersec .owl-dots .owl-dot.active span
{
    background: #A188EB;
}
.slidecontpart
{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
}
.slidecontpart .container
{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    padding: 50px 15px;
}
.slidecontpart h1
{
    font-size: 46px;
    line-height: 53px;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #fff;
    max-width: 700px;
    text-transform: uppercase;
    letter-spacing: 5px;
}
.slidecontpart p
{
    max-width: 700px;
    color: #fff;
    font-weight: normal;
    padding: 40px 0 0;
    letter-spacing: 5px;
}
/* a.getintouchbut
{ */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#050ef1+0,fd0200+100 */
    /* background: linear-gradient(to right, #050ef1 0%,#fd0200 100%);  */
    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    /* line-height: 67px;
    width: 254px;
    background-size: 100% 100%;
    display: inline-block;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    margin-top: 35px;
    transition: all 0.5s ease;
    text-align: center;
}
a.getintouchbut:hover
{
    color: #fff; */
    /* Keep the original gradient background */
    /* background: linear-gradient(to right, #050ef1 0%,#fd0200 100%);
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
    transform: scale(1.05); */
    /* Add inner borders on left and top */
    /* border-left: 4px solid rgba(255,255,255,0.8);
    border-top: 4px solid rgba(255,255,255,0.8);
    border-right: none;
    border-bottom: none; */
    /* Move text to right with animation */
    /* text-indent: 15px;
    transition: all 0.3s ease;
} */

/* Slide-specific hover effects for desktop and mobile */
/* .slide-1 a.getintouchbut:hover
{
    background: linear-gradient(to right, #2C3E50 0%, #34495E 100%); /* Dark blue-gray gradient for slide 1 */
    /* border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}

.slide-2 a.getintouchbut:hover
{ */
    /* background: linear-gradient(to right, #8E44AD 0%, #9B59B6 100%); */
     /* Purple gradient for slide 2 */
    /* border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}

.slide-3 a.getintouchbut:hover
{ */
    /* background: linear-gradient(to right, #E67E22 0%, #F39C12 100%);  */
    /* Orange gradient for slide 3 */
    /* border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}

.slide-4 a.getintouchbut:hover
{ */
    /* background: linear-gradient(to right, #27AE60 0%, #2ECC71 100%); */
     /* Green gradient for slide 4 */
    /* border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}

.slide-5 a.getintouchbut:hover
{ */
    /* background: linear-gradient(to right, #E74C3C 0%, #C0392B 100%);  */
    /* Red gradient for slide 5 */
    /* border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}

.slide-6 a.getintouchbut:hover
{ */
    /* background: linear-gradient(to right, #3498DB 0%, #2980B9 100%); */
     /* Blue gradient for slide 6
    border-left: 4px solid rgba(255,255,255,0.9);
    border-top: 4px solid rgba(255,255,255,0.9);
}
.fstcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstcontbg.png) no-repeat center top;
    background-size: cover;
}  */
/*.lftrytlinepart
{
    background-image: url(../images/lftline.png), url(../images/rytline.png);
    background-position: left center, right center;
    background-repeat: no-repeat, no-repeat;
    padding: 0 50px;
}*/
a.getintouchbut {
  position: relative;
  display: inline-block;
  font-family: 'Titillium Web', sans-serif;
  font-weight: bold;
  font-size: 22px;
  color: #fff;
  text-decoration: none;
  text-transform: uppercase;
  text-align: center;
  line-height: 67px;
  width: 254px;
  margin-top: 35px;
  border-radius: 8px;
  z-index: 1;
  background: linear-gradient(to right, #050ef1 0%, #fd0200 100%);/* horizontal gradient bg */
  border: 2px 8px solid transparent;
  transition: all 0.4s ease;
  overflow: hidden;
  letter-spacing: 1px;
  padding: 0 20px;
   transform-origin: center;
}

/* transparent bg + visible gradient border on hover */
a.getintouchbut:hover {
  background-color: transparent !important;
  border: 2px solid transparent;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1;
  border-image-slice: 1;
  text-indent: 15px;
  transition: transform 0.3s ease;
  transform: scale(1.05) translateX(5px);

  /* Gradient text effect */
  background: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}












.lftrytlinepart h4
{
    font-size: 70px;
    line-height: 85px;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Firefox ke liye */
    background-clip: text;
}
.fstcontsec p
{
    text-align: center;
}
.fstsecforthcolpart
{
    display: flex;
    flex-wrap: wrap;
    margin: 70px 0;
}
.sglecolpart
{
    flex:0 0 25%;
    max-width: 25%;
    text-align: center;
    background: url(../images/line.png) no-repeat left center;
    background-size: auto 100%;
    display: flex;
    flex-direction: column;
    row-gap: 17px;
    padding: 0 15px;
}
.sglecolpart:first-child
{
    background-image: none;
}
.sglecolpart h2
{
    font-size: 30px;
    font-weight: bold;
    line-height: 35px;
    color: #000;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
}
.fstcontsec h3
{
    max-width:750px;
    margin: 0 auto;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #000;
    font-size: 37px;
    line-height: 45px;
    text-align: center;
    text-transform: capitalize;
}
.sndcontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/sndcontsecbg.png) no-repeat center center;
    background-size: cover;
    padding: 100px 0;
}
.sndcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 75px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.servicecontsec
{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.serviceimgsec
{
    flex:0 0 40%;
    max-width: 40%;
}
.servicwcontentsec
{
    flex:0 0 60%;
    max-width: 60%;
    padding: 0 0 0 50px;
}
.servicwcontentsec h3
{
    padding: 0 0 50px 0;
}
.tabslidersec .owl-nav
{
    margin: 0;
}
.tabslidersec .owl-nav .owl-prev
{
    background: url(../images/toparw.png) no-repeat left top !important;
    background-size: 100% 100%;
    width: 38px;
    height: 45px;
    display: block;
    position: absolute;
    right: -32px;
    top: 0;
    z-index: 1;
    margin: 0;
}
.tabslidersec .owl-nav .owl-next
{
    background: url(../images/btmarw.png) no-repeat left top !important;
    background-size: 100% 100%;
    width: 38px;
    height: 45px;
    display: block;
    position: absolute;
    right: -32px;
    bottom: 0;
    z-index: 1;
    margin: 0;
}
.tabslidersec .owl-nav span
{
    display: none;
}
.tabslidersec .owl-dots
{
    margin: 0 !important;
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 15px;
}
.tabslidersec .owl-dots .owl-dot
{
    display: block;
}
.tabslidersec .owl-dots .owl-dot span
{
    margin: 5px 0;
    width: 15px;
    height: 15px;
    border: 1px solid #000;
    background-color: #fff;
}
.tabslidersec .owl-dots .owl-dot span:hover, .tabslidersec .owl-dots .owl-dot.active span
{
    border: 1px solid #000;
    background-color: #000;
}
.servicedisplayflex
{
    margin: 0;
    width: 100%;
    overflow: hidden;
}
.sgleservicedisplayflex
{
    float: left;
    width: 31.3333333%;
    margin: 4% 2% 0 0;
}
.sgleservicedisplayflex .inner
{
    font-size: 17px;
    letter-spacing: normal;
    line-height: 1.5;
}
.servicecontheight
{
    background: linear-gradient(45deg, #050EF1, #FD0200);
    padding: 2px;
    -webkit-border-bottom-right-radius: 20px;
    -moz-border-radius-bottomright: 20px;
    border-bottom-right-radius: 20px;
    width: 100%;
    height: 100%;
}
.servicecontheight > .inner
{
    padding: 18px;
    background: white;
    -webkit-border-bottom-right-radius: 20px;
    -moz-border-radius-bottomright: 20px;
    border-bottom-right-radius: 20px;
    width: 100%;
    height: 100%;
}
.tabsec
{
    width: 100%;
    overflow: hidden;
}
.tabsec ul li
{
    float: left;
    width: 33.3333333%;
    text-align: center;
    font-size: 36px;
    line-height: normal;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
}
.tabsec ul li a
{
    display: block;
    padding: 75px 15px;
    color: #000;
    background: #dfd4f3;
    transition: all 0.5s ease;
}
.tabsec ul li a:hover, .tabsec ul li a.active
{
    background: #25223F;
    color: #fff;
}
.tabcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
}
.tabcontsec h2
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 42px;
    line-height: 49px;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    padding: 0 0 25px 0;
}
.tabcontsec p
{
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    color: #25223F;
}
.tabcontsec a.getintouchbut
{
    line-height: 46px;
    width: 171px;
    color: #fff;
    font-size: 15px;
    background:url(../images/darkgetintouch.png) no-repeat left top;
    background-size: 100% 100%;
    transition: all 0.5s ease;
}
.tabcontsec a.getintouchbut:hover
{
    color: #6E57B0;
}
.carouselsec
{
    width: 100%;
    overflow: hidden;
    padding-bottom: 100px;
}
.facilitydesignsec .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -40px;
    z-index: 1;
    line-height: 1;
}
.facilitydesignsec .owl-dots .owl-dot
{
    margin: 0 5px;
}
.facilitydesignsec .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #25223F;
}
.facilitydesignsec .owl-dots .owl-dot.active span
{
    background: #25223F;
}
/*.carouselsec .container
{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}*/
.sglecarouselsec
{
    width: 100%;
    background: #6E57B0;
    padding: 18px 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.iconsec
{
    float: left;
    width: 20%;
}
.iconsec img
{
    width: auto !important;
}
.carouselcontsec
{
    float: right;
    width: 80%;
    padding-left: 20px;
}
.thrdcontsec
{
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 100px 0;
}
.customervoicesec
{
    width: 100%;
}
.customervoice .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -40px;
    z-index: 1;
    line-height: 1;
}
.customervoice .owl-dots .owl-dot
{
    margin: 0 5px;
}
.customervoice .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #A188EB;
}
.customervoice .owl-dots .owl-dot.active span
{
    background: #A188EB;
}
.customervoicesec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.industryleadersec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}
.sgleindustrysec
{
    flex:0 0 14.2%;
    max-width: 14.2%;
    padding: 0 10px;
    flex-wrap: wrap;
}
/* .sgleindustrysec h3
{
    background: linear-gradient(to right, #480bb0 0%, #5c0a9d 100%);
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: normal;
    font-weight: normal;
    padding: 10px 5px;
    min-height: 70px;
} */
/* .sgleindustrysec h3 strong
{
    font-weight: normal;
    display: block;
} */



.onestgrediant { background: linear-gradient(267.62deg, #FD0200 -800.9%, #050EF1 433.34%);}
.twostgrediant { background: linear-gradient(267.62deg, #FD0200 -680.08%, #050EF1 482.9%);}

.threegradient {background: linear-gradient(267.62deg, #FD0200 -564.5%, #050EF1 615.58%);
 }
.fourgradient {background: linear-gradient(267.62deg, #FD0200 -446.3%, #050EF1 720.96%);
 }
.fivegradient {
background: linear-gradient(270deg, #FD0200 -339.36%, #050EF1 879.39%);
 }
.sixgradient {
background: linear-gradient(267.62deg, #FD0200 -207.05%, #050EF1 965.91%);
}
.sevenstgrediant {background: linear-gradient(267.42deg, #FD0200 -86.89%, #050EF1 1083.09%);
}
 
.tech-box {
  

  color: white;
  font-family: 'Titillium Web', sans-serif;
  font-weight: 700;
  font-size: 16.64px;
  line-height: 18.42px;
  letter-spacing: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 95% 100%, 0% 100%);
  box-sizing: border-box;
  padding: 20px;
}


.tech-box p {

  font-weight: normal!important;
    font-size: 14px;
}

/* Service section image styling */
/* .ssimg {
    width: 1068px;
    height: 743px;
    top: 1654.19px;
    left: 426px;
    position: absolute;
} */




.sgleindustrysec ul li
{
    text-align: center;
    border-bottom: 1px solid rgba(0,0,0,0.15);
    padding: 20px 0 5px 0;
}
.sgleindustrysec ul li img
{
    max-height: 21px;
}
.sglecustomersec
{
    width: 100%;
    height: 340px;
    display: flex;
    flex-wrap: wrap;
    background: #FEFFFFC9;
    padding: 25px;
    align-items: center;
    justify-content: center;
    position: relative;
}
.sglecustomersec img
{
    width: auto !important;
}
.hovercustomersec
{
    background: url(../images/bg.jpg) no-repeat left top;
    background-size: cover;
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 25px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 5px;
    opacity: 0;
    transition: all 0.5s ease;
    justify-content:left;
}
.sglecustomersec:hover .hovercustomersec
{
    opacity: 1;
}
.hovercustomersec p
{
    font-size: 17px;
    line-height: 26px;
    color: #25223F;
    font-weight: normal;
    text-align: left;
}
.starratingsec
{
    text-align: left;
    padding: 5px 0;
    width: 100%;
    overflow: hidden;
}
.hovercustomersec h3
{
    font-size: 18px;
    font-weight: normal;
    line-height: normal;
    color: #25223F;
    text-align: left;
    width: 100%;
}
.hovercustomersec h4
{
    font-size: 13px;
    font-weight: normal;
    line-height: normal;
    color: #25223F;
    text-align: left;
    width: 100%;
}
.forthcontsec
{
    width: 100vw;
    max-width: 100vw;
    overflow: hidden;
    padding: 100px 0;
    background: linear-gradient(to right, #5b0a9e 0%, #ca0532 100%);
    -webkit-border-top-left-radius: 50px;
    -webkit-border-top-right-radius: 50px;
    -moz-border-radius-topleft: 50px;
    -moz-border-radius-topright: 50px;
    border-top-left-radius: 50px;
    border-top-right-radius: 50px;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure container is properly contained within full-width case studies section */
.forthcontsec .container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
}

.casestudiessec
{
    width: 100%;
    overflow: hidden;
}
.casestudiessec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
}
.casestudycontsec
{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    gap: 60px;
    justify-content: space-between;
}
.sglecasestudycontsec
{
    max-width: 30%;
    flex:0 0 30%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    border: 1px solid #6E57B000;
    height: 310px;
}
.transparentbg
{
    background: url(../images/transparentbg.png) no-repeat left top;
    width: 100%;
    height: 100%;
    background-size: cover;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
}
.casestudycontpart
{
    width: 100%;
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -130px;
    z-index: 1;
    transition: all 1s ease;
    background: #fff;
    padding-top: 10px;
}
.sglecasestudycontsec:hover .casestudycontpart
{
    bottom: 0;
}
.sglecasestudycontsec h3
{
    font-size: 20px;
    line-height: 28px;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #000;
    padding: 0 20px 10px 20px;
}
.sglecasestudycontsec p
{
    font-size: 15px;
    line-height: 21px;
    font-weight: normal;
    color: #000;
    padding: 0 20px;
}
.sglecasestudycontsec a.downloadusecasebut
{
    background: #fff;
    line-height: 45px;
    padding: 0;
    display: block;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 14px;
    color: #000;
    text-transform: uppercase;
    margin-top: 20px;
    text-align: center;
    transition: all 0.5s ease;
    border-top: 1px solid #000;
}
.sglecasestudycontsec a.downloadusecasebut:hover
{
    background: #000;
    color: #fff;
}
.fifthcontsec
{
    width: 100vw;
    max-width: 100vw;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstcontbg.png) no-repeat center top;
    background-size: cover;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure container is properly contained within full-width contact section */
.fifthcontsec .container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
}

.contactsec
{
    width: 100%;
    overflow: hidden;
}
.contactsec .lftrytlinepart
{
    display: flex;
    flex-wrap: wrap;
}
.leftcontpanel
{
    flex:0 0 40%;
    max-width: 40%;
}
.leftcontpanel h2
{
    font-size: 72px;
    line-height: 1;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 40px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Firefox ke liye */
    background-clip: text;
}
.leftcontpanel p
{
    text-align: left !important;
}
.rightcontpanel
{
    flex:0 0 60%;
    max-width: 60%;
    padding: 0 0 0 50px;
}
.inputfieldwidth
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 25px;
}
.inputfieldwidth:last-child
{
    margin-bottom: 0;
}
.inputfield
{
    width: 100%;
    background: rgba(153,153,153,0.25);
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    color: #25223F;
    font-size: 17px;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    border: 0;
    outline: 0;
}
.textareafield
{
    width: 100%;
    height: 150px;
    background: rgba(153,153,153,0.25);
    line-height: normal;
    padding: 15px 20px;
    color: #25223F;
    font-size: 17px;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    border: 0;
    outline: 0;
}
.submitbut,
.inputfieldwidth input[type="submit"]
{
    background: linear-gradient(90deg, #050ef1 0%, #fd0200 100%);
    background-size: 100% 100%;
    height: 67px;
    width: 254px;
    display: inline-block;
    font-weight: bold;
    font-family: 'Titillium Web', sans-serif;
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    transition: all 0.4s ease;
    text-align: center;
    border: 2px 4px solid transparent;
    border-radius: 8px;
    outline: 0;
    cursor: pointer;
    letter-spacing: 1px;
}
.submitbut:hover,
.inputfieldwidth input[type="submit"]:hover
{
    background-color: transparent !important;
    border: 2px 4px solid ;
    border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1;
    border-image-slice: 1;
    transform: scale(1.05) translateX(5px);

    /* Gradient text effect */
    background: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    color: transparent !important;
}
/*.submitbut
{
    background: #7B65EA;
    line-height: 55px;
    padding: 0 45px;
    display: inline-block;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: all 0.5s ease;
}
.submitbut:hover
{
    background: #fff;
    color: #7B65EA;
}*/
.tablinks
{
    cursor: pointer;
}
.tabcontent, .valuetabcontent
{
    display: none;
}
.footerbg
{
    width: 100%;
    overflow: hidden;
    background: #000;
}
.topftrsec
{
    width: 100%;
    overflow: hidden;
    border-bottom: 1px solid rgba(153,153,153,0.25);
}
.topftrflexpart
{
    display: flex;
    flex-wrap: wrap;
}
.topftrcolpart
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 40px 15px;
    border-left: 1px solid rgba(153,153,153,0.25);
}
.topftrcolpart:last-child
{
    border-right: 1px solid rgba(153,153,153,0.25);
}
.ftrlogo
{
    max-width: 200px;
    margin-bottom: 20px;
}
.ftrusa
{
    margin-top: 20px;
}
.topftrcolpart p
{
    font-size: 16px;
    line-height: 24px;
    color: #999;
    font-weight: normal;
}
.topftrcolpart ul li
{
    font-size: 16px;
    line-height: 40px;
    color: #999;
    font-weight: normal;
    text-transform: uppercase;
}
.topftrcolpart ul li a
{
    color: #999;
}
.topftrcolpart ul li a:hover
{
    color: #fff;
}
.ftrmap
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 20px;
}
.topftrcolpart p
{
    margin-bottom: 10px;
}
.topftrcolpart p:last-child
{
    margin: 0;
}
.topftrcolpart p.social
{
    display: flex;
    flex-wrap: wrap;
    column-gap: 10px;
}
a.ftrsocial
{
    width: 35px;
    height: 35px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}
a.ftrsocial:hover
{
    background: #7B65EA;
}
a.backtotop
{
    background: url(../images/toparrow.png) no-repeat right center;
    background-size: auto 100%;
    font-size: 16px;
    text-transform: uppercase;
    color: #999;
    padding: 0 85px 0 0;
    height: 72px;
    line-height: 72px;
    display: inline-block;
}
.botftrsec
{
    width: 100%;
    overflow: hidden;
    padding: 30px 0;
}
.botftrsec p
{
    font-size: 15px;
    font-weight: normal;
    line-height: 1;
    text-align: center;
    color: #fff;
}
.mobnavsec
{
    display: none;
}
.mobileview
{
    display: none;
}
.sglecolpart img
{
    width: auto !important;
    margin: 0 auto;
}
.innerbannersec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background-position: center top;
    background-repeat: no-repeat;
    background-size: cover;
}
.innerbannercontsec
{
    max-width: 60%;
    overflow: hidden;
}
.innerbannersec h1
{
    text-transform: uppercase;
    color: #fff;
    font-size: 46px;
    line-height: 53px;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 20px 0;
}
.fstservicecontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstservicebg.jpg) no-repeat center top;
    background-size: cover;
}
.fstservicecontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
}
.fstservicecontsec p
{
    text-align: center;
    font-weight: normal;
}
.sndservicecontsec
{
    width:100%;
    overflow: hidden;
    padding: 100px 0;
    background-color: #000;
}
.sndserviceconttabsec
{
    display: flex;
    flex-wrap: wrap;
    justify-content: right;
    margin: 0 0 50px 0;
}
.sndserviceconttabsec ul li
{
    float: left;
    font-weight: 500;
    font-size: 18px;
    line-height: 40px;
    margin: 0 0 0 18px;
}
.sndserviceconttabsec ul li:first-child
{
    margin: 0;
}
.sndserviceconttabsec ul li a
{
    color: #fff;
    border: 1px solid #6E57B0;
    display: block;
    padding: 0 20px;
}
.sndserviceconttabsec ul li a:hover, .sndserviceconttabsec ul li a.active
{
    background-color: #6E57B0;
    color: #fff;
}
.sndservicesectabpart
{
    display: flex;
    flex-wrap: wrap;
}
.leftsndservicepart
{
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 25px 0 0;
}
.lftsndserviceimgpart
{
    position: relative;
}
.lftsndserviceimgpart::after
{
    content: "";
    position: absolute;
    right: 0;
    bottom: 8px;
    width: 0;
    height: 0;
    border-bottom: 50px solid #000;
    border-left: 50px solid transparent;
}
.rightsndservicepart
{
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0 0 25px;
}
.rightsndservicepart h2
{
    color: #A188EB;
    font-size: 33px;
    line-height: 39px;
    font-family: 'Titillium Web';
    font-weight: 600;
    text-transform: capitalize;
    padding: 0 0 18px 0;
}
.rightsndservicepart h3
{
    color: #fff;
    font-size: 40px;
    line-height: 50px;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 18px 0;
}
.rightsndservicepart p
{
    font-weight: normal;
}
a.learnmorebut
{
    width: 212px;
    height: 56px;
    background: url(../images/learn-more.png) no-repeat left top;
    background-size: 100% 100%;
    display: block;
    text-align: center;
    line-height: 56px;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 19px;
    color: #fff;
    text-transform: uppercase;
    margin-top: 20px;
}
a.learnmorebut:hover
{
    color: #000;
}
.notopmargin
{
    margin-top: 0 !important;
}
.fstappcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstappcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
}
.fstappcontsec p
{
    text-align: center;
    font-weight: normal;
}
.fstapppgcontflex
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.sgleindustryappsec
{
    flex:0 0 25%;
    max-width: 25%;
    margin-top: 100px;
    padding: 0 15px;
}
.industryappthumb
{
    width: 100%;
    overflow: hidden;
}
.sgleindustryappsec h3
{
    font-size: 18px;
    line-height: 25px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: 600;
    text-align: center;
    margin-top: 20px;
}
.resourcehubcontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/bg02.png) no-repeat center top;
    background-size: cover;
    padding: 100px 0;
}
.resourcehubcontsec p
{
    font-weight: normal;
    text-align: center;
}
.resourcetabnfiltersec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.resourcesearchsec
{
    max-width: 54.5%;
    flex:0 0 54.5%;
    background: #2A224F;
}
.labeltxt
{
    font-weight: 600;
    font-size: 25px;
    line-height: normal;
    color: #fff;
    font-family: 'Titillium Web';
    line-height: 73px;
    background: #130F2F;
    width: 30%;
    float: left;
    text-align: center;
}
.resoucesearchfield
{
    width: 70%;
    float: left;
    padding: 0 20px;
}
.searchfield
{
    width: 100%;
    border-top: 0;
    border-right: 0;
    border-left: 0;
    border-bottom: 1px solid #FFFFFF;
    color: #fff;
    font-weight: normal;
    font-size: 14px;
    background: transparent;
    outline: 0;
    height: 60px;
    font-family: 'Spline Sans Mono';
}
.resourcefiltersec
{
    max-width: 44.5%;
    flex:0 0 44.5%;
    background: #2A224F;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}
.resourcefiltersec p
{
    font-weight: 600;
    font-size: 25px;
    line-height: normal;
}
.resourcefiltersec p a
{
    color: #fff;
}
.resourcefiltersec p a:hover
{
    color: #7B65EA;
}
.resourceconttabsec
{
    margin: 0 -15px;
    display: flex;
    flex-wrap: wrap;
}
.sgleresourcecontsec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 30px;
}
.resourcecontdatasec
{
    width: 100%;
    height: 100%;
    overflow: hidden;
    border:1px solid #6E57B0;
    position: relative;
    padding: 20px 20px 66px 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6e57b0+100 */
    background: linear-gradient(to bottom, #000000 0%,#6e57b0 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.resourcethumb
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 22px;
}
.sgleresourcecontsec h3
{
    font-size: 22px;
    line-height: 30px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 20px 0;
}
a.whtedownloadusecasebut
{
    background: url(../images/downloadusecasebut.png) no-repeat center top;
    background-size: 100% 100%;
    width: 352px;
    height: 36px;
    display: block;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    right: 0;
    bottom: 0;
    z-index: 1;
    line-height: 36px;
    text-align: center;
    color: #6E57B0;
    font-size: 14px;
    font-family: 'Titillium Web';
    text-transform: uppercase;
    font-weight: bold;
}
.sndapppgcontflex
{
    width: 100%;
    overflow: hidden;
}
.sndapppgcontflex h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
}
.sndapppgflexwrap
{
    display: flex;
    flex-wrap: wrap;
}
.lftflexwrappanel
{
    flex:0 0 25%;
    max-width: 25%;
}
.sndapppgflexwrap ul
{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.sndapppgflexwrap ul li {
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    font-style: normal;
    font-size: 24px;
    line-height: normal;
    background-image: url(../images/horizontal-divider.png), url(../images/horizontal-divider.png);
    background-repeat: no-repeat, no-repeat;
    background-position: center top, center bottom;
    margin: 15px 0;
}
.sndapppgflexwrap ul li a {
    color: #fff;
    padding: 10px 0;
    display: block;
    cursor: pointer;
}
.sndapppgflexwrap ul li a:hover, .sndapppgflexwrap ul li a.active
{
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6159a5+50,000000+100 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.midflexwrappanel
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 20px;
}
.midflexwrapcontsec
{
    border:1px solid rgba(A161,136,235);
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+10,6159a5+50,000000+90 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    padding: 70px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}
.midflexwrapcontsec p
{
    background: url(../images/horizontal-divider.png) no-repeat center bottom;
    padding: 15px 0;
}
.midflexwrapcontsec p:last-child
{
    background-image: none;
}
.rytflexwrappanel
{
    flex:0 0 25%;
    max-width: 25%;
}
.contactpgalignpad
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0 120px 0;
    background: url(../images/bg02.png) no-repeat center top;
    background-size: cover;
}
.contactpgalignpad .contactsec .lftrytlinepart
{
    background-image: none;
    padding: 0;
}
p.contactpgsocial
{
    display: flex;
    flex-wrap: wrap;
    line-height: 40px;
    gap:10px;
}
.mapimgsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
    text-align: center;
}
.fstfacilitycontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstfacilityflexdiv
{
    display: flex;
    flex-wrap: wrap;
}
.lftfacilitysec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 25px 0 0;
}
.lftfacilitysec h2
{
    font-size: 46px;
    line-height: 53px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 30px 0;
}
.rytfacilitysec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 0 0 25px;
}
.fstfacilitysecbtmdiv
{
    display: flex;
    flex-wrap:wrap;
    margin-top: 120px;
}
.fstfacilitysecbtmdiv h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #fff;
    padding: 0 0 40px 0;
}
.fstfacilitysecbtmdiv p
{
    text-align: center;
    font-weight: normal;
    padding: 0 0 40px 0;
}
.sndfacilitycontsec
{
    background: url(../images/btmfacilitybg.jpg) no-repeat center top;
    background-size: cover;
    padding: 100px 0;
    width: 100%;
    overflow: hidden;
}
.sndfacilitycontsec h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #fff;
    padding: 0 0 40px 0;
}
.sndfacilitycontsec p {
    text-align: center;
    font-weight: normal;
}
.facilitycolpart
{
    display: flex;
    flex-wrap: wrap;
    margin: 70px 0 0;
}
.facilitycolpart .sglecolpart
{
    text-align: left;
    padding: 0 25px;
}
.sglecolpart h3
{
    color: #A188EB;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 39px;
    font-family: "Titillium Web";
    letter-spacing: 3px;
}
.sglecolpart p
{
    text-align: left;
    padding: 0;
    font-weight: normal;
}
.thrdfacilitycontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/silitronicsbg.jpg) no-repeat left top;
    background-size: cover;
    padding: 100px 0;
}
.thrdfacilitycontsec h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #25223F;
    padding: 0 0 30px 0;
}
.thrdfacilityflexpart
{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 -15px;
}
.sglefacilitysec
{
    padding: 0 15px;
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    margin-top: 30px;
}
.facilitybdrsec
{
    border: 1px solid #25223F;
    padding: 20px;
    width: 100%;
    height: 100%;
}
.facilityicontitlesec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 5px 0;
}
.facilityiconsec
{
    flex:0 0 20%;
    max-width: 20%;
}
.facilitytitlesec
{
    flex:0 0 80%;
    max-width: 80%;
    padding: 0 0 0 15px;
}
.facilitytitlesec h3
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 24px;
    letter-spacing: 2px;
    line-height: 1.2;
    padding: 0 0 2px 0;
}
.facilitytitlesec h4
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 2px;
    line-height: 1;
}
.sglefacilitysec p
{
    font-weight: normal;
    color: #25223F;
    font-size: 14px;
    line-height: 1.5;
}
.topfacilitysec
{
    width: 100%;
}
.topfacilitysec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.topfacilitysec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.row
{
    margin: 0 -15px;
    display: flex;
    flex-wrap: wrap;
}
.sgletopfacilitysec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 80px;
}
.topfacilitybg
{
    background: url(../images/bg.png) no-repeat center top;
    background-size: 100% 100%;
    width: 486px;
    height: 334px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    overflow: hidden;
}
.sgletopfacilitysec h3
{
    text-align: center;
    font-weight: normal;
    color: #fff;
    padding-top: 30px;
}
.midfacilitysec
{
    width: 100%;
    overflow: hidden;
    margin-top: 150px;
}
.midfacilitysec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.sglemidfacilitysec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 30px;
}
.midfacilitythumb
{
    position: relative;
    margin: 0 0 25px 0;
}
.midfacilitythumb img
{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
a.playvidbut
{
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 62px;
    height: 62px;
    display: block;
    margin: 0 auto;
}
.sglemidfacilitysec p
{
    text-align: center;
    font-weight: normal;
    font-size: 22px;
    line-height: 35px;
    color: #fff;
}
.topabtcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstabtcontsec
{
    width: 100%;
    overflow: hidden;
}
.fstabtcontsec p
{
    text-align: center;
    font-weight: normal;
}
.sndabtcontsec
{
    width: 100%;
    overflow: hidden;
}
.sndabtcontsec .sgletopfacilitysec .topfacilitybg
{
    align-items: start;
    padding: 20px;
    justify-content: space-between;
    flex-direction: column;
}
.abticon
{
    display: flex;
    flex-wrap: wrap;
    justify-content: right;
    width: 100%;
}
.botfacilitypart h4
{
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
    color: #fff;
    font-size: 36px;
    line-height: 1.2;
    padding: 0 0 10px 0;
}
.botfacilitypart p
{
    font-weight: normal;
}
.thrdabtcontsec
{
    width: 100%;
    margin-top: 90px;
}
.thrdabtcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.thrdabtcontsec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sgleteamsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 75px;
}
.teamcontsec
{
    width: 100%;
    height: 100%;
    position: relative;
    background: url(../images/teambg.png) no-repeat left top;
    background-size: 100% 100%;
}
.teamcontsec img
{
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}
.abttitlesec
{
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
    padding: 30px;
    transition: all 0.5s ease;
}
.sgleteamsec h3
{
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 42px;
    text-transform: capitalize;
    padding: 0 0 10px 0;
    line-height: 1.2;
}
.sgleteamsec p
{
    font-weight: normal;
    font-size: 18px;
    font-style: normal;
    line-height: 24px;
    text-align: left;
}
.teamhovercontsec
{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 30px;
    z-index: 1;
    transition: all 0.5s ease;
    opacity: 0;
}
.teamhoveralignpad
{
    width: 100%;
    height: 100%;
    overflow-y: auto;
}
.sgleteamsec:hover .abttitlesec
{
    opacity: 0;
}
.sgleteamsec:hover .teamhovercontsec
{
    opacity: 1;
}
.sgleteamsec:hover .teamcontsec img
{
    opacity: 0;
}
.frthabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.frthabtcontsec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.frthabtcontsec p {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sglefrthabtsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
    margin-top: 50px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}
.sglefrthabtsec div
{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    height: 200px;
}
.sglefrthabtsec p
{
    text-align: center;
    font-weight: normal;
    color: #fff;
}
.fifthabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.fifthabtcontsec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.fifthabtcontsec p {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.fifthabtconttabsec
{
    display: flex;
    flex-wrap: wrap;
    margin-top: 50px;
}
.lftabttabsec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 50px 0 0;
}
.lftabttabsec ul
{
    text-align: center;
    width: 100%;
}
.lftabttabsec ul li
{
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    font-style: normal;
    font-size: 34px;
    line-height: normal;
    background-image: url(../images/horizontal-divider.png), url(../images/horizontal-divider.png);
    background-repeat: no-repeat, no-repeat;
    background-position: center top, center bottom;
    margin: 20px 0;
}
.lftabttabsec ul li:first-child
{
    margin-top: 0;
}
.lftabttabsec ul li a
{
    color: #fff;
    padding: 10px 0;
    display: block;
}
.lftabttabsec ul li a:hover, .lftabttabsec ul li a.active
{
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6159a5+50,000000+100 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.rytabttabsec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 0 0 50px;
}
.rytabttabsec p
{
    text-align: center;
    font-weight: normal;
    padding-top: 20px;
}
.sixthhabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.sixthhabtcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.sixthhabtcontsec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sixthhabtcontsec ul
{
    text-align: center;
    margin-top: 50px;
}
.sixthhabtcontsec ul li
{
    display: inline-block;
    border-left: 1px solid #D9D9D9;
    padding: 0 10px 0 20px;
}
.sixthhabtcontsec ul li:hover, .sixthhabtcontsec ul li a.active
{
    color: #A188EB;
}
.sixthhabtcontsec ul li:first-child
{
    border-left: 0;
}
.tabcontdatasec
{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 50px;
    margin-top: 100px;
}
.sndcontsec .row
{
    align-items: center;
}
.rytsndcontsec h3
{
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-style: normal;
    font-size: 28px;
    line-height: 1.3;
    padding: 0 0 30px 0;
}
.tabviewcontsec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.sgletabviewcontsec
{
    margin-top: 40px;
    max-width: 33.3333333%;
    flex:0 0 33.3333333%;
    padding:  0 15px;
}
.sgletabviewcontsec p
{
    font-size: 14px;
    line-height: 1.5;
    font-weight: normal;
}
.tabviewalignsec
{
    border-top: 1pxc solid transparent;
    border-right: 1px solid #A188EB;
    border-bottom: 1px solid #A188EB;
    border-left: 1px solid transparent;
    padding: 0 10px 10px 0;
    height: 100%;
}
.lftsndcontsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}
.midsndcontsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}
.rytsndcontsec
{
    flex:0 0 50%;
    max-width: 100%;
    padding: 0 15px;
}
.verticalcarouselsec
{
    height: calc(100vh - 100px);
}
.cv-carousel .cv-nav
{
    margin:0 20px 0 0;
}
.cv-carousel .cv-nav div
{
    background: #7b65ea !important;
    text-transform: capitalize;
    color: #fff !important;
    font-size: 20px !important;
    margin: 20px 0 !important;
    font-weight: normal !important;
}


.fixed-getintouch {
    position: fixed;
    
    bottom: 15px;
    right: 15px;
   
    color: white;
    font-family: monospace;
    font-size: 16px;
    font-weight: bold;
    
    border-radius: 50%;
    text-align: center;
    text-decoration: none;
    
    z-index: 9999;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .fixed-getintouch:hover {
    transform: scale(1.1);
    
  }
  .bannersec {
    display: block;
  }

  .bannersec-mobile, .mobindustryleadersec {
    display: none;
  }
  
  /* Hide fixed Get in touch button on mobile and tablet */
  @media (max-width: 991px) {
    .fixed-getintouch {
      display: none !important; /* Hide on mobile and tablet */
    }
  }

  /* Ensure fixed Get in touch button is visible on desktop */
  @media (min-width: 992px) {
    .fixed-getintouch {
      display: block !important; /* Show on desktop */
    }
  }

  /* Override slide-specific hover to maintain consistent gradient border effect */
.bannersec .owl-carousel .item .getintouchbut:hover,
.slide .getintouchbut:hover,
.carousel-item .getintouchbut:hover {
  background-color: transparent !important;
  border: 2px solid transparent;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
  border-image-slice: 1;
  transform: scale(1.05) translateX(5px);

  /* Gradient text effect */
  background: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

/* Ensure button style is consistent across all slides and containers */
.slide .getintouchbut,
.carousel-item .getintouchbut,
.banner .getintouchbut,
.bannersec .getintouchbut {
  background: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) !important;
  color: #fff !important;
  font-weight: bold !important;
  border: 2px solid transparent !important;
}

/* Force transparent background on hover for all contexts */
.slide .getintouchbut:hover,
.carousel-item .getintouchbut:hover,
.banner .getintouchbut:hover,
.bannersec .getintouchbut:hover,
.owl-carousel .getintouchbut:hover,
.item .getintouchbut:hover {
  background-color: transparent !important;
  background-image: none !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
  border-image-slice: 1 !important;
  transform: scale(1.05) translateX(5px) !important;
  text-indent: 15px !important;
  transition: all 0.3s ease !important;
  color: #fff !important;
  box-shadow: 0 8px 20px rgba(0,0,0,0.4) !important;
}

/* Responsive footer for mobile - Updated for 2-column layout */
@media (max-width: 767px) {
  .footerbg .topftrsec, .footerbg .botftrsec {
    padding: 0 10px;
    box-sizing: border-box;
  }
  .footerbg .topftrsec {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 0;
    border: none;
  }
  .footerbg .topftrsec .container {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0;
    margin: 0;
  }
  .footerbg .topftrflexpart {
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
    gap: 0;
  }
  /* Override for 3-column mobile layout */
  .footerbg .topftrcolpart:first-child {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 20px 15px 15px 15px !important;
    border-left: 0 !important;
    border-bottom: 1px solid rgba(153,153,153,0.25) !important;
    text-align: left !important;
  }
  .footerbg .topftrcolpart:nth-child(2) {
    width: 50% !important;
    max-width: 50% !important;
    flex: 0 0 50% !important;
    padding: 15px 10px 15px 15px !important;
    border-left: 0 !important;
  }
  .footerbg .topftrcolpart:nth-child(3) {
    width: 50% !important;
    max-width: 50% !important;
    flex: 0 0 50% !important;
    padding: 15px 15px 15px 10px !important;
    border-left: 1px solid rgba(153,153,153,0.25) !important;
  }
  .footerbg .topftrcolpart:last-child {
    display: none !important;
  }
  .footerbg .topftrsec img, .footerbg .topftrsec .footer-logo {
    margin-bottom: 10px;
    max-width: 120px;
    height: auto;
  }
  .footerbg .topftrsec ul, .footerbg .topftrsec li {
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: left;
  }
  .footerbg .topftrsec ul {
    margin-bottom: 10px;
  }
  .footerbg .topftrsec .footer-address {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
  }
  .footerbg .topftrsec .footer-social {
    margin-bottom: 10px;
  }
  .footerbg .botftrsec {
    text-align: left !important;
    font-size: 12px !important;
    padding: 15px !important;
    border-top: 1px solid rgba(153,153,153,0.25) !important;
  }
  .footerbg .botftrsec p {
    margin: 0 !important;
    text-align: left !important;
  }
  /* Mobile social media icons - Very small */
  .footerbg .ftrsocial {
    margin: 5px 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    height: auto !important;
    min-height: auto !important;
  }
  .footerbg .ftrsocial a {
    display: inline-block !important;
    margin-right: 6px !important;
    padding: 4px !important;
    line-height: 1 !important;
    border: 1px solid rgba(153,153,153,0.4) !important;
    border-radius: 3px !important;
    background: rgba(255,255,255,0.1) !important;
  }
  .footerbg .ftrsocial img {
    width: 14px !important;
    height: 14px !important;
    max-width: 14px !important;
    max-height: 14px !important;
    display: block !important;
  }
  .footerbg .topftrcolpart .social {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-top: 18px !important;
    margin-bottom: 0 !important;
    gap: 18px !important;
  }
  .footerbg .topftrcolpart .ftrsocial {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background: #fff !important;
    margin: 0 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 0 !important;
  }
  .footerbg .topftrcolpart .ftrsocial img {
    width: 22px !important;
    height: 22px !important;
    display: block;
  }
}

p.sbannertext{
    width: 535;
height: 88;
top: 685.5px;
left: 115px;
font-family: Spline Sans Mono;
font-weight: 400;
font-size: 22px;
line-height: 30px;
letter-spacing: 0px;
vertical-align: middle;
color: rgba(255, 255, 255, 1);

}
 .sbbox{
     width: 535;
height: 88;
top: 685.5px;
left: 115px;
 }
.ssimg {
    width: 100%;
    max-width: 600px;
    height: auto;
    margin: 20px auto 0 auto;
    display: block;
}
.slide2text{
    width: 1404;
height: 179;
top: 1385.37px;
left: 258px;

}

.slide2text p{
    font-family: Spline Sans Mono;
font-weight: 400;
font-size: 22px;
line-height: 30px;
letter-spacing: 0px;
text-align: center;
vertical-align: middle;
color: rgba(37, 34, 63, 1);


}



@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Regular.eot');
    src: url('../fonts/SplineSansMono-Regular.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Regular.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Regular.woff') format('woff'),
         url('../fonts/SplineSansMono-Regular.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Regular.svg#SplineSansMono-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Medium.eot');
    src: url('../fonts/SplineSansMono-Medium.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Medium.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Medium.woff') format('woff'),
         url('../fonts/SplineSansMono-Medium.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Medium.svg#SplineSansMono-Medium') format('svg');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Spline Sans Mono';
    src: url('../fonts/SplineSansMono-Bold.eot');
    src: url('../fonts/SplineSansMono-Bold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/SplineSansMono-Bold.woff2') format('woff2'),
         url('../fonts/SplineSansMono-Bold.woff') format('woff'),
         url('../fonts/SplineSansMono-Bold.ttf') format('truetype'),
         url('../fonts/SplineSansMono-Bold.svg#SplineSansMono-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Titillium Web';
    src: url('../fonts/TitilliumWeb-Bold.eot');
    src: url('../fonts/TitilliumWeb-Bold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/TitilliumWeb-Bold.woff2') format('woff2'),
         url('../fonts/TitilliumWeb-Bold.woff') format('woff'),
         url('../fonts/TitilliumWeb-Bold.ttf') format('truetype'),
         url('../fonts/TitilliumWeb-Bold.svg#TitilliumWeb-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Titillium Web';
    src: url('../fonts/TitilliumWeb-SemiBold.eot');
    src: url('../fonts/TitilliumWeb-SemiBold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/TitilliumWeb-SemiBold.woff2') format('woff2'),
         url('../fonts/TitilliumWeb-SemiBold.woff') format('woff'),
         url('../fonts/TitilliumWeb-SemiBold.ttf') format('truetype'),
         url('../fonts/TitilliumWeb-SemiBold.svg#TitilliumWeb-SemiBold') format('svg');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
*
{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
html {
    scroll-behavior: smooth;
  }
h1, h2, h3, h4, h5, h6, p
{
    margin: 0;
    padding: 0;
}
ul, ol, li
{
    margin: 0;
    padding: 0;
    list-style-type: none;
}
a, a:hover, a:focus
{
    text-decoration: none;
    border: 0;
    outline: 0;
}
img
{
    max-width: 100%;
    height: auto;
}
body
{
    font-family: 'Spline Sans Mono';
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
    color: #000;
}
.container
{
    max-width: 1600px;
    padding: 0 15px;
    margin: 0 auto;
}
.headersec
{
    width: 100%;
    overflow: hidden;
    padding: 5px 0;
    background: #000;
}
.headersec.sticky
{
    position: fixed;
    width: 100%;
    z-index: 99;
    left: 0;
    top: 0;
}
.logonnavsec
{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}
.navsec ul li
{
    float: left;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    text-transform:uppercase;
    margin:0 0 0 25px;
}
.navsec ul li:first-child
{
    margin: 0;
}
.navsec ul li a
{
    color: #999999;
}
.navsec ul li a:hover, .navsec ul li a.active
{
    color: #fff;
}
.bannersec
{
    width: 100%;
    height: 976px;
    overflow: hidden;
    position: relative;
}
.bannersec .slidersec, .bannersec .slidersec .owl-stage-outer, .bannersec .slidersec .owl-stage-outer .owl-stage, .bannersec .slidersec .owl-stage-outer .owl-stage .owl-item, .bannersec .slidersec .owl-stage-outer .owl-stage .owl-item .item
{
    height: 100%;
}
.slidersec img, .slidersec video
{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.slidersec .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 20px;
    z-index: 1;
    line-height: 1;
}
.slidersec .owl-dots .owl-dot
{
    margin: 0 5px;
}
.slidersec .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #A188EB;
}
.slidersec .owl-dots .owl-dot.active span
{
    background: #A188EB;
}
.slidecontpart
{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
}
.slidecontpart .container
{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    padding: 50px 15px;
}
.slidecontpart h1
{
    font-size: 46px;
    line-height: 53px;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #fff;
    max-width: 700px;
    text-transform: uppercase;
    letter-spacing: 5px;
}
.slidecontpart p
{
    max-width: 700px;
    color: #fff;
    font-weight: normal;
    padding: 40px 0 0;
    letter-spacing: 5px;
}
a.getintouchbut
{
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#050ef1+0,fd0200+100 */
    background: linear-gradient(to right, #050ef1 0%,#fd0200 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    line-height: 67px;
    width: 254px;
    background-size: 100% 100%;
    display: inline-block;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    margin-top: 35px;
    transition: all 0.5s ease;
    text-align: center;
}
a.getintouchbut:hover
{
    color: #fff;
    /* Transparent background with gradient border */
    background-color: transparent !important;
    border: 2px solid transparent !important;
    border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
    border-image-slice: 1 !important;
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
    transform: scale(1.05) translateX(5px);
    /* Move text to right with animation */
    text-indent: 15px;
    transition: all 0.3s ease;
}

/* Slide-specific hover effects for desktop and mobile - Updated for transparent background */
.slide-1 a.getintouchbut:hover,
.slide-2 a.getintouchbut:hover,
.slide-3 a.getintouchbut:hover,
.slide-4 a.getintouchbut:hover,
.slide-5 a.getintouchbut:hover,
.slide-6 a.getintouchbut:hover
{
    background-color: transparent !important;
    border: 2px solid transparent !important;
    border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
    border-image-slice: 1 !important;
    transform: scale(1.05) translateX(5px) !important;
    text-indent: 15px;
    transition: all 0.3s ease;
}
.fstcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstcontbg.png) no-repeat center top;
    background-size: cover;
}
/*.lftrytlinepart
{
    background-image: url(../images/lftline.png), url(../images/rytline.png);
    background-position: left center, right center;
    background-repeat: no-repeat, no-repeat;
    padding: 0 50px;
}*/
.lftrytlinepart h4
{
    font-size: 70px;
    line-height: 85px;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Firefox ke liye */
    background-clip: text;
}
.fstcontsec p
{
    text-align: center;
}
.fstsecforthcolpart
{
    display: flex;
    flex-wrap: wrap;
    margin: 70px 0;
}
.sglecolpart
{
    flex:0 0 25%;
    max-width: 25%;
    text-align: center;
    background: url(../images/line.png) no-repeat left center;
    background-size: auto 100%;
    display: flex;
    flex-direction: column;
    row-gap: 17px;
    padding: 0 15px;
}
.sglecolpart:first-child
{
    background-image: none;
}
.sglecolpart h2
{
    font-size: 30px;
    font-weight: bold;
    line-height: 35px;
    color: #000;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
}
.fstcontsec h3
{
    max-width:750px;
    margin: 0 auto;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #000;
    font-size: 37px;
    line-height: 45px;
    text-align: center;
    text-transform: capitalize;
}
.sndcontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/sndcontsecbg.png) no-repeat center center;
    background-size: cover;
    padding: 100px 0;
}
.sndcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 75px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.servicecontsec
{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.serviceimgsec
{
    flex:0 0 40%;
    max-width: 40%;
}
.servicwcontentsec
{
    flex:0 0 60%;
    max-width: 60%;
    padding: 0 0 0 50px;
}
.servicwcontentsec h3
{
    padding: 0 0 50px 0;
}
.tabslidersec .owl-nav
{
    margin: 0;
}
.tabslidersec .owl-nav .owl-prev
{
    background: url(../images/toparw.png) no-repeat left top !important;
    background-size: 100% 100%;
    width: 38px;
    height: 45px;
    display: block;
    position: absolute;
    right: -32px;
    top: 0;
    z-index: 1;
    margin: 0;
}
.tabslidersec .owl-nav .owl-next
{
    background: url(../images/btmarw.png) no-repeat left top !important;
    background-size: 100% 100%;
    width: 38px;
    height: 45px;
    display: block;
    position: absolute;
    right: -32px;
    bottom: 0;
    z-index: 1;
    margin: 0;
}
.tabslidersec .owl-nav span
{
    display: none;
}
.tabslidersec .owl-dots
{
    margin: 0 !important;
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 15px;
}
.tabslidersec .owl-dots .owl-dot
{
    display: block;
}
.tabslidersec .owl-dots .owl-dot span
{
    margin: 5px 0;
    width: 15px;
    height: 15px;
    border: 1px solid #000;
    background-color: #fff;
}
.tabslidersec .owl-dots .owl-dot span:hover, .tabslidersec .owl-dots .owl-dot.active span
{
    border: 1px solid #000;
    background-color: #000;
}
.servicedisplayflex
{
    margin: 0;
    width: 100%;
    overflow: hidden;
}
.sgleservicedisplayflex
{
    float: left;
    width: 31.3333333%;
    margin: 4% 2% 0 0;
}
.sgleservicedisplayflex .inner
{
    font-size: 17px;
    letter-spacing: normal;
    line-height: 1.5;
}
.servicecontheight
{
    background: linear-gradient(45deg, #050EF1, #FD0200);
    padding: 2px;
    -webkit-border-bottom-right-radius: 20px;
    -moz-border-radius-bottomright: 20px;
    border-bottom-right-radius: 20px;
    width: 100%;
    height: 100%;
}
.servicecontheight > .inner
{
    padding: 18px;
    background: white;
    -webkit-border-bottom-right-radius: 20px;
    -moz-border-radius-bottomright: 20px;
    border-bottom-right-radius: 20px;
    width: 100%;
    height: 100%;
}
.tabsec
{
    width: 100%;
    overflow: hidden;
}
.tabsec ul li
{
    float: left;
    width: 33.3333333%;
    text-align: center;
    font-size: 36px;
    line-height: normal;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
}
.tabsec ul li a
{
    display: block;
    padding: 75px 15px;
    color: #000;
    background: #dfd4f3;
    transition: all 0.5s ease;
}
.tabsec ul li a:hover, .tabsec ul li a.active
{
    background: #25223F;
    color: #fff;
}
.tabcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
}
.tabcontsec h2
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 42px;
    line-height: 49px;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    padding: 0 0 25px 0;
}
.tabcontsec p
{
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    color: #25223F;
}
.tabcontsec a.getintouchbut
{
    line-height: 46px;
    width: 171px;
    color: #fff;
    font-size: 15px;
    background:url(../images/darkgetintouch.png) no-repeat left top;
    background-size: 100% 100%;
    transition: all 0.5s ease;
}
.tabcontsec a.getintouchbut:hover
{
    color: #6E57B0;
}
.carouselsec
{
    width: 100%;
    overflow: hidden;
    padding-bottom: 100px;
}
.facilitydesignsec .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -40px;
    z-index: 1;
    line-height: 1;
}
.facilitydesignsec .owl-dots .owl-dot
{
    margin: 0 5px;
}
.facilitydesignsec .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #25223F;
}
.facilitydesignsec .owl-dots .owl-dot.active span
{
    background: #25223F;
}
/*.carouselsec .container
{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}*/
.sglecarouselsec
{
    width: 100%;
    background: #6E57B0;
    padding: 18px 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.iconsec
{
    float: left;
    width: 20%;
}
.iconsec img
{
    width: auto !important;
}
.carouselcontsec
{
    float: right;
    width: 80%;
    padding-left: 20px;
}
.thrdcontsec
{
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 100px 0;
}
.customervoicesec
{
    width: 100%;
}
.customervoice .owl-dots
{
    margin:0 !important;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -40px;
    z-index: 1;
    line-height: 1;
}
.customervoice .owl-dots .owl-dot
{
    margin: 0 5px;
}
.customervoice .owl-dots .owl-dot span
{
    margin: 0;
    background: transparent;
    border: 1px solid #A188EB;
}
.customervoice .owl-dots .owl-dot.active span
{
    background: #A188EB;
}
.customervoicesec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.industryleadersec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}
.sgleindustrysec
{
    flex:0 0 14.2%;
    max-width: 14.2%;
    padding: 0 10px;
    flex-wrap: wrap;
}
/* .sgleindustrysec h3
{
    background: linear-gradient(to right, #480bb0 0%, #5c0a9d 100%);
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: normal;
    font-weight: normal;
    padding: 10px 5px;
    min-height: 70px;
} */
/* .sgleindustrysec h3 strong
{
    font-weight: normal;
    display: block;
} */



.onestgrediant { background: linear-gradient(267.62deg, #FD0200 -800.9%, #050EF1 433.34%);}
.twostgrediant { background: linear-gradient(267.62deg, #FD0200 -680.08%, #050EF1 482.9%);}

.threegradient {background: linear-gradient(267.62deg, #FD0200 -564.5%, #050EF1 615.58%);
 }
.fourgradient {background: linear-gradient(267.62deg, #FD0200 -446.3%, #050EF1 720.96%);
 }
.fivegradient {
background: linear-gradient(270deg, #FD0200 -339.36%, #050EF1 879.39%);
 }
.sixgradient {
background: linear-gradient(267.62deg, #FD0200 -207.05%, #050EF1 965.91%);
}
.sevenstgrediant {background: linear-gradient(267.42deg, #FD0200 -86.89%, #050EF1 1083.09%);
}
 
.tech-box {
  

  color: white;
  font-family: 'Titillium Web', sans-serif;
  font-weight: 700;
  font-size: 16.64px;
  line-height: 18.42px;
  letter-spacing: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 95% 100%, 0% 100%);
  box-sizing: border-box;
  padding: 20px;
}


.tech-box p {
  
  font-weight: normal!important;
    font-size: 14px;
}




.sgleindustrysec ul li
{
    text-align: center;
    border-bottom: 1px solid rgba(0,0,0,0.15);
    padding: 20px 0 5px 0;
}
.sgleindustrysec ul li img
{
    max-height: 21px;
}
.sglecustomersec
{
    width: 100%;
    height: 340px;
    display: flex;
    flex-wrap: wrap;
    background: #FEFFFFC9;
    padding: 25px;
    align-items: center;
    justify-content: center;
    position: relative;
}
.sglecustomersec img
{
    width: auto !important;
}
.hovercustomersec
{
    background: url(../images/bg.jpg) no-repeat left top;
    background-size: cover;
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 25px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 5px;
    opacity: 0;
    transition: all 0.5s ease;
    justify-content:left;
}
.sglecustomersec:hover .hovercustomersec
{
    opacity: 1;
}
.hovercustomersec p
{
    font-size: 17px;
    line-height: 26px;
    color: #25223F;
    font-weight: normal;
    text-align: left;
}
.starratingsec
{
    text-align: left;
    padding: 5px 0;
    width: 100%;
    overflow: hidden;
}
.hovercustomersec h3
{
    font-size: 18px;
    font-weight: normal;
    line-height: normal;
    color: #25223F;
    text-align: left;
    width: 100%;
}
.hovercustomersec h4
{
    font-size: 13px;
    font-weight: normal;
    line-height: normal;
    color: #25223F;
    text-align: left;
    width: 100%;
}
.forthcontsec
{
    width: 100vw;
    max-width: 100vw;
    overflow: hidden;
    padding: 100px 0;
    background: linear-gradient(to right, #5b0a9e 0%, #ca0532 100%);
    -webkit-border-top-left-radius: 50px;
    -webkit-border-top-right-radius: 50px;
    -moz-border-radius-topleft: 50px;
    -moz-border-radius-topright: 50px;
    border-top-left-radius: 50px;
    border-top-right-radius: 50px;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-top: 0;
    margin-bottom: 0;
}
.casestudiessec
{
    width: 100%;
    overflow: hidden;
}
.casestudiessec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
}
.casestudycontsec
{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    gap: 60px;
    justify-content: space-between;
}
.sglecasestudycontsec
{
    max-width: 30%;
    flex:0 0 30%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    border: 1px solid #6E57B000;
    height: 310px;
}
.transparentbg
{
    background: url(../images/transparentbg.png) no-repeat left top;
    width: 100%;
    height: 100%;
    background-size: cover;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
}
.casestudycontpart
{
    width: 100%;
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -130px;
    z-index: 1;
    transition: all 1s ease;
    background: #fff;
    padding-top: 10px;
}
.sglecasestudycontsec:hover .casestudycontpart
{
    bottom: 0;
}
.sglecasestudycontsec h3
{
    font-size: 20px;
    line-height: 28px;
    font-family: 'Titillium Web';
    font-weight: bold;
    color: #000;
    padding: 0 20px 10px 20px;
}
.sglecasestudycontsec p
{
    font-size: 15px;
    line-height: 21px;
    font-weight: normal;
    color: #000;
    padding: 0 20px;
}
.sglecasestudycontsec a.downloadusecasebut
{
    background: #fff;
    line-height: 45px;
    padding: 0;
    display: block;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 14px;
    color: #000;
    text-transform: uppercase;
    margin-top: 20px;
    text-align: center;
    transition: all 0.5s ease;
    border-top: 1px solid #000;
}
.sglecasestudycontsec a.downloadusecasebut:hover
{
    background: #000;
    color: #fff;
}
.fifthcontsec
{
    width: 100vw;
    max-width: 100vw;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstcontbg.png) no-repeat center top;
    background-size: cover;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-top: 0;
    margin-bottom: 0;
}
.contactsec
{
    width: 100%;
    overflow: hidden;
}
.contactsec .lftrytlinepart
{
    display: flex;
    flex-wrap: wrap;
}
.leftcontpanel
{
    flex:0 0 40%;
    max-width: 40%;
}
.leftcontpanel h2
{
    font-size: 72px;
    line-height: 1;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 40px 0;
    background: linear-gradient(90deg, #050EF1, #FD0200);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Firefox ke liye */
    background-clip: text;
}
.leftcontpanel p
{
    text-align: left !important;
}
.rightcontpanel
{
    flex:0 0 60%;
    max-width: 60%;
    padding: 0 0 0 50px;
}
.inputfieldwidth
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 25px;
}
.inputfieldwidth:last-child
{
    margin-bottom: 0;
}
.inputfield
{
    width: 100%;
    background: rgba(153,153,153,0.25);
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    color: #25223F;
    font-size: 17px;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    border: 0;
    outline: 0;
}
.textareafield
{
    width: 100%;
    height: 150px;
    background: rgba(153,153,153,0.25);
    line-height: normal;
    padding: 15px 20px;
    color: #25223F;
    font-size: 17px;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    border: 0;
    outline: 0;
}
.submitbut
{
    background: linear-gradient(to right, #050ef1 0%, #fd0200 100%);
    background-size: 100% 100%;
    height: 67px;
    width: 254px;
    display: inline-block;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    transition: all 0.5s ease;
    text-align: center;
    border: 0;
    outline: 0;
    cursor: pointer;
}
.submitbut:hover
{
    color: #000;
}
/*.submitbut
{
    background: #7B65EA;
    line-height: 55px;
    padding: 0 45px;
    display: inline-block;
    font-weight: normal;
    font-family: 'Spline Sans Mono';
    font-size: 22px;
    color: #fff;
    text-transform: uppercase;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: all 0.5s ease;
}
.submitbut:hover
{
    background: #fff;
    color: #7B65EA;
}*/
.tablinks
{
    cursor: pointer;
}
.tabcontent, .valuetabcontent
{
    display: none;
}
.footerbg
{
    width: 100%;
    overflow: hidden;
    background: #000;
}
.topftrsec
{
    width: 100%;
    overflow: hidden;
    border-bottom: 1px solid rgba(153,153,153,0.25);
}
.topftrflexpart
{
    display: flex;
    flex-wrap: wrap;
}
.topftrcolpart
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 40px 15px;
    border-left: 1px solid rgba(153,153,153,0.25);
}
.topftrcolpart:last-child
{
    border-right: 1px solid rgba(153,153,153,0.25);
}
.ftrlogo
{
    max-width: 200px;
    margin-bottom: 20px;
}
.ftrusa
{
    margin-top: 20px;
}
.topftrcolpart p
{
    font-size: 16px;
    line-height: 24px;
    color: #999;
    font-weight: normal;
}
.topftrcolpart ul li
{
    font-size: 16px;
    line-height: 40px;
    color: #999;
    font-weight: normal;
    text-transform: uppercase;
}
.topftrcolpart ul li a
{
    color: #999;
}
.topftrcolpart ul li a:hover
{
    color: #fff;
}
.ftrmap
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 20px;
}
.topftrcolpart p
{
    margin-bottom: 10px;
}
.topftrcolpart p:last-child
{
    margin: 0;
}
.topftrcolpart p.social
{
    display: flex;
    flex-wrap: wrap;
    column-gap: 10px;
}
a.ftrsocial
{
    width: 35px;
    height: 35px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}
a.ftrsocial:hover
{
    background: #7B65EA;
}
a.backtotop
{
    background: url(../images/toparrow.png) no-repeat right center;
    background-size: auto 100%;
    font-size: 16px;
    text-transform: uppercase;
    color: #999;
    padding: 0 85px 0 0;
    height: 72px;
    line-height: 72px;
    display: inline-block;
}
.botftrsec
{
    width: 100%;
    overflow: hidden;
    padding: 30px 0;
}
.botftrsec p
{
    font-size: 15px;
    font-weight: normal;
    line-height: 1;
    text-align: center;
    color: #fff;
}
.mobnavsec
{
    display: none;
}
.mobileview
{
    display: none;
}
.sglecolpart img
{
    width: auto !important;
    margin: 0 auto;
}
.innerbannersec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background-position: center top;
    background-repeat: no-repeat;
    background-size: cover;
}
.innerbannercontsec
{
    max-width: 60%;
    overflow: hidden;
}
.innerbannersec h1
{
    text-transform: uppercase;
    color: #fff;
    font-size: 46px;
    line-height: 53px;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 20px 0;
}
.fstservicecontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstservicebg.jpg) no-repeat center top;
    background-size: cover;
}
.fstservicecontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
}
.fstservicecontsec p
{
    text-align: center;
    font-weight: normal;
}
.sndservicecontsec
{
    width:100%;
    overflow: hidden;
    padding: 100px 0;
    background-color: #000;
}
.sndserviceconttabsec
{
    display: flex;
    flex-wrap: wrap;
    justify-content: right;
    margin: 0 0 50px 0;
}
.sndserviceconttabsec ul li
{
    float: left;
    font-weight: 500;
    font-size: 18px;
    line-height: 40px;
    margin: 0 0 0 18px;
}
.sndserviceconttabsec ul li:first-child
{
    margin: 0;
}
.sndserviceconttabsec ul li a
{
    color: #fff;
    border: 1px solid #6E57B0;
    display: block;
    padding: 0 20px;
}
.sndserviceconttabsec ul li a:hover, .sndserviceconttabsec ul li a.active
{
    background-color: #6E57B0;
    color: #fff;
}
.sndservicesectabpart
{
    display: flex;
    flex-wrap: wrap;
}
.leftsndservicepart
{
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 25px 0 0;
}
.lftsndserviceimgpart
{
    position: relative;
}
.lftsndserviceimgpart::after
{
    content: "";
    position: absolute;
    right: 0;
    bottom: 8px;
    width: 0;
    height: 0;
    border-bottom: 50px solid #000;
    border-left: 50px solid transparent;
}
.rightsndservicepart
{
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0 0 25px;
}
.rightsndservicepart h2
{
    color: #A188EB;
    font-size: 33px;
    line-height: 39px;
    font-family: 'Titillium Web';
    font-weight: 600;
    text-transform: capitalize;
    padding: 0 0 18px 0;
}
.rightsndservicepart h3
{
    color: #fff;
    font-size: 40px;
    line-height: 50px;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 18px 0;
}
.rightsndservicepart p
{
    font-weight: normal;
}
a.learnmorebut
{
    width: 212px;
    height: 56px;
    background: url(../images/learn-more.png) no-repeat left top;
    background-size: 100% 100%;
    display: block;
    text-align: center;
    line-height: 56px;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 19px;
    color: #fff;
    text-transform: uppercase;
    margin-top: 20px;
}
a.learnmorebut:hover
{
    color: #000;
}
.notopmargin
{
    margin-top: 0 !important;
}
.fstappcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstappcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 50px 0;
}
.fstappcontsec p
{
    text-align: center;
    font-weight: normal;
}
.fstapppgcontflex
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.sgleindustryappsec
{
    flex:0 0 25%;
    max-width: 25%;
    margin-top: 100px;
    padding: 0 15px;
}
.industryappthumb
{
    width: 100%;
    overflow: hidden;
}
.sgleindustryappsec h3
{
    font-size: 18px;
    line-height: 25px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: 600;
    text-align: center;
    margin-top: 20px;
}
.resourcehubcontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/bg02.png) no-repeat center top;
    background-size: cover;
    padding: 100px 0;
}
.resourcehubcontsec p
{
    font-weight: normal;
    text-align: center;
}
.resourcetabnfiltersec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.resourcesearchsec
{
    max-width: 54.5%;
    flex:0 0 54.5%;
    background: #2A224F;
}
.labeltxt
{
    font-weight: 600;
    font-size: 25px;
    line-height: normal;
    color: #fff;
    font-family: 'Titillium Web';
    line-height: 73px;
    background: #130F2F;
    width: 30%;
    float: left;
    text-align: center;
}
.resoucesearchfield
{
    width: 70%;
    float: left;
    padding: 0 20px;
}
.searchfield
{
    width: 100%;
    border-top: 0;
    border-right: 0;
    border-left: 0;
    border-bottom: 1px solid #FFFFFF;
    color: #fff;
    font-weight: normal;
    font-size: 14px;
    background: transparent;
    outline: 0;
    height: 60px;
    font-family: 'Spline Sans Mono';
}
.resourcefiltersec
{
    max-width: 44.5%;
    flex:0 0 44.5%;
    background: #2A224F;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}
.resourcefiltersec p
{
    font-weight: 600;
    font-size: 25px;
    line-height: normal;
}
.resourcefiltersec p a
{
    color: #fff;
}
.resourcefiltersec p a:hover
{
    color: #7B65EA;
}
.resourceconttabsec
{
    margin: 0 -15px;
    display: flex;
    flex-wrap: wrap;
}
.sgleresourcecontsec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 30px;
}
.resourcecontdatasec
{
    width: 100%;
    height: 100%;
    overflow: hidden;
    border:1px solid #6E57B0;
    position: relative;
    padding: 20px 20px 66px 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6e57b0+100 */
    background: linear-gradient(to bottom, #000000 0%,#6e57b0 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.resourcethumb
{
    width: 100%;
    overflow: hidden;
    margin-bottom: 22px;
}
.sgleresourcecontsec h3
{
    font-size: 22px;
    line-height: 30px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 20px 0;
}
a.whtedownloadusecasebut
{
    background: url(../images/downloadusecasebut.png) no-repeat center top;
    background-size: 100% 100%;
    width: 352px;
    height: 36px;
    display: block;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    right: 0;
    bottom: 0;
    z-index: 1;
    line-height: 36px;
    text-align: center;
    color: #6E57B0;
    font-size: 14px;
    font-family: 'Titillium Web';
    text-transform: uppercase;
    font-weight: bold;
}
.sndapppgcontflex
{
    width: 100%;
    overflow: hidden;
}
.sndapppgcontflex h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 80px 0;
}
.sndapppgflexwrap
{
    display: flex;
    flex-wrap: wrap;
}
.lftflexwrappanel
{
    flex:0 0 25%;
    max-width: 25%;
}
.sndapppgflexwrap ul
{
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.sndapppgflexwrap ul li {
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    font-style: normal;
    font-size: 24px;
    line-height: normal;
    background-image: url(../images/horizontal-divider.png), url(../images/horizontal-divider.png);
    background-repeat: no-repeat, no-repeat;
    background-position: center top, center bottom;
    margin: 15px 0;
}
.sndapppgflexwrap ul li a {
    color: #fff;
    padding: 10px 0;
    display: block;
    cursor: pointer;
}
.sndapppgflexwrap ul li a:hover, .sndapppgflexwrap ul li a.active
{
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6159a5+50,000000+100 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.midflexwrappanel
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 20px;
}
.midflexwrapcontsec
{
    border:1px solid rgba(A161,136,235);
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+10,6159a5+50,000000+90 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    padding: 70px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}
.midflexwrapcontsec p
{
    background: url(../images/horizontal-divider.png) no-repeat center bottom;
    padding: 15px 0;
}
.midflexwrapcontsec p:last-child
{
    background-image: none;
}
.rytflexwrappanel
{
    flex:0 0 25%;
    max-width: 25%;
}
.contactpgalignpad
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0 120px 0;
    background: url(../images/bg02.png) no-repeat center top;
    background-size: cover;
}
.contactpgalignpad .contactsec .lftrytlinepart
{
    background-image: none;
    padding: 0;
}
p.contactpgsocial
{
    display: flex;
    flex-wrap: wrap;
    line-height: 40px;
    gap:10px;
}
.mapimgsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
    text-align: center;
}
.fstfacilitycontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstfacilityflexdiv
{
    display: flex;
    flex-wrap: wrap;
}
.lftfacilitysec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 25px 0 0;
}
.lftfacilitysec h2
{
    font-size: 46px;
    line-height: 53px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    padding: 0 0 30px 0;
}
.rytfacilitysec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 0 0 25px;
}
.fstfacilitysecbtmdiv
{
    display: flex;
    flex-wrap:wrap;
    margin-top: 120px;
}
.fstfacilitysecbtmdiv h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #fff;
    padding: 0 0 40px 0;
}
.fstfacilitysecbtmdiv p
{
    text-align: center;
    font-weight: normal;
    padding: 0 0 40px 0;
}
.sndfacilitycontsec
{
    background: url(../images/btmfacilitybg.jpg) no-repeat center top;
    background-size: cover;
    padding: 100px 0;
    width: 100%;
    overflow: hidden;
}
.sndfacilitycontsec h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #fff;
    padding: 0 0 40px 0;
}
.sndfacilitycontsec p {
    text-align: center;
    font-weight: normal;
}
.facilitycolpart
{
    display: flex;
    flex-wrap: wrap;
    margin: 70px 0 0;
}
.facilitycolpart .sglecolpart
{
    text-align: left;
    padding: 0 25px;
}
.sglecolpart h3
{
    color: #A188EB;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 39px;
    font-family: "Titillium Web";
    letter-spacing: 3px;
}
.sglecolpart p
{
    text-align: left;
    padding: 0;
    font-weight: normal;
}
.thrdfacilitycontsec
{
    width: 100%;
    overflow: hidden;
    background: url(../images/silitronicsbg.jpg) no-repeat left top;
    background-size: cover;
    padding: 100px 0;
}
.thrdfacilitycontsec h2
{
    text-align: center;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 72px;
    line-height: 85px;
    color: #25223F;
    padding: 0 0 30px 0;
}
.thrdfacilityflexpart
{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 -15px;
}
.sglefacilitysec
{
    padding: 0 15px;
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    margin-top: 30px;
}
.facilitybdrsec
{
    border: 1px solid #25223F;
    padding: 20px;
    width: 100%;
    height: 100%;
}
.facilityicontitlesec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 5px 0;
}
.facilityiconsec
{
    flex:0 0 20%;
    max-width: 20%;
}
.facilitytitlesec
{
    flex:0 0 80%;
    max-width: 80%;
    padding: 0 0 0 15px;
}
.facilitytitlesec h3
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 24px;
    letter-spacing: 2px;
    line-height: 1.2;
    padding: 0 0 2px 0;
}
.facilitytitlesec h4
{
    color: #25223F;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 2px;
    line-height: 1;
}
.sglefacilitysec p
{
    font-weight: normal;
    color: #25223F;
    font-size: 14px;
    line-height: 1.5;
}
.topfacilitysec
{
    width: 100%;
}
.topfacilitysec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.topfacilitysec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.row
{
    margin: 0 -15px;
    display: flex;
    flex-wrap: wrap;
}
.sgletopfacilitysec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 80px;
}
.topfacilitybg
{
    background: url(../images/bg.png) no-repeat center top;
    background-size: 100% 100%;
    width: 486px;
    height: 334px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    overflow: hidden;
}
.sgletopfacilitysec h3
{
    text-align: center;
    font-weight: normal;
    color: #fff;
    padding-top: 30px;
}
.midfacilitysec
{
    width: 100%;
    overflow: hidden;
    margin-top: 150px;
}
.midfacilitysec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.sglemidfacilitysec
{
    flex:0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 15px;
    margin-top: 30px;
}
.midfacilitythumb
{
    position: relative;
    margin: 0 0 25px 0;
}
.midfacilitythumb img
{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
a.playvidbut
{
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 62px;
    height: 62px;
    display: block;
    margin: 0 auto;
}
.sglemidfacilitysec p
{
    text-align: center;
    font-weight: normal;
    font-size: 22px;
    line-height: 35px;
    color: #fff;
}
.topabtcontsec
{
    width: 100%;
    overflow: hidden;
    padding: 100px 0 0;
    background: url(../images/fstapppgsecbg.jpg) no-repeat center center;
    background-size: cover;
}
.fstabtcontsec
{
    width: 100%;
    overflow: hidden;
}
.fstabtcontsec p
{
    text-align: center;
    font-weight: normal;
}
.sndabtcontsec
{
    width: 100%;
    overflow: hidden;
}
.sndabtcontsec .sgletopfacilitysec .topfacilitybg
{
    align-items: start;
    padding: 20px;
    justify-content: space-between;
    flex-direction: column;
}
.abticon
{
    display: flex;
    flex-wrap: wrap;
    justify-content: right;
    width: 100%;
}
.botfacilitypart h4
{
    font-family: 'Titillium Web';
    font-weight: bold;
    text-transform: uppercase;
    color: #fff;
    font-size: 36px;
    line-height: 1.2;
    padding: 0 0 10px 0;
}
.botfacilitypart p
{
    font-weight: normal;
}
.thrdabtcontsec
{
    width: 100%;
    margin-top: 90px;
}
.thrdabtcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.thrdabtcontsec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sgleteamsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 75px;
}
.teamcontsec
{
    width: 100%;
    height: 100%;
    position: relative;
    background: url(../images/teambg.png) no-repeat left top;
    background-size: 100% 100%;
}
.teamcontsec img
{
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}
.abttitlesec
{
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
    padding: 30px;
    transition: all 0.5s ease;
}
.sgleteamsec h3
{
    font-family: 'Titillium Web';
    font-weight: bold;
    font-size: 42px;
    text-transform: capitalize;
    padding: 0 0 10px 0;
    line-height: 1.2;
}
.sgleteamsec p
{
    font-weight: normal;
    font-size: 18px;
    font-style: normal;
    line-height: 24px;
    text-align: left;
}
.teamhovercontsec
{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 30px;
    z-index: 1;
    transition: all 0.5s ease;
    opacity: 0;
}
.teamhoveralignpad
{
    width: 100%;
    height: 100%;
    overflow-y: auto;
}
.sgleteamsec:hover .abttitlesec
{
    opacity: 0;
}
.sgleteamsec:hover .teamhovercontsec
{
    opacity: 1;
}
.sgleteamsec:hover .teamcontsec img
{
    opacity: 0;
}
.frthabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.frthabtcontsec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.frthabtcontsec p {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sglefrthabtsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
    margin-top: 50px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
}
.sglefrthabtsec div
{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    height: 200px;
}
.sglefrthabtsec p
{
    text-align: center;
    font-weight: normal;
    color: #fff;
}
.fifthabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.fifthabtcontsec h2 {
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.fifthabtcontsec p {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.fifthabtconttabsec
{
    display: flex;
    flex-wrap: wrap;
    margin-top: 50px;
}
.lftabttabsec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 50px 0 0;
}
.lftabttabsec ul
{
    text-align: center;
    width: 100%;
}
.lftabttabsec ul li
{
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    font-style: normal;
    font-size: 34px;
    line-height: normal;
    background-image: url(../images/horizontal-divider.png), url(../images/horizontal-divider.png);
    background-repeat: no-repeat, no-repeat;
    background-position: center top, center bottom;
    margin: 20px 0;
}
.lftabttabsec ul li:first-child
{
    margin-top: 0;
}
.lftabttabsec ul li a
{
    color: #fff;
    padding: 10px 0;
    display: block;
}
.lftabttabsec ul li a:hover, .lftabttabsec ul li a.active
{
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,6159a5+50,000000+100 */
    background: linear-gradient(to right, #000000 0%,#6159a5 50%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.rytabttabsec
{
    flex:0 0 50%;
    max-width: 50%;
    padding: 0 0 0 50px;
}
.rytabttabsec p
{
    text-align: center;
    font-weight: normal;
    padding-top: 20px;
}
.sixthhabtcontsec
{
    width: 100%;
    overflow: hidden;
    margin-top: 100px;
}
.sixthhabtcontsec h2
{
    font-size: 70px;
    line-height: 85px;
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    text-align: center;
    padding: 0 0 30px 0;
}
.sixthhabtcontsec p
{
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    color: #fff;
}
.sixthhabtcontsec ul
{
    text-align: center;
    margin-top: 50px;
}
.sixthhabtcontsec ul li
{
    display: inline-block;
    border-left: 1px solid #D9D9D9;
    padding: 0 10px 0 20px;
}
.sixthhabtcontsec ul li:hover, .sixthhabtcontsec ul li a.active
{
    color: #A188EB;
}
.sixthhabtcontsec ul li:first-child
{
    border-left: 0;
}
.tabcontdatasec
{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 50px;
    margin-top: 100px;
}
.sndcontsec .row
{
    align-items: center;
}
.rytsndcontsec h3
{
    color: #fff;
    font-family: 'Titillium Web';
    font-weight: bold;
    font-style: normal;
    font-size: 28px;
    line-height: 1.3;
    padding: 0 0 30px 0;
}
.tabviewcontsec
{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.sgletabviewcontsec
{
    margin-top: 40px;
    max-width: 33.3333333%;
    flex:0 0 33.3333333%;
    padding:  0 15px;
}
.sgletabviewcontsec p
{
    font-size: 14px;
    line-height: 1.5;
    font-weight: normal;
}
.tabviewalignsec
{
    border-top: 1pxc solid transparent;
    border-right: 1px solid #A188EB;
    border-bottom: 1px solid #A188EB;
    border-left: 1px solid transparent;
    padding: 0 10px 10px 0;
    height: 100%;
}
.lftsndcontsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}
.midsndcontsec
{
    flex:0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}
.rytsndcontsec
{
    flex:0 0 50%;
    max-width: 100%;
    padding: 0 15px;
}
.verticalcarouselsec
{
    height: calc(100vh - 100px);
}
.cv-carousel .cv-nav
{
    margin:0 20px 0 0;
}
.cv-carousel .cv-nav div
{
    background: #7b65ea !important;
    text-transform: capitalize;
    color: #fff !important;
    font-size: 20px !important;
    margin: 20px 0 !important;
    font-weight: normal !important;
}


.fixed-getintouch {
    position: fixed;
    
    bottom: 15px;
    right: 15px;
   
    color: white;
    font-family: monospace;
    font-size: 16px;
    font-weight: bold;
    
    border-radius: 50%;
    text-align: center;
    text-decoration: none;
    
    z-index: 9999;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .fixed-getintouch:hover {
    transform: scale(1.1);
    
  }
  .bannersec {
    display: block;
  }

  .bannersec-mobile, .mobindustryleadersec {
    display: none;
  }
  
  /* Hide fixed Get in touch button on mobile and tablet */
  @media (max-width: 991px) {
    .fixed-getintouch {
      display: none !important; /* Hide on mobile and tablet */
    }
  }

  /* Ensure fixed Get in touch button is visible on desktop */
  @media (min-width: 992px) {
    .fixed-getintouch {
      display: block !important; /* Show on desktop */
    }
  }

  /* First banner custom hover background - transparent with gradient border */
.bannersec .owl-carousel .item:first-child .getintouchbut:hover {
  background-color: transparent !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
  border-image-slice: 1 !important;
}

/* Second banner custom hover background - transparent with gradient border */
.bannersec .owl-carousel .item:nth-child(2) .getintouchbut:hover {
  background-color: transparent !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
  border-image-slice: 1 !important;
}

/* Universal Get in Touch button hover effect - Final override */
a.getintouchbut:hover,
.getintouchbut:hover,
.bannersec a.getintouchbut:hover,
.bannersec .getintouchbut:hover,
.owl-carousel a.getintouchbut:hover,
.owl-carousel .getintouchbut:hover,
.item a.getintouchbut:hover,
.item .getintouchbut:hover {
  background-color: transparent !important;
  background-image: none !important;
  background: transparent !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(90deg, #050ef1 0%, #fd0200 100%) 1 !important;
  border-image-slice: 1 !important;
  transform: scale(1.05) translateX(5px) !important;
  text-indent: 15px !important;
  transition: all 0.3s ease !important;
  color: #fff !important;
  box-shadow: 0 8px 20px rgba(0,0,0,0.4) !important;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
  letter-spacing: 1px !important;
}

/* Responsive footer for mobile - Updated for 2-column layout */
@media (max-width: 767px) {
  .footerbg .topftrsec, .footerbg .botftrsec {
    padding: 0 10px;
    box-sizing: border-box;
  }
  .footerbg .topftrsec {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 0;
    border: none;
  }
  .footerbg .topftrsec .container {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0;
    margin: 0;
  }
  .footerbg .topftrflexpart {
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
    gap: 0;
  }
  /* Override for 3-column mobile layout */
  .footerbg .topftrcolpart:first-child {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 20px 15px 15px 15px !important;
    border-left: 0 !important;
    border-bottom: 1px solid rgba(153,153,153,0.25) !important;
    text-align: left !important;
  }
  .footerbg .topftrcolpart:nth-child(2) {
    width: 50% !important;
    max-width: 50% !important;
    flex: 0 0 50% !important;
    padding: 15px 10px 15px 15px !important;
    border-left: 0 !important;
  }
  .footerbg .topftrcolpart:nth-child(3) {
    width: 50% !important;
    max-width: 50% !important;
    flex: 0 0 50% !important;
    padding: 15px 15px 15px 10px !important;
    border-left: 1px solid rgba(153,153,153,0.25) !important;
  }
  .footerbg .topftrcolpart:last-child {
    display: none !important;
  }
  .footerbg .topftrsec img, .footerbg .topftrsec .footer-logo {
    margin-bottom: 10px;
    max-width: 120px;
    height: auto;
  }
  .footerbg .topftrsec ul, .footerbg .topftrsec li {
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: left;
  }
  .footerbg .topftrsec ul {
    margin-bottom: 10px;
  }
  .footerbg .topftrsec .footer-address {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
  }
  .footerbg .topftrsec .footer-social {
    margin-bottom: 10px;
  }
  .footerbg .botftrsec {
    text-align: left !important;
    font-size: 12px !important;
    padding: 15px !important;
    border-top: 1px solid rgba(153,153,153,0.25) !important;
  }
  .footerbg .botftrsec p {
    margin: 0 !important;
    text-align: left !important;
  }
  /* Mobile social media icons - Very small */
  .footerbg .ftrsocial {
    margin: 5px 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    height: auto !important;
    min-height: auto !important;
  }
  .footerbg .ftrsocial a {
    display: inline-block !important;
    margin-right: 6px !important;
    padding: 4px !important;
    line-height: 1 !important;
    border: 1px solid rgba(153,153,153,0.4) !important;
    border-radius: 3px !important;
    background: rgba(255,255,255,0.1) !important;
  }
  .footerbg .ftrsocial img {
    width: 14px !important;
    height: 14px !important;
    max-width: 14px !important;
    max-height: 14px !important;
    display: block !important;
  }
}

/* services-section */
.services-section {
    /* margin: 80px 0 60px 0; */
    padding: 60px 0;
    width: 100vw;
    max-width: 100vw;
    background: linear-gradient(135deg, #E8E3FF 0%, #F3F0FF 50%, #E8E3FF 100%);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.1);
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-top: 0;
    margin-bottom: 0;
}

.services-section .container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
}
/* Style the tab */
.tab {
  overflow: hidden;
}
.tablinks-active {
    display: block;
}
.tabcontent div img {
    width: 100%;
}
/* Style the buttons inside the tab */
.tab button {
  background-color: inherit;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  transition: 0.3s;
  font-size: 17px;
}

/* Change background color of buttons on hover */
.tab button:hover {
  background-color: #ddd;
}

/* Create an active/current tablink class */
.tab button.active {
  background-color: #ccc;
}

/* Style the tab content */
.tabcontent {
  display: none;
  padding: 6px 12px;
  border-top: none;
}

.tabcontent-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 32px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Mobile responsive for tabcontent-container */
@media (max-width: 768px) {
    .services-section {
        margin-top: 0;
        margin-bottom: 0;
        padding: 40px 0;
        width: 100vw;
        max-width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
    }

    .services-section .container {
        padding: 0 15px;
        max-width: 100%;
        width: 100%;
        margin: 0 auto;
    }

    .tab {
        gap: 10px;
        margin-bottom: 30px;
        padding: 15px 0;
    }

    .tablinks {
        padding: 10px 16px !important;
        font-size: 14px;
        flex: 1;
        min-width: calc(50% - 5px);
        text-align: center;
    }

    .tabcontent-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 15px;
    }

    .ssimg {
        max-width: 100%;
        margin-top: 15px;
    }
}
/* Style the tab */
.tab {
  overflow: hidden;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 40px;
  padding: 20px 0;
}
.tablinks {
    border: 2px solid #8B7BB8 !important;
    padding: 12px 24px !important;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: #F5F3FF;
    color: #6B46C1;
    font-size: 16px;
    min-width: 140px;
    text-align: center;
}
.tablinks:hover {
    background: #8B5CF6 !important;
    color: #FFFFFF !important;
    border: 2px solid #8B5CF6 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}
/* Style the buttons inside the tab */
.tab button {
  background-color: inherit;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  transition: 0.3s;
  font-size: 17px;
}

/* Change background color of buttons on hover */
.tab button:hover {
  background: #8B5CF6 !important;
  color: #FFFFFF !important;
  border: 2px solid #8B5CF6 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* Create an active/current tablink class */
.tab button.active, .tablinks-active {
  background: #8B5CF6 !important;
  color: #FFFFFF !important;
  border: 2px solid #8B5CF6 !important;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}
.tab-btn {
    border: none;
    background: none;
    cursor: pointer;
    color: #fff;
    background: linear-gradient(to right, #050ef1 0%, #fd0200 100%);;
    padding: 10px;
}

.bannersec-mobile .container{
    top:65% !important
}


