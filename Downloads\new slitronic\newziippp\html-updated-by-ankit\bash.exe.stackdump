Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE9B670000 ntdll.dll
7FFE99950000 KERNEL32.DLL
7FFE98D90000 KERNELBASE.dll
7FFE99700000 USER32.dll
7FFE992D0000 win32u.dll
7FFE9B1D0000 GDI32.dll
7FFE991A0000 gdi32full.dll
7FFE988A0000 msvcp_win.dll
7FFE98C70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE9AA60000 advapi32.dll
7FFE99A20000 msvcrt.dll
7FFE99F60000 sechost.dll
7FFE99170000 bcrypt.dll
7FFE99360000 RPCRT4.dll
7FFE97E70000 CRYPTBASE.DLL
7FFE98A00000 bcryptPrimitives.dll
7FFE9AB20000 IMM32.DLL
