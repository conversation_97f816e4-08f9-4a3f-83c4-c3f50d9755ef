<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Fri May  5 14:19:03 2017
 By 
Copyright (c) 2009-2011 by Accademia di Belle Arti di Urbino and students of MA course of Visual design. Some rights reserved.
</metadata>
<defs>
<font id="TitilliumWeb-SemiBold" horiz-adv-x="560" >
  <font-face 
    font-family="Titillium Web SemiBold"
    font-weight="600"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 7 0 0 0 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="500"
    cap-height="685"
    bbox="-224 -300 1062 1101"
    underline-thickness="30"
    underline-position="-110"
    unicode-range="U+0020-F6C3"
  />
<missing-glyph horiz-adv-x="235" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="235" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="220" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="266" 
d="M74 0v140h117v-140h-117zM85 241l-10 444h116l-10 -444h-96z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="395" 
d="M329 453h-95l-4 232h105zM160 453h-96l-4 232h106z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M540 160h-102v-160h-95v160h-126v-160h-95v160h-102v91h102v152h-102v91h102v169h95v-169h126v169h95v-169h102v-91h-102v-152h102v-91zM343 251v152h-126v-152h126z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M503 196q0 -104 -59.5 -155.5t-161.5 -51.5h-8l-14 -116q-64 3 -64 7l14 113q-64 6 -124 18l-20 4l11 86q79 -11 145 -15l27 207q-104 28 -147 69t-43 129t58.5 134t161.5 46h18l17 134h64l-18 -139l128 -18l-9 -86q-71 8 -130 12l-25 -193q101 -29 140 -67t39 -118z
M166 491q0 -37 20.5 -56t75.5 -37l23 179q-119 -2 -119 -86zM395 190q0 34 -18.5 53t-65.5 33l-25 -192q109 4 109 106z" />
    <glyph glyph-name="percent" unicode="%" 
d="M138 -6l222 697l62 -21l-222 -696zM20 529q0 142 115.5 142t115.5 -142q0 -72 -30 -108t-85.5 -36t-85.5 36t-30 108zM102 529q0 -40 7.5 -58t26.5 -18t26.5 18t7.5 58t-7.5 57.5t-26.5 17.5t-26.5 -17.5t-7.5 -57.5zM309 133q0 142 115.5 142t115.5 -142q0 -72 -30 -108
t-85.5 -36t-85.5 36t-30 108zM397.5 189.5q-7.5 -17.5 -7.5 -57t7.5 -58t26.5 -18.5t26.5 18t7.5 58.5t-7 57.5t-26.5 17t-27 -17.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="696" 
d="M174.5 667q51.5 40 142.5 40t140 -40.5t49 -112t-32 -112.5t-113 -88l122 -121q7 19 14 65.5t9 77.5l105 -2q-14 -122 -49 -205l119 -109l-62 -71l-113 98q-33 -46 -88 -72t-121 -26q-141 0 -200 53t-59 157q0 83 35 128.5t113 74.5q-39 45 -51 76t-12 80q0 69 51.5 109z
M285 86q48 0 88 16.5t59 45.5l-189 189q-50 -19 -72.5 -49t-22.5 -80q0 -122 137 -122zM233 536q0 -49 45 -97l22 -23q53 30 74.5 56t21.5 67q0 76 -81.5 76t-81.5 -79z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="225" 
d="M64 453l-3 232h106l-7 -232h-96z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="289" 
d="M163 301q0 -87 23.5 -193.5t47.5 -169.5l24 -63h-103q-14 23 -36 76.5t-37 101t-27 116t-12 151.5t28 195t56 173l28 62h103q-34 -86 -64.5 -224t-30.5 -225z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="289" 
d="M246 301q0 -64 -11.5 -131.5t-28.5 -117.5q-34 -106 -60 -156l-12 -21h-103q11 26 26.5 69.5t42 156.5t26.5 200t-23.5 199.5t-47.5 181.5l-24 68h103q14 -26 36 -84t37 -108.5t27 -121.5t12 -135z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="423" 
d="M382 504h-127l39 -122l-62 -19l-41 123l-105 -79l-40 51l107 79l-104 74l39 54l103 -75l40 123l64 -21l-39 -124h126v-64z" />
    <glyph glyph-name="plus" unicode="+" 
d="M52 202v98h178v179h98v-179h180v-98h-180v-181h-98v181h-178z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="246" 
d="M26 -123l48 247h120l-80 -247h-88z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="421" 
d="M61 220v99h299v-99h-299z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="239" 
d="M61 0v144h117v-144h-117z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="443" 
d="M32 9l285 705l96 -35l-286 -704z" />
    <glyph glyph-name="zero" unicode="0" 
d="M280 671q125 0 187 -80t62 -264.5t-62.5 -261t-187 -76.5t-187 76t-62.5 260.5t62.5 265t187.5 80.5zM280 574q-72 0 -103.5 -54.5t-31.5 -191.5t32.5 -189.5t103 -52.5t102 52.5t31.5 189.5t-31 191.5t-103 54.5z" />
    <glyph glyph-name="one" unicode="1" 
d="M405 660v-660h-111v534l-155 -100l-51 83l215 143h102z" />
    <glyph glyph-name="two" unicode="2" 
d="M492 0h-425v95l163 168q74 76 104 119t30 94.5t-29 73.5t-95 22q-62 0 -137 -14l-25 -4l-7 88q97 29 201 29q207 0 207 -184q0 -72 -31.5 -125t-112.5 -128l-141 -137h298v-97z" />
    <glyph glyph-name="three" unicode="3" 
d="M66 639q93 32 204 32t161.5 -42.5t50.5 -134.5q0 -40 -15.5 -72t-31.5 -46.5t-40 -30.5q53 -25 76.5 -54.5t23.5 -95.5q0 -105 -52.5 -155.5t-167.5 -50.5q-45 0 -99 7.5t-86 15.5l-31 8l7 86q110 -20 194 -20q120 1 120 106q0 48 -31 73t-82 26h-133v93h133
q37 0 68.5 30.5t31.5 74t-27.5 64.5t-75.5 21q-79 0 -163 -14l-26 -5z" />
    <glyph glyph-name="four" unicode="4" 
d="M337 0v118h-297v85l174 457h123l-184 -445h184v194h112v-194h72v-97h-72v-118h-112z" />
    <glyph glyph-name="five" unicode="5" 
d="M484 660v-98h-306l-17 -174q68 31 134 31q212 0 212 -198q0 -112 -58.5 -172t-165.5 -60q-45 0 -102 8.5t-91 16.5l-34 9l12 84q118 -20 202 -20q58 0 90 32t32 91t-28 85t-73 26q-81 0 -132 -20l-18 -7l-70 15l18 351h395z" />
    <glyph glyph-name="six" unicode="6" 
d="M490 560q-97 14 -176 14t-118 -49t-40 -137l23 8q72 23 121 23q224 0 224 -210q0 -108 -62 -164t-181 -56t-179.5 88.5t-60.5 252.5q0 341 268 341q75 0 162 -18l29 -6zM174 299l-19 -7q2 -206 132 -206q59 0 90.5 32t31.5 89t-31 85.5t-86.5 28.5t-117.5 -22z" />
    <glyph glyph-name="seven" unicode="7" 
d="M73 560v100h416v-132l-243 -539l-104 30l235 511v30h-304z" />
    <glyph glyph-name="eight" unicode="8" 
d="M278 671q110 0 175.5 -45.5t65.5 -130.5q0 -60 -18.5 -90.5t-69.5 -62.5q51 -33 74.5 -67.5t23.5 -95.5q0 -100 -69 -145t-182 -45q-248 0 -248 179q0 67 23 103t73 71q-47 31 -66 64.5t-19 89.5q0 84 63.5 129.5t173.5 45.5zM149 192q0 -103 130.5 -103t130.5 105
q0 69 -84 95h-96q-81 -26 -81 -97zM400 479q0 92 -120 92t-120 -92q0 -60 70 -95h96q74 35 74 95z" />
    <glyph glyph-name="nine" unicode="9" 
d="M248 86q152 0 154 192l-24 -8q-76 -26 -122 -26q-108 0 -165 51.5t-57 154.5t63.5 162t172.5 59q125 0 186 -89t61 -263t-68.5 -252t-200.5 -78q-75 0 -162 18l-29 6l10 87q97 -14 181 -14zM265 342q57 0 117 22l21 7q-2 203 -133 203q-57 0 -89 -33t-32 -90
q0 -109 116 -109z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="239" 
d="M61 303v144h117v-144h-117zM61 0v144h117v-144h-117z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="266" 
d="M84 124h120l-80 -247h-88zM77 303v144h117v-144h-117z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M474 377l-294 -124l294 -130v-112l-410 195v90l410 192v-111z" />
    <glyph glyph-name="equal" unicode="=" 
d="M61 301v98h438v-98h-438zM61 103v98h438v-98h-438z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M380 253l-294 124v111l410 -192v-90l-410 -195v112z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="441" 
d="M406 534q0 -67 -16.5 -102t-67.5 -75t-67.5 -63.5t-16.5 -50.5v-33h-88q-24 31 -24 78q0 25 19 50t68 65.5t65 63t16 59.5q0 73 -106 73q-60 0 -126 -14l-23 -5l-6 83q93 33 169 33q105 0 154.5 -38.5t49.5 -123.5zM133 0v140h116v-140h-116z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="975" 
d="M939 305v-10q0 -165 -46 -231.5t-139 -66.5q-45 0 -76.5 14.5t-40.5 28.5l-9 15q-88 -57 -167.5 -57t-127 55.5t-47.5 190t44 196.5t152 62q40 0 86 -18l16 -7v14h108v-184q0 -152 10 -185q3 -12 11 -21t16.5 -11t29.5 -2t37 14q34 31 34 193v11q0 171 -77.5 248
t-250.5 77t-261.5 -93t-88.5 -284.5t81.5 -278t269.5 -86.5l143 9l5 -95q-92 -9 -148 -9q-115 0 -197 22.5t-142 75.5q-120 105 -120 369q0 235 120.5 350t339.5 115q435 0 435 -421zM477 93q42 0 114 34q-7 40 -7 135v130q-50 15 -80 15q-64 0 -86 -36.5t-22 -122.5
q0 -155 81 -155z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="612" 
d="M79 685h260q105 0 157 -42.5t52 -135.5q0 -61 -19.5 -96t-61.5 -58q98 -38 98 -159q0 -194 -218 -194h-268v685zM339 299h-149v-203h151q55 0 82.5 23t27.5 79t-32 78.5t-80 22.5zM333 589h-143v-196h147q98 0 98 101q0 95 -102 95z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="544" 
d="M309 -11q-150 0 -203.5 83t-53.5 273t54 270.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92q-103 -22 -195 -22z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="641" 
d="M316 0h-237v685h237q158 0 216 -80t58 -251q0 -87 -11.5 -147.5t-40.5 -109.5q-57 -97 -222 -97zM475 354q0 124 -31 179t-128 55h-126v-490h126q99 0 132 73q16 37 21.5 79t5.5 104z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="529" 
d="M79 0v685h425v-97h-314v-231h261v-97h-261v-260h-111z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="613" 
d="M358 262v98h195v-348q-27 -7 -109.5 -15t-119.5 -8q-156 0 -215 86t-59 269.5t60.5 267.5t209.5 84q88 0 198 -20l35 -7l-4 -88q-121 16 -217 16t-131.5 -54t-35.5 -200t33.5 -201.5t135.5 -55.5q73 0 109 9v167h-85z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="676" 
d="M486 0v296h-296v-296h-111v685h111v-292h296v292h112v-685h-112z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="296" 
d="M19 -71v98q53 0 72.5 16t19.5 71v571h110l1 -579q0 -107 -45 -142t-158 -35z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="583" 
d="M190 0h-111v685h111v-309l102 8l140 301h127l-169 -344l176 -341h-129l-145 286l-102 -7v-279z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="475" 
d="M463 0h-384v685h111v-586h273v-99z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="850" 
d="M79 0v685h193l153 -535l153 535h194v-685h-112v562h-15l-162 -534h-116l-162 534h-15v-562h-111z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="686" 
d="M79 0v685h194l208 -587h15v587h111v-685h-190l-213 588h-14v-588h-111z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="591" 
d="M330 213h-140v-213h-111v685h251q233 0 233 -230q0 -118 -58.5 -180t-174.5 -62zM190 309h139q120 0 120 146q0 70 -29 102t-91 32h-139v-280z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="656" 
d="M328 -11q-156 0 -217.5 84t-61.5 265.5t62.5 269.5t216.5 88t216 -87.5t62 -269.5q0 -120 -25.5 -194t-83.5 -113l84 -135l-103 -48l-89 146q-19 -6 -61 -6zM328 86q96 0 129.5 57.5t33.5 194.5t-34.5 199t-128.5 62t-129 -62t-35 -198t34 -194.5t130 -58.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="618" 
d="M190 244v-244h-111v685h260q232 0 232 -218q0 -146 -112 -198l113 -269h-122l-99 244h-161zM457 466q0 123 -118 123h-149v-249h151q60 0 88 34.5t28 91.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="543" 
d="M280 599q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -124.5q0 -108 -61.5 -161.5t-167.5 -53.5q-85 0 -188 20l-36 7l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5q77 0 182 -18l35 -6l-9 -90
q-140 16 -198 16z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="526" 
d="M13 586v99h500v-99h-193v-586h-112v586h-195z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="594" 
d="M458 685h116l-168 -685h-218l-168 685h116l136 -587h50z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="911" 
d="M24 685h117l98 -589h21l130 587h130l130 -587h22l98 589h117l-135 -685h-179l-118 549l-117 -549h-180z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="564" 
d="M138 685l147 -270l149 270h117l-203 -348l203 -337h-124l-147 258l-150 -258h-117l204 331l-204 354h125z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="549" 
d="M331 0h-112v282l-215 403h125l145 -294l146 294h124l-213 -403v-282z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="534" 
d="M42 588v97h450v-107l-318 -462v-19h318v-97h-450v106l317 462v20h-317z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="344" 
d="M306 749v-97h-123v-678h123v-97h-233v872h233z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="471" 
d="M440 13l-95 -40l-314 697l94 42z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="344" 
d="M38 652v97h233v-872h-233v97h123v678h-123z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M400 316l-124 238l-125 -238h-114l190 344h94l193 -344h-114z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="625" 
d="M99 -81h428v-93h-428v93z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="254" 
d="M24 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="536" 
d="M299 511q103 0 148.5 -57.5t45.5 -203.5t-54 -203.5t-190 -57.5q-47 0 -149 9l-34 3v707h108v-227q69 30 125 30zM249 86q80 0 106.5 37.5t26.5 127.5t-22.5 126.5t-73.5 36.5q-48 0 -96 -15l-16 -5v-304q55 -4 75 -4z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="442" 
d="M253 511q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8l3 -87q-101 -18 -152 -18q-114 0 -160.5 61.5t-46.5 201.5t49 199.5t160 59.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="540" 
d="M474 708v-708h-108v26q-73 -37 -134 -37q-98 0 -143.5 59t-45.5 198t50.5 202t157.5 63q36 0 114 -13v210h109zM348 104l17 7v296q-60 10 -111 10q-101 0 -101 -168q0 -92 23.5 -127.5t74.5 -35.5t97 18z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="346" 
d="M197 407v-407h-108v407h-58v93h58v32q0 107 31 147t110 40l108 -10l-1 -89q-49 2 -81.5 2t-45.5 -19.5t-13 -71.5v-31h132v-93h-132z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="525" 
d="M503 -68q0 -164 -237 -164q-114 0 -169 31t-55 113q0 37 18 63.5t58 57.5q-33 22 -33 73q0 20 27 65l9 15q-72 43 -72 150q0 90 54 132t146 42q44 0 87 -10l15 -3l155 4v-88l-83 5q27 -35 27 -82q0 -98 -49.5 -135.5t-154.5 -37.5q-26 0 -44 4q-14 -34 -14 -52.5
t18.5 -25.5t89.5 -8q119 -1 163 -32t44 -117zM149 -78q0 -35 28 -50t96 -15q121 0 121 69q0 39 -21.5 49.5t-85.5 11.5l-98 6q-22 -18 -31 -33.5t-9 -37.5zM179 268.5q22 -21.5 71 -21.5t70.5 21.5t21.5 67.5t-22 67.5t-71 21.5q-92 0 -92 -89q0 -46 22 -67.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="546" 
d="M175 0h-109v708h109v-234q74 37 137 37q100 0 136.5 -56.5t36.5 -186.5v-268h-109v265q0 81 -17 115t-72 34q-48 0 -96 -16l-16 -6v-392z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="241" 
d="M66 0v500h109v-500h-109zM66 585v115h109v-115h-109z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="242" 
d="M67 19v481h108v-482q0 -98 -34.5 -145t-138.5 -100l-43 81q70 44 89 71.5t19 93.5zM67 585v115h108v-115h-108z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="499" 
d="M175 0h-109v708h109v-409l62 6l118 195h122l-144 -234l152 -266h-123l-122 211l-65 -7v-204z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="253" 
d="M72 0v708h109v-708h-109z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="838" 
d="M175 0h-109v500h108v-31q71 42 130 42q87 0 127 -49q91 49 181 49t127 -55.5t37 -187.5v-268h-108v265q0 81 -16.5 115t-68.5 34q-45 0 -97 -20l-17 -7q8 -20 8 -128v-259h-108v257q0 89 -16 123t-70 34q-50 0 -93 -20l-15 -6v-388z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="546" 
d="M175 0h-109v500h108v-31q73 42 138 42q100 0 136.5 -56.5t36.5 -186.5v-268h-108v265q0 81 -17.5 115t-71.5 34q-51 0 -98 -20l-15 -6v-388z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="537" 
d="M66 -215v715h108v-31q69 42 129 42q99 0 145 -60.5t46 -203t-52.5 -200.5t-171.5 -58q-41 0 -95 9v-213h-109zM284 414q-47 0 -94 -21l-15 -7v-295q40 -8 88 -8q68 0 94 39t26 133q0 159 -99 159z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="535" 
d="M239 -11q-103 0 -149.5 58t-46.5 203.5t54.5 203t188.5 57.5q49 0 150 -9l33 -3v-714h-108v234q-67 -30 -122 -30zM286 417q-79 0 -106.5 -39.5t-27.5 -128.5t24.5 -126t75.5 -37q47 0 94 15l15 5v307q-48 4 -75 4z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="361" 
d="M66 0v500h108v-60q85 55 170 71v-109q-86 -17 -147 -44l-22 -9v-349h-109z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="469" 
d="M413 397q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -110t-50.5 -116t-147.5 -37q-61 0 -154 17l-31 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38q64 0 157 -16l31 -6z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="358" 
d="M335 407h-138v-220q0 -61 9 -81t46 -20l82 3l5 -87q-67 -13 -102 -13q-85 0 -116.5 39t-31.5 147v232h-64v93h64v145h108v-145h138v-93z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="543" 
d="M369 500h108v-500h-108v31q-73 -42 -135 -42q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t70 -31q54 0 99 20l15 6v388z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="491" 
d="M19 500h114l96 -407h32l100 407h111l-130 -500h-194z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="768" 
d="M27 500h107l79 -407h20l95 397h112l95 -397h20l78 407h108l-106 -500h-173l-78 343l-78 -343h-173z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="464" 
d="M18 500h116l98 -173l99 173h116l-149 -246l149 -254h-116l-99 171l-98 -171h-116l145 251z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="492" 
d="M20 500h107l106 -407h27l106 407h108l-190 -715h-107l60 215h-86z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="454" 
d="M41 403v97h371v-97l-240 -306h240v-97h-371v97l241 306h-241z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="357" 
d="M228 575l7 -125q0 -63 -21 -91t-88 -46q66 -17 87.5 -47.5t21.5 -94.5l-7 -117q0 -43 19 -66.5t71 -26.5v-92q-107 4 -151.5 43.5t-44.5 129.5l7 121q0 77 -110 107v85q57 13 83.5 37.5t26.5 63.5l-7 127q0 94 44.5 132.5t152.5 42.5l1 -92q-53 -4 -72.5 -26t-19.5 -65z
" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="252" 
d="M72 -215v923h108v-923h-108z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="357" 
d="M129 54l-7 117q0 64 21.5 94.5t87.5 47.5q-67 18 -88 46t-21 91l7 125q0 43 -19.5 65t-72.5 26l1 92q108 -4 152.5 -42.5t44.5 -132.5l-7 -127q0 -39 26.5 -63.5t83.5 -37.5v-85q-110 -30 -110 -107l7 -121q0 -90 -44.5 -129.5t-151.5 -43.5v92q52 3 71 26.5t19 66.5z
" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M381.5 181q-27.5 0 -105 25.5t-96.5 25.5t-45.5 -10.5t-43.5 -21.5l-17 -10l-10 88q62 52 120 52q28 0 103 -25.5t95 -25.5q35 0 88 31l17 10l9 -87q-18 -18 -52.5 -35t-62 -17z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="220" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="248" 
d="M183 500v-140h-117v140h117zM172 259l10 -444h-116l10 444h96z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M274 -78v110q-91 5 -136 56t-45 161q0 206 180 219v108h91v-112l88 -12l-3 -83q-79 5 -122 5q-68 0 -95.5 -29t-27.5 -96.5t27.5 -95t98.5 -27.5l120 5l3 -83q-48 -10 -89 -12v-114h-90z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M456 564q-73 9 -118.5 9t-60 -20.5t-14.5 -74.5v-52h160v-93h-160v-240h137l72 16l17 -91l-81 -18h-331v93h79v240h-66v93h66v57q0 109 37 148t121 39q59 0 124 -15l21 -5z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M371 86q-43 -24 -91.5 -24t-91.5 24l-71 -72l-75 75l72 71q-24 40 -24 90t24 93l-72 72l75 75l71 -72q43 24 91.5 24t91.5 -24l72 72l75 -75l-72 -72q24 -43 24 -91.5t-24 -91.5l72 -71l-75 -75zM280 154q40 0 69 29t29 69t-29 69t-69 29t-69 -29t-29 -69t29 -69t69 -29z
" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M58 301v92h112l-157 267h124l144 -236l143 236h124l-156 -267h110v-92h-161l-4 -18v-45h165v-92h-165v-146h-111v146h-168v92h168v45l-3 18h-165z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="256" 
d="M74 708h108v-373h-108v373zM74 164h108v-379h-108v379z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="509" 
d="M433 534q-111 16 -165.5 16t-78 -17.5t-23.5 -55t29 -52.5t114 -33.5t118.5 -47.5t33.5 -99.5t-47 -135.5q37 -32 37 -115q0 -167 -205 -167q-54 0 -152 16l-30 5l11 88q108 -15 163 -15q109 0 109 75q0 35 -25 48t-70 23t-47 11q-89 20 -125.5 51.5t-36.5 101.5
q0 36 20.5 75t41.5 56q-44 32 -44 109q0 173 206 173q66 0 143 -16l28 -5zM177 325q-25 -37 -25 -73.5t17.5 -51t71.5 -27t89 -25.5q6 8 13.5 33.5t7.5 53.5t-20 42.5t-85 30.5t-69 17z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="254" 
d="M-22 607v114h104v-114h-104zM187 607v114h104v-114h-104z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="644" 
d="M128.5 235.5q-76.5 80.5 -76.5 199.5t76.5 198.5t195 79.5t193.5 -81t75 -199.5t-75 -198t-193.5 -79.5t-195 80.5zM106 434q0 -94 61.5 -160t154 -66t154.5 65.5t62 160t-62.5 161t-154 66.5t-153.5 -66.5t-62 -160.5zM331 595q38 0 79 -13l-6 -72q-36 7 -65.5 7
t-39 -17.5t-9.5 -63t10 -65.5t36 -20l68 6l6 -69q-32 -16 -90.5 -16t-86.5 36.5t-28 125.5t29 125t97 36z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="403" 
d="M330 551v-139q8 -10 23 -12l-2 -68q-55 0 -75.5 7t-32.5 23q-48 -29 -96 -29t-74 27.5t-26 74.5q0 88 114 94l76 4v23q0 26 -46 26l-124 -7l-3 63q70 21 135 21t98 -23.5t33 -84.5zM177 470q-36 -3 -36 -33.5t33 -30.5q25 0 54 12l9 4v52z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="574" 
d="M251 330l-116 -83l116 -94v-111l-209 162v81l209 155v-110zM514 330l-116 -83l116 -94v-111l-209 162v81l209 155v-110z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M61 351h432v-241h-98v144h-334v97z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="644" 
d="M129 235.5q-77 80.5 -77 199.5t76.5 198.5t195 79.5t193.5 -81t75 -199.5t-75 -198t-193 -79.5t-195 80.5zM106 434q0 -94 62.5 -160t154.5 -66t153.5 65.5t61.5 160t-62.5 161t-154 66.5t-153.5 -66.5t-62 -160.5zM286 378v-99h-80v309h125q114 0 114 -100
q0 -38 -9.5 -59.5t-34.5 -37.5l48 -112h-84l-37 99h-42zM285 527v-89h41q42 0 42 44.5t-47 44.5h-36z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="254" 
d="M-9 609v82h282v-82h-282z" />
    <glyph glyph-name="degree" unicode="&#xb0;" 
d="M134 550q0 65 40.5 105.5t105.5 40.5t105.5 -40.5t40.5 -105.5t-40.5 -105t-105.5 -40t-105.5 40t-40.5 105zM198 550q0 -37 22.5 -60t59.5 -23t60.5 23t23.5 60t-23.5 60.5t-60.5 23.5t-59.5 -23.5t-22.5 -60.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M52 280v98h178v115h98v-115h180v-98h-180v-112h-98v112h-178zM52 125h456v-98h-456v98z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="280" 
d="M246 478h-221v75l79 71q48 41 48 68q0 25 -42 25l-79 -7l-4 80q67 10 118 10t75 -22.5t24 -64.5t-13 -65.5t-45 -48.5l-49 -42h109v-79z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="280" 
d="M134 800q114 0 114 -86q0 -53 -34 -72q41 -16 41 -75q0 -99 -113 -99l-118 8l6 77q59 -7 96.5 -7t37.5 28q0 26 -35 26h-66v72h65q12 0 20 7t8 19q0 24 -35 24l-87 -6l-6 75q65 9 106 9z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="268" 
d="M14 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="mu" unicode="&#xb5;" 
d="M380 500h108v-500h-108v31q-73 -42 -135 -42q-39 0 -64 7v-211h-109v715h109v-286q1 -74 17 -101t68 -27q54 0 99 20l15 6v388z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="596" 
d="M403 0v592h-85v-592h-94v303h-7q-82 0 -134 53t-52 137t52.5 138t135.5 54h330v-93h-52v-592h-94z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="239" 
d="M61 194v144h117v-144h-117z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="264" 
d="M201 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v101h47v-43q63 -1 91 -18z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="280" 
d="M204 790v-312h-88v215l-56 -38l-41 62l105 73h80z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="403" 
d="M201.5 659q155.5 0 155.5 -163t-155.5 -163t-155.5 163t155.5 163zM202 419q32 0 44 18t12 60t-11.5 59.5t-44 17.5t-46 -18t-13.5 -59t13.5 -59.5t45.5 -18.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="575" 
d="M439 247l-116 83v110l209 -155v-81l-209 -162v111zM176 247l-116 83v110l209 -155v-81l-209 -162v111z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="542" 
d="M211 790v-312h-88v215l-56 -38l-41 62l105 73h80zM32 29l393 631l47 -30l-393 -633zM410 -100v41h-136v73l52 198h98l-61 -192h47l10 89h77v-89h18v-79h-18v-41h-87z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="536" 
d="M213 790v-312h-88v215l-56 -38l-41 62l105 73h80zM31 29l393 631l47 -30l-393 -633zM501 -100h-221v75l79 71q48 41 48 68q0 25 -42 25l-79 -7l-4 80q67 10 118 10t75 -22.5t24 -64.5t-13 -65.5t-45 -48.5l-49 -42h109v-79z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="556" 
d="M151 800q114 0 114 -86q0 -53 -34 -72q41 -16 41 -75q0 -99 -113 -99l-118 8l6 77q59 -7 96.5 -7t37.5 28q0 26 -35 26h-66v72h65q12 0 20 7t8 19q0 24 -35 24l-87 -6l-6 75q65 9 106 9zM47 29l393 631l47 -30l-393 -633zM426 -100v41h-136v73l52 198h98l-61 -192h47
l10 89h77v-89h18v-79h-18v-41h-87z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="437" 
d="M32 -34q0 67 16.5 102t67.5 75t67.5 63.5t16.5 50.5v33h88q24 -31 24 -78q0 -25 -19 -50t-68 -65.5t-65 -63t-16 -59.5q0 -73 106 -73q60 0 126 14l23 5l6 -83q-93 -33 -169 -33q-105 0 -154.5 38.5t-49.5 123.5zM305 500v-140h-116v140h116z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM186 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM172 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM117 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM376 777q-24 0 -87 25t-76 25q-25 0 -64 -33l-13 -11l-24 78q6 7 15.5 17.5t36 27.5t52.5 17t87.5 -25t72.5 -25q22 0 62 33l14 11l24 -79q-15 -21 -45.5 -41t-54.5 -20z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM142 783v114h104v-114h-104zM351 783v114h104v-114h-104z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="599" 
d="M436 731q0 -38 -21 -67l165 -664h-112l-37 147h-263l-37 -147h-111l164 664q-21 29 -21 67q0 54 39 85.5t97.5 31.5t97.5 -31.5t39 -85.5zM275 592l-85 -347h219l-84 347h-50zM239 731q0 -22 16 -34t44 -12t44.5 12t16.5 34t-16.5 34.5t-44.5 12.5t-44 -12.5t-16 -34.5z
" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="873" 
d="M399 0v142h-229l-42 -142h-113l202 693h612v-107h-320v-181h260v-105h-260v-193h320v-107h-430zM296 586l-98 -337h201l1 337h-104z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="544" 
d="M409 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v91q-126 9 -172.5 92.5t-46.5 267.5t54 264.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92
q-92 -20 -186 -22v-31q63 -1 91 -18z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM187 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM164 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM115 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM138 783v114h104v-114h-104zM347 783v114h104v-114h-104z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM12 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM10 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM-49 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM-22 783v114h104v-114h-104zM187 783v114h104v-114h-104z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="644" 
d="M23 292v106h60v295h235q162 0 221 -87q30 -45 42 -103.5t12 -145t-11.5 -148t-40.5 -111.5q-58 -98 -223 -98h-235v292h-60zM478 359q0 119 -31.5 173.5t-128.5 54.5h-125v-189h142v-106h-142v-186h125q100 0 132 73q17 37 22.5 78t5.5 102z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="686" 
d="M79 0v685h194l208 -587h15v587h111v-685h-190l-213 588h-14v-588h-111zM421 777q-24 0 -87 25t-76 25q-25 0 -64 -33l-13 -11l-24 78q6 7 15.5 17.5t36 27.5t52.5 17t87.5 -25t72.5 -25q22 0 63 33l13 11l24 -79q-15 -21 -45.5 -41t-54.5 -20z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM214 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM181 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM148 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM408 777q-24 0 -87 25t-76 25q-25 0 -64 -33l-13 -11l-24 78
q6 7 15.5 17.5t36 27.5t52.5 17t87.5 -25t72.5 -25q22 0 62 33l14 11l24 -79q-15 -21 -45.5 -41t-54.5 -20z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM172 783v114h104v-114h-104zM381 783v114h104v-114h-104z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M132 467l148 -150l149 151l68 -69l-151 -149l151 -148l-69 -69l-148 151l-148 -151l-70 69l151 148l-150 148z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="656" 
d="M328 -11q-60 0 -107 12l-57 -123l-80 39l58 124q-93 75 -93 298q0 181 62.5 269t216.5 88q61 0 112 -16l58 125l82 -35l-62 -133q88 -80 88 -298q0 -182 -61.5 -266t-216.5 -84zM199 537q-35 -62 -35 -196t30 -189l203 436q-30 11 -69 11q-94 0 -129 -62zM328 86
q96 0 129.5 58t33.5 188.5t-27 189.5l-199 -428q29 -8 63 -8z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM219 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM185 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM144 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM169 783v114h104v-114h-104zM378 783v114h104v-114h-104z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="549" 
d="M331 0h-112v282l-215 403h125l145 -294l146 294h124l-213 -403v-282zM148 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="597" 
d="M329 216q61 0 91.5 37t30.5 102.5t-30 96t-92 30.5h-139v-266h139zM330 108h-140v-108h-111v693h111v-105h140q234 0 234 -231q0 -119 -59.5 -184t-174.5 -65z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="595" 
d="M175 0h-109v532q0 102 50 144.5t163 42.5t165 -35.5t52 -116.5q0 -53 -20.5 -79.5t-62.5 -45.5t-53 -28t-11 -23t16.5 -26.5t81.5 -43.5t92.5 -62t27.5 -89q0 -101 -46 -141t-156 -40q-57 0 -121 14l-23 5l4 88q92 -11 139 -11q91 0 91 69q0 30 -17.5 46t-81.5 46
q-67 29 -93 58.5t-26 75t18 70.5t59.5 42.5t55.5 31.5t14 42t-22 42.5t-81.5 14.5t-82.5 -21t-23 -75v-527z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM145 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM105 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM88 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM342.5 613q-25.5 -16 -46.5 -16t-69.5 20t-62.5 20q-24 0 -63 -25l-13 -9l-24 63l16 16q9 9 35 24.5t47.5 15.5t70 -20t60.5 -20q22 0 63 27l13 9l24 -64l-15 -16q-10 -9 -35.5 -25z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM83 607v114h104v-114h-104zM292 607v114h104v-114h-104z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM181 561.5q-32 31.5 -32 79t32 79.5t79 32t79.5 -32.5t32.5 -79.5t-32.5 -78.5t-79.5 -31.5t-79 31.5zM225 676.5q-14 -14.5 -14 -36t14 -36t35.5 -14.5t35.5 14.5t14 36t-14 36t-35.5 14.5t-35.5 -14.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="793" 
d="M706 94l29 3l2 -85q-115 -23 -209 -23t-144 50l-27 -12q-86 -38 -180 -38q-70 0 -106.5 42t-36.5 119.5t42.5 110.5t134.5 40l117 9v32q0 32 -17.5 48.5t-49.5 16.5q-75 0 -159 -8l-32 -3l-4 96q127 18 211 18t123 -52q49 52 149 52q204 0 204 -228l-7 -79h-310
q1 -60 27 -87.5t97.5 -27.5t145.5 6zM197 82q45 0 90 11.5t48 13.5q-8 47 -8 119l-115 -8q-69 -5 -69 -70.5t54 -65.5zM436 288h210q0 68 -24 97t-79.5 29t-81 -30t-25.5 -96z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="442" 
d="M343 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v92q-88 11 -124.5 72.5t-36.5 194.5t49 192.5t160 59.5q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8
l3 -87q-100 -18 -151 -18v-31q63 -1 91 -18z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM149 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM122 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM99 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM100 607v114h104v-114h-104zM309 607v114h104v-114h-104z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="241" 
d="M66 0v500h109v-500h-109zM-25 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="241" 
d="M34 647l236 101l34 -94l-244 -78zM66 500h109v-500h-109v500z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="241" 
d="M66 500h109v-500h-109v500zM-44 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="241" 
d="M66 500h109v-500h-109v500zM-40 607v114h104v-114h-104zM169 607v114h104v-114h-104z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="559" 
d="M377 633q137 -91 137 -281.5t-57 -275.5t-187 -85q-112 0 -171 56.5t-59 163t54.5 165.5t159.5 59q64 0 126 -23l20 -7q-5 55 -30.5 95.5t-81.5 72.5l-124 -83l-45 63l85 57q-44 15 -116 31l17 78q112 -15 193 -46l98 66l46 -63zM261 338q-52 0 -79.5 -35t-27.5 -88
q0 -126 116 -126q67 0 98 50.5t32 170.5q-67 28 -139 28z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="546" 
d="M66 0v500h108v-31l20 11q20 10 54.5 20.5t63.5 10.5q100 0 136.5 -56.5t36.5 -186.5v-268h-108v265q0 81 -17.5 115t-73.5 34t-111 -26v-388h-109zM393.5 613q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16q10 9 36 24.5t47.5 15.5t70 -20
t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM165 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM132 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM100 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM375.5 613q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16
q10 9 36 24.5t47.5 15.5t70 -20t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM107 607v114h104v-114h-104zM316 607v114h104v-114h-104z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M105 241v110h68v77h213v-77h69v-110h-350zM225 23v118h108v-118h-108z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="530" 
d="M265 511q26 0 64 -6l41 101l68 -25l-42 -101q92 -55 92 -227q0 -132 -50 -198t-173 -66q-38 0 -68 6l-43 -105l-67 25l44 105q-89 57 -89 233q0 128 51.5 193t171.5 65zM265 81q67 0 90 40t23 132t-26 129l-121 -298q15 -3 34 -3zM265 419q-64 0 -88.5 -38t-24.5 -132.5
t24 -129.5l119 297q-13 3 -30 3z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="543" 
d="M369 500h108v-500h-108v31q-73 -42 -135 -42q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t70 -31q54 0 99 20l15 6v388zM139 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM141 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM92 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM117 607v114h104v-114h-104zM326 607v114h104v-114h-104z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="492" 
d="M20 500h107l106 -407h27l106 407h108l-190 -715h-107l60 215h-86zM136 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="536" 
d="M258 83q74 0 99.5 39t25.5 128.5t-23 126.5t-74 37q-47 0 -95 -15l-16 -5v-305q53 -6 83 -6zM299 511q104 0 149 -57.5t45 -203t-51.5 -203.5t-175.5 -58l-91 7v-211h-109v923h109v-227q69 30 124 30z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="492" 
d="M20 500h107l106 -407h27l106 407h108l-190 -715h-107l60 215h-86zM90 607v114h104v-114h-104zM299 607v114h104v-114h-104z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM131 795v81h332v-81h-332z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM109 609v82h282v-82h-282z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="599" 
d="M20 0l169 685h221l170 -685h-112l-37 147h-263l-37 -147h-111zM275 592l-85 -347h219l-84 347h-50zM244.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="508" 
d="M436 343v-212q1 -25 10.5 -35.5t32.5 -13.5l-3 -93q-54 0 -81 8t-51 28l-24 -9q-24 -9 -65.5 -18t-77.5 -9q-69 0 -106 42t-37 118t40 110t125 40l129 11v32q0 31 -18 48t-48 17q-74 0 -193 -10l-3 93l34 5q102 15 168 15q84 0 126 -39.5t42 -127.5zM197 82q57 0 131 22
v122l-116 -8q-69 -4 -69 -70t54 -66zM253 684q62 0 67 57h112q-5 -69 -51 -111t-128 -42t-128 41.5t-52 111.5h112q6 -57 68 -57z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="599" 
d="M504 -91.5q0 -15.5 10 -26t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 68q0 60 68 119h-2l-37 147h-263l-37 -147h-111l169 685h221l170 -685h-14q-22 -15 -42 -45.5t-20 -46zM275 592l-85 -347h219l-84 347h-50z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="506" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-25 0 -36 1q-52 -52 -52 -80q0 -17 10 -27.5t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 62.5t20.5 69.5t40.5 49l20 16q-13 7 -24 16q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36
q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69q50 0 106 16l18 6v133z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="544" 
d="M309 -11q-150 0 -203.5 83t-53.5 273t54 270.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92q-103 -22 -195 -22zM162 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="442" 
d="M253 511q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8l3 -87q-101 -18 -152 -18q-114 0 -160.5 61.5t-46.5 201.5t49 199.5t160 59.5zM110 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="544" 
d="M309 -11q-150 0 -203.5 83t-53.5 273t54 270.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92q-103 -22 -195 -22zM123 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="442" 
d="M253 511q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8l3 -87q-101 -18 -152 -18q-114 0 -160.5 61.5t-46.5 201.5t49 199.5t160 59.5zM66 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="544" 
d="M309 -11q-150 0 -203.5 83t-53.5 273t54 270.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92q-103 -22 -195 -22zM235 777v114h108v-114h-108z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="442" 
d="M253 511q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8l3 -87q-101 -18 -152 -18q-114 0 -160.5 61.5t-46.5 201.5t49 199.5t160 59.5zM200 579v114h108v-114h-108z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="544" 
d="M309 -11q-150 0 -203.5 83t-53.5 273t54 270.5t203 80.5q89 0 196 -25l-4 -90q-90 16 -180 16t-122 -53.5t-32 -201t31 -201t121 -53.5t182 15l3 -92q-103 -22 -195 -22zM259 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="442" 
d="M253 511q53 0 125 -14l25 -5l-4 -86q-79 8 -117 8q-76 0 -102 -34t-26 -128t25 -130t104 -36l117 8l3 -87q-101 -18 -152 -18q-114 0 -160.5 61.5t-46.5 201.5t49 199.5t160 59.5zM205 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="641" 
d="M316 0h-237v685h237q158 0 216 -80t58 -251q0 -87 -11.5 -147.5t-40.5 -109.5q-57 -97 -222 -97zM475 354q0 124 -31 179t-128 55h-126v-490h126q99 0 132 73q16 37 21.5 79t5.5 104zM253 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="625" 
d="M576 680h106l-53 -222h-96zM474 708v-708h-108v26q-73 -37 -134 -37q-98 0 -143.5 59t-45.5 198t50.5 202t157.5 63q36 0 114 -13v210h109zM348 104l17 7v296q-60 10 -111 10q-101 0 -101 -168q0 -92 23.5 -127.5t74.5 -35.5t97 18z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="644" 
d="M23 292v106h60v295h235q162 0 221 -87q30 -45 42 -103.5t12 -145t-11.5 -148t-40.5 -111.5q-58 -98 -223 -98h-235v292h-60zM478 359q0 119 -31.5 173.5t-128.5 54.5h-125v-189h142v-106h-142v-186h125q100 0 132 73q17 37 22.5 78t5.5 102z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="540" 
d="M155 622v94h311v-8h8v-708h-108v26q-73 -37 -134 -37q-98 0 -143.5 59t-45.5 198t50.5 202t157.5 63q36 0 114 -13v124h-210zM348 104l17 7v296q-60 10 -111 10q-101 0 -101 -168q0 -92 23.5 -127.5t74.5 -35.5t97 18z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM125 795v81h332v-81h-332z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM117 609v82h282v-82h-282z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM249.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM206.5 674q19.5 -20 52 -20t51.5 20.5t21 53.5h84
q-6 -63 -47.5 -106t-109.5 -43t-110 43t-48 106h84q3 -34 22.5 -54z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM244 782v114h108v-114h-108z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM201 579v114h108v-114h-108z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-29q-22 -15 -42 -45.5t-20 -46t10 -26t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 68q0 60 68 119h-307z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="509" 
d="M346 -90q0 -17 10 -27.5t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 73.5t59 105.5q-29 -3 -53 -3q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t87 -29q81 0 156 6l28 3l2 -81q-24 -5 -55 -10l13 -1q-64 -57 -64 -91z
M362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="553" 
d="M79 0v685h432v-97h-321v-194h261v-96h-261v-200h321v-98h-432zM237 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="508" 
d="M422 90l28 3l2 -81q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 266 217 266q210 0 210 -229l-7 -78h-310q1 -62 27 -91t97 -29t146 6zM362 288q0 74 -23.5 103.5t-79.5 29.5t-81.5 -31t-26.5 -102h211zM218 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="613" 
d="M358 263v98h195v-348q-27 -7 -109.5 -15t-119.5 -8q-156 0 -215 86t-59 269.5t60.5 267.5t209.5 84q98 0 198 -20l35 -7l-4 -88q-121 16 -217 16t-131.5 -54t-35.5 -200t33.5 -201.5t135.5 -55.5q73 0 109 9v167h-85zM145 775l134 143h94l134 -143h-116l-64 67l-65 -67
h-117z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="525" 
d="M503 -68q0 -164 -237 -164q-114 0 -169 31t-55 113q0 37 18 63.5t58 57.5q-33 22 -33 73q0 20 27 65l9 15q-72 43 -72 150q0 90 54 132t146 42q44 0 87 -10l15 -3l155 4v-88l-83 5q27 -35 27 -82q0 -98 -49.5 -135.5t-154.5 -37.5q-26 0 -44 4q-14 -34 -14 -52.5
t18.5 -25.5t89.5 -8q119 -1 163 -32t44 -117zM149 -78q0 -35 28 -50t96 -15q121 0 121 69q0 39 -21.5 49.5t-85.5 11.5l-98 6q-22 -18 -31 -33.5t-9 -37.5zM179 268.5q22 -21.5 71 -21.5t70.5 21.5t21.5 67.5t-22 67.5t-71 21.5q-92 0 -92 -89q0 -46 22 -67.5zM107 585
l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="613" 
d="M358 262v98h195v-348q-27 -7 -109.5 -15t-119.5 -8q-156 0 -215 86t-59 269.5t60.5 267.5t209.5 84q88 0 198 -20l35 -7l-4 -88q-121 16 -217 16t-131.5 -54t-35.5 -200t33.5 -201.5t135.5 -55.5q73 0 109 9v167h-85zM272.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99
q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="525" 
d="M503 -68q0 -164 -237 -164q-114 0 -169 31t-55 113q0 37 18 63.5t58 57.5q-33 22 -33 73q0 20 27 65l9 15q-72 43 -72 150q0 90 54 132t146 42q44 0 87 -10l15 -3l155 4v-88l-83 5q27 -35 27 -82q0 -98 -49.5 -135.5t-154.5 -37.5q-26 0 -44 4q-14 -34 -14 -52.5
t18.5 -25.5t89.5 -8q119 -1 163 -32t44 -117zM149 -78q0 -35 28 -50t96 -15q121 0 121 69q0 39 -21.5 49.5t-85.5 11.5l-98 6q-22 -18 -31 -33.5t-9 -37.5zM179 268.5q22 -21.5 71 -21.5t70.5 21.5t21.5 67.5t-22 67.5t-71 21.5q-92 0 -92 -89q0 -46 22 -67.5zM215.5 674
q19.5 -20 52 -20t51.5 20.5t21 53.5h84q-6 -63 -47.5 -106t-109.5 -43t-110 43t-48 106h84q3 -34 22.5 -54z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="613" 
d="M358 262v98h195v-348q-27 -7 -109.5 -15t-119.5 -8q-156 0 -215 86t-59 269.5t60.5 267.5t209.5 84q88 0 198 -20l35 -7l-4 -88q-121 16 -217 16t-131.5 -54t-35.5 -200t33.5 -201.5t135.5 -55.5q73 0 109 9v167h-85zM258 782v114h108v-114h-108z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="525" 
d="M503 -68q0 -164 -237 -164q-114 0 -169 31t-55 113q0 37 18 63.5t58 57.5q-33 22 -33 73q0 20 27 65l9 15q-72 43 -72 150q0 90 54 132t146 42q44 0 87 -10l15 -3l155 4v-88l-83 5q27 -35 27 -82q0 -98 -49.5 -135.5t-154.5 -37.5q-26 0 -44 4q-14 -34 -14 -52.5
t18.5 -25.5t89.5 -8q119 -1 163 -32t44 -117zM149 -78q0 -35 28 -50t96 -15q121 0 121 69q0 39 -21.5 49.5t-85.5 11.5l-98 6q-22 -18 -31 -33.5t-9 -37.5zM179 268.5q22 -21.5 71 -21.5t70.5 21.5t21.5 67.5t-22 67.5t-71 21.5q-92 0 -92 -89q0 -46 22 -67.5zM212 579v114
h108v-114h-108z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="613" 
d="M358 262v98h195v-348q-27 -7 -109.5 -15t-119.5 -8q-156 0 -215 86t-59 269.5t60.5 267.5t209.5 84q88 0 198 -20l35 -7l-4 -88q-121 16 -217 16t-131.5 -54t-35.5 -200t33.5 -201.5t135.5 -55.5q73 0 109 9v167h-85zM281 -77h106l-53 -223h-97z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="525" 
d="M329 808l-44 -222h-105l52 222h97zM503 -68q0 -164 -237 -164q-114 0 -169 31t-55 113q0 37 18 63.5t58 57.5q-33 22 -33 73q0 20 27 65l9 15q-72 43 -72 150q0 90 54 132t146 42q44 0 87 -10l15 -3l155 4v-88l-83 5q27 -35 27 -82q0 -98 -49.5 -135.5t-154.5 -37.5
q-26 0 -44 4q-14 -34 -14 -52.5t18.5 -25.5t89.5 -8q119 -1 163 -32t44 -117zM149 -78q0 -35 28 -50t96 -15q121 0 121 69q0 39 -21.5 49.5t-85.5 11.5l-98 6q-22 -18 -31 -33.5t-9 -37.5zM179 268.5q22 -21.5 71 -21.5t70.5 21.5t21.5 67.5t-22 67.5t-71 21.5
q-92 0 -92 -89q0 -46 22 -67.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="676" 
d="M486 0v296h-296v-296h-111v685h111v-292h296v292h112v-685h-112zM157 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="546" 
d="M175 0h-109v708h109v-234q74 37 137 37q100 0 136.5 -56.5t36.5 -186.5v-268h-109v265q0 81 -17 115t-72 34q-48 0 -96 -16l-16 -6v-392zM112 760l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="685" 
d="M17 495v95h66v95h111v-95h296v95h112v-95h73v-95h-73v-495h-112v296h-296v-296h-111v495h-66zM194 393h296v102h-296v-102z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="546" 
d="M11 553v94h55v61h109v-61h147v-94h-147v-79q74 37 137 37q100 0 136.5 -56.5t36.5 -186.5v-268h-109v265q0 81 -17 115t-72 34q-48 0 -96 -16l-16 -6v-392h-109v553h-55z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM214 777q-24 0 -87 25t-76 25q-25 0 -64 -33l-13 -11l-24 78q6 7 15.5 17.5t36 27.5t52.5 17t87.5 -25t72.5 -25q22 0 62 33l14 11l24 -79q-15 -21 -45.5 -41t-54.5 -20z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="241" 
d="M66 500h109v-500h-109v500zM236.5 613q-25.5 -16 -46.5 -16t-69.5 20t-62.5 20q-24 0 -63 -25l-13 -9l-24 63l16 16q9 9 35 24.5t47.5 15.5t70 -20t60.5 -20q22 0 63 27l13 9l24 -64l-15 -16q-10 -9 -35.5 -25z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM-29 795v81h332v-81h-332z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="241" 
d="M66 500h109v-500h-109v500zM-20 609v82h282v-82h-282z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM95.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="241" 
d="M66 500h109v-500h-109v500zM69.5 674q19.5 -20 52 -20t51.5 20.5t21 53.5h84q-6 -63 -47.5 -106t-109.5 -43t-110 43t-48 106h84q3 -34 22.5 -54z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="269" 
d="M79 0v685h111v-685q-23 -17 -46.5 -46t-23.5 -45t10 -26.5t25 -10.5l46 5l10 -79q-53 -10 -95 -10t-70.5 25t-28.5 68q0 63 71 119h-9z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="241" 
d="M66 0v500h109v-500q-28 -21 -49.5 -47.5t-21.5 -43t10 -27t26 -10.5l46 5l9 -79q-53 -10 -94.5 -10t-70 25t-28.5 68q0 61 73 119h-9zM66 585v115h109v-115h-109z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="269" 
d="M79 0v685h111v-685h-111zM80 782v114h108v-114h-108z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="241" 
d="M66 0v500h109v-500h-109z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="296" 
d="M19 -71v98q53 0 72.5 16t19.5 71v571h110l1 -579q0 -107 -45 -142t-158 -35zM-15 775l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="242" 
d="M67 19v481h108v-482q0 -98 -34.5 -145t-138.5 -100l-43 81q70 44 89 72t19 93zM-39 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="581" 
d="M190 0h-111v693h111v-307l100 7l140 300h129l-169 -346l176 -347h-129l-145 286l-102 -7v-279zM253 -77h106l-53 -223h-96z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="499" 
d="M38 -300l43 223h106l-53 -223h-96zM175 0h-109v708h109v-409l62 6l118 195h122l-144 -234l152 -266h-123l-122 211l-65 -7v-204z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="476" 
d="M463 0h-384v693h111v-586h273v-107zM120 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="253" 
d="M72 0v708h109v-708h-109zM46 854l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="475" 
d="M220 -77h106l-53 -223h-97zM463 0h-384v685h111v-586h273v-99z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="253" 
d="M72 0v708h109v-708h-109zM69 -77h106l-53 -223h-97z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="496" 
d="M451 693v-249h-105v249h105zM463 0h-384v685h111v-586h273v-99z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="331" 
d="M282 680h106l-53 -222h-96zM72 0v708h109v-708h-109z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="482" 
d="M470 0h-384v240l-48 -33l-53 74l101 70v334h111v-256l118 83l53 -73l-171 -121v-219h273v-99z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="350" 
d="M117 0v244l-58 -40l-53 74l111 77v353h108v-277l68 48l53 -74l-121 -85v-320h-108z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="686" 
d="M79 0v685h194l208 -587h15v587h111v-685h-190l-213 588h-14v-588h-111zM210 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="546" 
d="M66 0v500h108v-31l20 11q20 10 54.5 20.5t63.5 10.5q100 0 136.5 -56.5t36.5 -186.5v-268h-108v265q0 81 -17.5 115t-73.5 34t-111 -26v-388h-109zM152 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="686" 
d="M310 -77h105l-52 -223h-97zM79 0v685h194l208 -587h15v587h111v-685h-190l-213 588h-14v-588h-111z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="546" 
d="M50 -300l43 223h106l-53 -223h-96zM175 0h-109v500h108v-31q73 42 138 42q100 0 136.5 -56.5t36.5 -186.5v-268h-108v265q0 81 -17.5 115t-71.5 34q-51 0 -98 -20l-15 -6v-388z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="686" 
d="M79 0v685h194l208 -587h15v587h111v-685h-190l-213 588h-14v-588h-111zM297 775l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="546" 
d="M175 0h-109v500h108v-31q73 42 138 42q100 0 136.5 -56.5t36.5 -186.5v-268h-108v265q0 81 -17.5 115t-71.5 34q-51 0 -98 -20l-15 -6v-388zM236 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="686" 
d="M79 0v685h194l208 -587h15v587h111v-710q0 -107 -45 -142t-158 -35v98q53 0 72.5 16t19.5 71v17h-79l-213 588h-14v-588h-111z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="546" 
d="M174 0h-108v499h108v-26q84 38 141 38q95 0 132.5 -61.5t37.5 -188.5v-259q0 -100 -32.5 -146.5t-137.5 -98.5l-48 90q71 40 90.5 66.5t19.5 84.5v262q0 76 -17 114t-66 38q-42 0 -100 -20l-20 -6v-386z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM159 795v81h332v-81h-332z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM127 614v82h282v-82h-282z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM276.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99
q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM216.5 674q19.5 -20 52 -20t51.5 20.5t21 53.5h84q-6 -63 -47.5 -106t-109.5 -43
t-110 43t-48 106h84q3 -34 22.5 -54z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="656" 
d="M328 86q96 0 129.5 58t33.5 194.5t-34.5 198.5t-128.5 62t-129 -62t-35 -197.5t34 -194.5t130 -59zM328 -11q-155 0 -217 84.5t-62 265.5t62.5 269t216.5 88t216 -87.5t62 -269.5t-61.5 -266t-216.5 -84zM358 779l99 169l90 -39l-108 -164zM154 780l100 169l89 -39
l-107 -164z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM270 626l129 168l85 -57l-135 -166zM71 626l129 167l85 -56l-135 -166z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="912" 
d="M868 0h-426q-72 -11 -140 -11t-119.5 21.5t-80 68.5t-40 109.5t-11.5 156.5q0 193 58 276t204 83q59 0 127 -11h428v-107h-318v-181h258v-106h-258v-192h318v-107zM331 98q30 0 109 7v482q-82 7 -112 7q-94 0 -128 -53t-34 -195t32.5 -195t132.5 -53z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="844" 
d="M758 94l28 3l2 -85q-114 -23 -202 -23q-113 0 -157 78q-45 -78 -165.5 -78t-171 66t-50.5 196t51.5 194.5t173.5 64.5t166 -91q47 91 157 91t162.5 -56t52.5 -172l-8 -79h-310q1 -60 27 -87.5t97.5 -27.5t146.5 6zM175.5 125.5q23.5 -38.5 90 -38.5t89.5 38.5t23 124.5
t-25.5 124.5t-88 38.5t-87.5 -37t-25 -124.5t23.5 -126zM487 288h210q0 69 -23.5 97.5t-79 28.5t-81.5 -30.5t-26 -95.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="618" 
d="M190 244v-244h-111v685h260q232 0 232 -218q0 -146 -112 -198l113 -269h-122l-99 244h-161zM457 466q0 123 -118 123h-149v-249h151q60 0 88 34.5t28 91.5zM178 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="361" 
d="M66 0v500h108v-60q85 55 170 71v-109q-86 -17 -147 -44l-22 -9v-349h-109zM56 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="618" 
d="M226 -300l43 223h106l-52 -223h-97zM190 244v-244h-111v685h260q232 0 232 -218q0 -146 -112 -198l113 -269h-122l-99 244h-161zM457 466q0 123 -118 123h-149v-249h151q60 0 88 34.5t28 91.5z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="361" 
d="M66 -77h106l-52 -223h-97zM66 0v500h108v-60q85 55 170 71v-109q-86 -17 -147 -44l-22 -9v-349h-109z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="618" 
d="M190 244v-244h-111v685h260q232 0 232 -218q0 -146 -112 -198l113 -269h-122l-99 244h-161zM457 466q0 123 -118 123h-149v-249h151q60 0 88 34.5t28 91.5zM253 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="361" 
d="M66 0v500h108v-60q85 55 170 71v-109q-86 -17 -147 -44l-22 -9v-349h-109zM139 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="543" 
d="M280 599q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -124.5q0 -108 -61.5 -161.5t-167.5 -53.5q-85 0 -188 20l-36 7l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5q77 0 182 -18l35 -6l-9 -90
q-140 16 -198 16zM153 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="469" 
d="M413 397q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -110t-50.5 -116t-147.5 -37q-61 0 -154 17l-31 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38q64 0 157 -16l31 -6zM101 647
l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="543" 
d="M280 599q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -124.5q0 -108 -61.5 -161.5t-167.5 -53.5q-85 0 -188 20l-36 7l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5q77 0 182 -18l35 -6l-9 -90
q-140 16 -198 16zM94 775l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="469" 
d="M413 397q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -110t-50.5 -116t-147.5 -37q-61 0 -154 17l-31 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38q64 0 157 -16l31 -6zM75 585
l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="543" 
d="M396 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v89q-73 2 -176 21l-33 6l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5q77 0 182 -18
l35 -6l-9 -90q-140 16 -198 16q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -132t-52.5 -145.5t-144.5 -60v-33q63 -1 91 -18z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="469" 
d="M342 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v90q-69 4 -133 16l-24 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38q64 0 157 -16l31 -6
l-2 -91q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -108q0 -148 -179 -154v-32q63 -1 91 -18z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="543" 
d="M280 599q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -124.5q0 -108 -61.5 -161.5t-167.5 -53.5q-85 0 -188 20l-36 7l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5q77 0 182 -18l35 -6l-9 -90
q-140 16 -198 16zM242 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="469" 
d="M413 397q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -110t-50.5 -116t-147.5 -37q-61 0 -154 17l-31 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38q64 0 157 -16l31 -6zM209 585
l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="Tcedilla" unicode="&#x162;" horiz-adv-x="0" 
 />
    <glyph glyph-name="tcedilla" unicode="&#x163;" horiz-adv-x="358" 
d="M335 407h-138v-220q0 -61 9 -81t46 -20l82 3l5 -87q-67 -13 -102 -13q-85 0 -116.5 39t-31.5 147v232h-64v93h64v145h108v-145h138v-93zM248 -60q28 -17 28 -68t-27.5 -77t-76.5 -26q-38 0 -77 7l-13 3l4 65q34 -2 55 -2q41 0 41 31q0 15 -9.5 21t-31.5 6h-31v101h47v-43
q63 -1 91 -18z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="526" 
d="M13 586v99h500v-99h-193v-586h-112v586h-195zM217 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="463" 
d="M336 401h-138v-208q0 -61 9 -81t46 -20l81 3l6 -93q-67 -13 -102 -13q-85 0 -117 39.5t-32 146.5v226h-64v98h64v146h109v-146h138v-98zM478 693v-249h-105v249h105z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="529" 
d="M14 586v99h500v-99h-193v-218h150v-94h-150v-274h-112v274h-145v94h145v218h-195z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="360" 
d="M336 401h-138v-91h112v-82h-112v-35q0 -61 9 -81t46 -20l82 3l5 -93q-67 -13 -102 -13q-85 0 -116.5 39t-31.5 147v53h-41v82h41v91h-64v98h64v146h108v-146h138v-98z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM403 777q-24 0 -87 25t-76 25q-25 0 -64 -33l-13 -11l-24 78q6 7 15.5 17.5t36 27.5t52.5 17t87.5 -25t72.5 -25q22 0 63 33l13 11l24 -79
q-15 -21 -45.5 -41t-54.5 -20z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM380.5 613q-25.5 -16 -46.5 -16t-69.5 20t-62.5 20q-24 0 -63 -25l-13 -9l-24 63l16 16q9 9 35 24.5t47.5 15.5
t70 -20t60.5 -20q22 0 63 27l13 9l24 -64l-15 -16q-10 -9 -35.5 -25z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM162 795v81h332v-81h-332z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM130 609v82h282v-82h-282z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM275.5 867q20.5 -18 52.5 -18t52.5 18t22.5 47h99q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM217.5 674q19.5 -20 52 -20t51.5 20.5t21 53.5h84q-6 -63 -47.5 -106t-109.5 -43t-110 43t-48 106h84
q3 -34 22.5 -54z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM464 842q0 -54 -39 -85.5t-97.5 -31.5t-97.5 31.5t-39 85.5t39 85.5t97.5 31.5t97.5 -31.5t39 -85.5zM267 842q0 -22 16 -34t44 -12t44.5 12t16.5 34
t-16.5 34.5t-44.5 12.5t-44 -12.5t-16 -34.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM192 561.5q-32 31.5 -32 79t32 79.5t79 32t79.5 -32.5t32.5 -79.5t-32.5 -78.5t-79.5 -31.5t-79 31.5zM236 676.5
q-14 -14.5 -14 -36t14 -36t35.5 -14.5t35.5 14.5t14 36t-14 36t-35.5 14.5t-35.5 -14.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="650" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -121 -63.5 -176.5t-189 -55.5t-188.5 55.5t-63 176.5v464h112v-466zM357 779l99 169l90 -39l-108 -164zM153 780l100 169l89 -39l-107 -164z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="543" 
d="M477 500v-500h-108v31l-20 -11q-20 -10 -53.5 -20.5t-61.5 -10.5q-103 0 -138 55.5t-35 194.5v261h109v-262q0 -90 15 -121t72.5 -31t111.5 26v388h108zM301 626l129 168l85 -57l-135 -166zM102 626l129 167l85 -56l-135 -166z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="651" 
d="M185 219q0 -133 140 -133t140 133v466h112v-464q0 -186 -159 -222q-61 -55 -61 -89q0 -17 10 -27.5t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 73.5t56 102.5q-119 3 -178.5 58.5t-59.5 173.5v464h112v-466z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="543" 
d="M369 499h108v-499q-24 -15 -48.5 -43t-24.5 -46t10 -28.5t26 -10.5l45 5l10 -79q-53 -10 -95 -10t-70.5 25t-28.5 68q0 61 76 119h-8v26q-78 -37 -135 -37q-103 0 -138 55.5t-35 194.5v260h109v-260q0 -90 15 -121t70 -31q39 0 96 19l18 6v387z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="911" 
d="M24 685h117l98 -589h21l130 587h130l130 -587h22l98 589h117l-135 -685h-179l-118 549l-117 -549h-180zM275 775l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="768" 
d="M27 500h107l79 -407h20l95 397h112l95 -397h20l78 407h108l-106 -500h-173l-78 343l-78 -343h-173zM225 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="549" 
d="M331 0h-112v282l-215 403h125l145 -294l146 294h124l-213 -403v-282zM95 776l134 143h94l134 -143h-116l-64 67l-65 -67h-117z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="492" 
d="M20 500h107l106 -407h27l106 407h108l-190 -715h-107l60 215h-86zM88 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="549" 
d="M331 0h-112v282l-215 403h125l145 -294l146 294h124l-213 -403v-282zM116 783v114h104v-114h-104zM325 783v114h104v-114h-104z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="534" 
d="M42 588v97h450v-107l-318 -462v-19h318v-97h-450v106l317 462v20h-317zM135 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="454" 
d="M41 500h371v-97l-240 -306h240v-97h-371v97l241 306h-241v97zM95 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="534" 
d="M42 588v97h450v-107l-318 -462v-19h318v-97h-450v106l317 462v20h-317zM213 782v114h108v-114h-108z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="454" 
d="M41 500h371v-97l-240 -306h240v-97h-371v97l241 306h-241v97zM172 579v114h108v-114h-108z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="534" 
d="M42 588v97h450v-107l-318 -462v-19h318v-97h-450v106l317 462v20h-317zM225 776l-134 143h117l65 -66l63 66h117l-134 -143h-94z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="454" 
d="M41 403v97h371v-97l-240 -306h240v-97h-371v97l241 306h-241zM199 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M304 -50q0 -102 -37 -142t-121 -40q-36 0 -88 8l-16 3v90q63 -4 92 -4q61 0 61 87v425h-58v93h58v37q0 102 30 148.5t111 46.5q26 0 88 -8l19 -3v-89q-45 2 -78.5 2t-47 -20.5t-13.5 -68.5v-45h131v-93h-131v-427z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="599" 
d="M435 731q0 -38 -20 -66l165 -665h-112l-37 147h-263l-37 -147h-111l163 663q-21 29 -21 75.5t39 78t97.5 31.5t97.5 -31.5t39 -85.5zM275 592l-85 -347h219l-84 347h-50zM155 933l236 103l35 -97l-243 -82zM238 731q0 -22 16 -34t44 -12h5q26 1 41 13t15 33.5t-16.5 34
t-44.5 12.5t-44 -12.5t-16 -34.5z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM170 561.5q-32 31.5 -32 79t32 79.5t79 32t79.5 -32.5t32.5 -79.5t-32.5 -78.5t-79.5 -31.5t-79 31.5zM214 676.5q-14 -14.5 -14 -36t14 -36t35.5 -14.5t35.5 14.5t14 36t-14 36t-35.5 14.5t-35.5 -14.5zM118 840l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="874" 
d="M400 0v142h-229l-42 -142h-113l202 693h612v-107h-320v-181h260v-105h-260v-193h320v-107h-430zM297 586l-98 -337h201l1 337h-104zM386 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="793" 
d="M706 94l29 3l2 -85q-115 -23 -209 -23t-144 50l-27 -12q-86 -38 -180 -38q-70 0 -106.5 42t-36.5 119.5t42.5 110.5t134.5 40l117 9v32q0 32 -17.5 48.5t-49.5 16.5q-75 0 -159 -8l-32 -3l-4 96q127 18 211 18t123 -52q49 52 149 52q204 0 204 -228l-7 -79h-310
q1 -60 27 -87.5t97.5 -27.5t145.5 6zM197 82q45 0 90 11.5t48 13.5q-8 47 -8 119l-115 -8q-69 -5 -69 -70.5t54 -65.5zM436 288h210q0 68 -24 97t-79.5 29t-81 -30t-25.5 -96zM271 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="658" 
d="M329 -11q-61 0 -107 12l-57 -123l-80 39l58 124q-93 75 -93 298q0 181 62.5 269t216.5 88q61 0 112 -16l58 125l82 -35l-62 -133q88 -80 88 -298q0 -182 -61.5 -266t-216.5 -84zM200 537q-35 -62 -35 -196t30 -189l203 436q-30 11 -69 11q-94 0 -129 -62zM329 86
q96 0 129.5 58t33.5 188.5t-27 189.5l-199 -428q29 -8 63 -8zM187 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="530" 
d="M265 511q26 0 64 -6l41 101l68 -25l-42 -101q92 -55 92 -227q0 -132 -50 -198t-173 -66q-38 0 -68 6l-43 -105l-67 25l44 105q-89 57 -89 233q0 128 51.5 193t171.5 65zM265 81q67 0 90 40t23 132t-26 129l-121 -298q15 -3 34 -3zM131 647l236 101l34 -94l-244 -78z
M265 419q-64 0 -88.5 -38t-24.5 -132.5t24 -129.5l119 297q-13 3 -30 3z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="543" 
d="M230 -77h106l-53 -223h-97zM280 599q-127 0 -127 -89q0 -46 30 -65.5t132.5 -48t144.5 -68t42 -124.5q0 -108 -61.5 -161.5t-167.5 -53.5q-85 0 -188 20l-36 7l11 89q135 -18 206 -18q124 0 124 110q0 43 -28 63.5t-126 45t-146 67.5t-48 136t60.5 140.5t167.5 47.5
q77 0 182 -18l35 -6l-9 -90q-140 16 -198 16z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="469" 
d="M82 -77h106l-53 -223h-97zM413 397q-118 16 -171 16t-73.5 -12.5t-20.5 -39.5t22.5 -38t106 -25.5t118.5 -45.5t35 -110t-50.5 -116t-147.5 -37q-61 0 -154 17l-31 5l4 91q120 -16 173 -16t75.5 13t22.5 43t-21.5 41.5t-103 25t-119.5 42.5t-38 106t52.5 115t134.5 38
q64 0 157 -16l31 -6z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="526" 
d="M233 -77h106l-53 -223h-97zM13 586v99h500v-99h-193v-586h-112v586h-195z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="358" 
d="M89 -77h106l-53 -223h-97zM335 407h-138v-220q0 -61 9 -81t46 -20l82 3l5 -87q-67 -13 -102 -13q-85 0 -116.5 39t-31.5 147v232h-64v93h64v145h108v-145h138v-93z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="254" 
d="M-22 585l125 146h69l127 -146h-102l-58 76l-58 -76h-103z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="254" 
d="M106 585l-125 146h102l59 -75l58 75h102l-127 -146h-69z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="254" 
d="M87.5 674q19.5 -20 52 -20t51.5 20.5t21 53.5h84q-6 -63 -47.5 -106t-109.5 -43t-110 43t-48 106h84q3 -34 22.5 -54z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="254" 
d="M73 579v114h108v-114h-108z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="254" 
d="M51 561.5q-32 31.5 -32 79t32 79.5t79 32t79.5 -32.5t32.5 -79.5t-32.5 -78.5t-79.5 -31.5t-79 31.5zM95 676.5q-14 -14.5 -14 -36t14 -36t35.5 -14.5t35.5 14.5t14 36t-14 36t-35.5 14.5t-35.5 -14.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="464" 
d="M308 -90q0 -17 10 -27.5t26 -10.5l45 5l10 -79q-53 -10 -94.5 -10t-70 25t-28.5 62.5t20 69.5t40 49l21 16l85 -9q-64 -57 -64 -91z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="254" 
d="M253.5 613q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16q10 9 36 24.5t47.5 15.5t70 -20t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="254" 
d="M149 626l129 168l85 -57l-135 -166zM-50 626l129 167l85 -56l-135 -166z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M337 154v253h-116l-27 -407h-109l31 407q-35 0 -82 -10l-14 -3v89q62 17 146 17h244q61 0 104 10l15 3v-90q-26 -11 -84 -14v-255q0 -39 15.5 -54t61.5 -15v-92q-62 0 -96.5 6t-55.5 25.5t-27 48.5t-6 81z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="911" 
d="M24 685h117l98 -589h21l130 587h130l130 -587h22l98 589h117l-135 -685h-179l-118 549l-117 -549h-180zM358 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="768" 
d="M27 500h107l79 -407h20l95 397h112l95 -397h20l78 407h108l-106 -500h-173l-78 343l-78 -343h-173zM265 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="911" 
d="M24 685h117l98 -589h21l130 587h130l130 -587h22l98 589h117l-135 -685h-179l-118 549l-117 -549h-180zM330 836l236 103l35 -97l-243 -82z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="768" 
d="M27 500h107l79 -407h20l95 397h112l95 -397h20l78 407h108l-106 -500h-173l-78 343l-78 -343h-173zM262 647l236 101l34 -94l-244 -78z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="911" 
d="M24 685h117l98 -589h21l130 587h130l130 -587h22l98 589h117l-135 -685h-179l-118 549l-117 -549h-180zM298 783v114h104v-114h-104zM507 783v114h104v-114h-104z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="768" 
d="M27 500h107l79 -407h20l95 397h112l95 -397h20l78 407h108l-106 -500h-173l-78 343l-78 -343h-173zM229 607v114h104v-114h-104zM438 607v114h104v-114h-104z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="507" 
d="M436 346v-228q1 -22 11.5 -32.5t32.5 -13.5l-3 -83q-86 0 -133 37q-80 -37 -161 -37q-149 0 -149 159q0 76 40.5 110t124.5 41l129 11v36q0 40 -17.5 56t-51.5 16q-64 0 -160 -8l-32 -2l-4 77q109 26 200.5 26t132 -39.5t40.5 -125.5zM212 222q-69 -6 -69 -75t61 -69
q50 0 106 16l18 6v133zM75 555l125 146h69l127 -146h-102l-58 76l-58 -76h-103zM343.5 757q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16q10 9 36 24.5t47.5 15.5t70 -20t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="597" 
d="M20 0l169 693h221l167 -692h-112l-40 141h-257l-37 -142h-111zM275 590l-83 -341h211l-81 341h-47zM248.5 830q20.5 -18 52.5 -18t52.5 18t22.5 47h99q-6 -65 -51 -104t-123.5 -39t-123.5 39t-50 104h99q2 -29 22.5 -47zM144 1101l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="509" 
d="M422 94l28 3l2 -85q-114 -23 -202 -23q-111 0 -159.5 61t-48.5 195q0 265 217 265q106 0 158 -56t52 -172l-7 -79h-310q1 -60 27 -87.5t97 -27.5t146 6zM362 288q0 68 -24 97t-79.5 29t-81 -30t-26.5 -96h211zM101 555l125 146h69l127 -146h-102l-58 76l-58 -76h-103z
M366.5 757q-25.5 -16 -46.5 -16t-69.5 20t-62.5 20q-24 0 -63 -25l-13 -9l-24 63l16 16q9 9 35 24.5t47.5 15.5t70 -20t60.5 -20q22 0 63 27l13 9l24 -64l-15 -16q-10 -9 -35.5 -25z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="530" 
d="M265 511q120 0 171.5 -65t51.5 -195t-50 -196t-173 -66t-173 66t-50 196t51.5 195t171.5 65zM265 81q67 0 90 40t23 131t-24.5 129t-88.5 38t-88.5 -38t-24.5 -129t23 -131t90 -40zM103 555l125 146h69l127 -146h-102l-58 76l-58 -76h-103zM363.5 757
q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16q10 9 36 24.5t47.5 15.5t70 -20t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="549" 
d="M331 0h-112v282l-215 403h125l145 -294l146 294h124l-213 -403v-282zM175 939l236 -103l-28 -76l-243 82z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="492" 
d="M20 500h107l106 -407h27l106 407h108l-190 -715h-107l60 215h-86zM125 748l236 -101l-26 -71l-244 78z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="537" 
d="M325 0h-111v297l-213 396h124l144 -272l143 272h124l-211 -397v-296zM378.5 806q-25.5 -16 -46.5 -16t-69.5 20t-62.5 20q-24 0 -63 -26l-13 -8l-24 63l16 15q9 10 35 25.5t47.5 15.5t70 -20t60.5 -20q22 0 63 27l13 9l24 -64l-15 -15q-10 -10 -35.5 -26z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="492" 
d="M20 499h107l106 -394h27l106 394h108l-190 -714h-107l57 215h-83zM357.5 613q-25.5 -16 -46.5 -16t-69.5 20t-61.5 20q-25 0 -64 -25l-13 -9l-24 63l15 16q10 9 36 24.5t47.5 15.5t70 -20t60.5 -20q22 0 62 27l14 9l24 -64l-16 -16q-9 -9 -34.5 -25z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="622" 
d="M61 313h500v-93h-500v93z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1122" 
d="M61 313h1000v-93h-1000v93z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="242" 
d="M201 691l-45 -235h-114l76 235h83z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="238" 
d="M47 457l45 235h114l-76 -235h-83z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="239" 
d="M57 -57l22 113h54l-36 -113h-40z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="413" 
d="M372 691l-45 -235h-114l76 235h83zM201 691l-45 -235h-114l76 235h83z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="412" 
d="M47 458l45 235h114l-76 -235h-83zM220 458l45 235h114l-76 -235h-83z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="394" 
d="M165 115l-45 -235h-114l76 235h83zM339 115l-45 -235h-114l76 235h83z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="479" 
d="M31 407v93h154v185h109v-185h155v-93h-155l-8 -480h-93l-8 480h-154z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="516" 
d="M204 -73v185h-155v93h155v202h-155v93h155v185h108v-185h154v-93h-154v-202h155v-93h-155v-185h-108z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="460" 
d="M105 103v282h250v-282h-250z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="748" 
d="M61 0v144h117v-144h-117zM316 0v144h117v-144h-117zM570 0v144h117v-144h-117z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="841" 
d="M122 586.5q-7 -17.5 -7 -57.5t7 -58t26.5 -18t26.5 17.5t7 58.5t-7 58t-26.5 17t-26.5 -17.5zM151 -6l222 697l62 -21l-222 -696zM33 529q0 142 115.5 142t115.5 -142q0 -72 -30 -108t-85.5 -36t-85.5 36t-30 108zM437 -11q-115 0 -115 143t115.5 143t115.5 -142
q0 -72 -30 -108t-86 -36zM410.5 189.5q-7.5 -17.5 -7.5 -57t7.5 -58t26.5 -18.5t26.5 18.5t7.5 58t-7.5 57t-26.5 17.5t-26.5 -17.5zM579 133q0 142 115.5 142t115.5 -142q0 -72 -30 -108t-85.5 -36t-85.5 36t-30 108zM667.5 189.5q-7.5 -17.5 -7.5 -57t7.5 -58t26.5 -18.5
t26.5 18t7.5 58.5t-7 57.5t-26.5 17t-27 -17.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="311" 
d="M251 330l-116 -83l116 -94v-111l-209 162v81l209 155v-110z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="311" 
d="M175 254l-116 83v110l209 -155v-80l-209 -163v111z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="0" 
d="M-224 29l393 631l47 -30l-393 -633z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="280" 
d="M45.5 762.5q30.5 37.5 94 37.5t94 -38t30.5 -129t-30.5 -128t-93.5 -37t-94 37t-31 128.5t30.5 129zM166.5 703.5q-7.5 18.5 -26 18.5t-26.5 -18.5t-8 -70.5t8 -69.5t26.5 -17.5t26 17t7.5 69.5t-7.5 71z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="280" 
d="M154 478v41h-136v73l52 198h98l-61 -192h47l10 89h77v-89h18v-79h-18v-41h-87z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="280" 
d="M141 604q-16 0 -38 -8l-65 8l10 186h199v-75h-129l-4 -46q28 10 50 10q93 0 93 -99q0 -110 -113 -110q-45 0 -96 10l-17 4l6 71q56 -9 92.5 -9t36.5 29t-25 29z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="280" 
d="M111 676q34 9 47 9q103 0 103 -104q0 -53 -32 -83t-83 -30q-125 0 -125 157q0 89 30.5 132t101.5 43q24 0 81 -8l16 -3l-4 -72q-60 6 -90 6q-45 0 -45 -47zM144 613q-14 0 -24 -5.5t-10 -6.5q2 -57 32.5 -57t30.5 34.5t-29 34.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="280" 
d="M28 703v87h217v-91l-99 -230l-98 16l96 202v16h-116z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="280" 
d="M140 800q119 0 119 -87q0 -30 -11.5 -49.5t-22.5 -22.5q13 -3 26 -21t13 -50q0 -55 -31.5 -78.5t-93 -23.5t-92.5 22.5t-31 77.5q0 31 13.5 50t25.5 24q-11 4 -22.5 21.5t-11.5 49.5q0 87 119 87zM140 539q34 0 34 32q0 23 -24 36h-20q-24 -14 -24 -37q0 -31 34 -31z
M140 729q-32 0 -32 -27q0 -21 22 -33h20q22 11 22 33q0 27 -32 27z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="280" 
d="M121 546q45 0 47 45q-25 -8 -47 -8q-49 0 -75 27t-26 79t31 81.5t90 29.5q119 0 119 -165q0 -86 -29.5 -126.5t-98.5 -40.5q-39 0 -86 9l-15 3l5 73q48 -7 85 -7zM116 662q8 -10 21.5 -10t33.5 9q0 62 -33 62q-30 0 -30 -35q0 -16 8 -26z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="280" 
d="M45.5 184.5q30.5 37.5 94 37.5t94 -38t30.5 -129t-30.5 -128t-93.5 -37t-94 37t-31 128.5t30.5 129zM166.5 125.5q-7.5 18.5 -26 18.5t-26.5 -18.5t-8 -70.5t8 -69.5t26.5 -17.5t26 17t7.5 69.5t-7.5 71z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="280" 
d="M219 212v-312h-88v215l-56 -38l-41 62l105 73h80z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="280" 
d="M253 -100h-221v75l79 71q48 41 48 68q0 25 -42 25l-79 -7l-4 80q67 10 118 10t75 -22.5t24 -64.5t-13 -65.5t-45 -48.5l-49 -42h109v-79z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="280" 
d="M134 222q114 0 114 -86q0 -53 -34 -72q41 -16 41 -75q0 -99 -113 -99l-118 8l6 77q59 -7 96.5 -7t37.5 28q0 26 -35 26h-66v72h65q12 0 20 7t8 19q0 24 -35 24l-87 -6l-6 75q65 9 106 9z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="280" 
d="M159 -100v41h-136v73l52 198h98l-61 -192h47l10 89h77v-89h18v-79h-18v-41h-87z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="280" 
d="M133 26q-16 0 -38 -8l-65 8l10 186h199v-75h-129l-4 -46q28 10 50 10q93 0 93 -99q0 -110 -113 -110q-45 0 -96 10l-17 4l6 71q56 -9 92.5 -9t36.5 29t-25 29z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="280" 
d="M107 98q34 9 47 9q103 0 103 -104q0 -53 -32 -83t-83 -30q-125 0 -125 157q0 89 30.5 132t101.5 43q27 0 81 -8l16 -3l-4 -72q-60 6 -90 6q-45 0 -45 -47zM140 35q-14 0 -24 -5.5t-10 -6.5q2 -57 32.5 -57t30.5 34.5t-29 34.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="280" 
d="M31 125v87h217v-91l-99 -230l-98 16l96 202v16h-116z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="280" 
d="M139 222q119 0 119 -87q0 -30 -11.5 -49.5t-22.5 -22.5q13 -3 26 -21t13 -50q0 -55 -31.5 -78.5t-93 -23.5t-92.5 22.5t-31 77.5q0 31 13.5 50t25.5 24q-11 4 -22.5 21.5t-11.5 49.5q0 87 119 87zM139 -39q34 0 34 32q0 23 -24 36h-20q-24 -14 -24 -37q0 -31 34 -31z
M139 151q-32 0 -32 -27q0 -21 22 -33h20q22 11 22 33q0 27 -32 27z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="280" 
d="M120 -32q45 0 47 45q-25 -8 -47 -8q-49 0 -75 27t-26 79t31 81.5t90 29.5q119 0 119 -165q0 -86 -29.5 -126.5t-98.5 -40.5q-41 0 -86 9l-15 3l5 73q48 -7 85 -7zM115 84q8 -10 21.5 -10t33.5 9q0 62 -33 62q-30 0 -30 -35q0 -16 8 -26z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M28 365v84h62q14 117 69 169.5t173 52.5q77 0 189 -24l-4 -87q-81 15 -160 15t-112 -28.5t-43 -97.5h258v-84h-265v-79h265v-84h-258q11 -64 44 -90.5t104 -26.5t167 14l3 -88q-99 -22 -188 -22q-116 0 -171 51.5t-70 161.5h-63v84h57q-1 14 -1 44v35h-56z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="668" 
d="M85 571v67h200v-67h-52v-239h-74v239h-74zM313 331v307h89l52 -180l56 180h88v-307h-69v196l-48 -178h-50l-49 178v-196h-69z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" 
d="M49 84h99q-4 6 -11 16t-24 42t-30 66q-35 90 -35 173q0 152 52.5 221t179.5 69t179.5 -69t52.5 -221q0 -88 -36 -174q-31 -76 -54 -108l-10 -15h99v-93h-203v81q10 18 25 48t39.5 104.5t24.5 128.5q0 124 -22 172.5t-95 48.5t-95 -48.5t-22 -172.5q0 -54 22.5 -124
t44.5 -113l22 -44v-81h-203v93z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M264 719q129 0 187.5 -88t58.5 -276t-57.5 -276t-186.5 -88q-112 0 -171 56.5t-59 164t56.5 165.5t158.5 58q68 0 123 -20l20 -7q-5 121 -37 169.5t-112 48.5q-32 0 -71 -10t-62 -20l-23 -9l-4 85q85 47 179 47zM394 316q-56 25 -121.5 25t-93 -34t-27.5 -92
q0 -131 114 -131q65 0 96.5 56t31.5 176z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" 
d="M512 0h-464v84l139 576h186l139 -578v-82zM293 568h-26l-110 -475h245z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M356 -172v832h-154v-832h-111v832h-60v97h499v-97h-63v-832h-111z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M36 757h487v-98h-369v-18l233 -301v-77l-233 -318v-20h369v-97h-487v122l250 352l-250 333v122z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M64 202v98h432v-98h-432z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M2 345v98h147l108 -483h12l190 825h110l-221 -918h-174l-115 478h-57z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M161 121q-65 0 -99 43.5t-34 127t33.5 127.5t97.5 44q41 0 70 -21t51 -69q22 48 51 69t70 21q64 0 97.5 -43.5t33.5 -127.5q0 -171 -133 -171q-40 0 -68.5 21t-50.5 68q-23 -50 -50 -69.5t-69 -19.5zM175 369q-47 0 -47 -77t47 -77q23 0 36 17.5t32 59.5q-14 29 -20 40.5
t-19.5 24t-28.5 12.5zM385 215q47 0 47 77t-47 77q-15 0 -28.5 -12.5t-19.5 -24t-20 -40.5q4 -13 9.5 -21t10 -16.5t6.5 -11.5t6.5 -9t8 -8t8.5 -6q7 -5 19 -5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M333 -12q0 -99 -37.5 -140.5t-106.5 -41.5q-27 0 -78 8l-15 3l4 90q31 -3 62.5 -3t45 19t13.5 67v595q0 110 32.5 152.5t111.5 42.5q29 0 83 -8l16 -3l-4 -90q-34 4 -67 4t-46.5 -22t-13.5 -76v-597z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M381.5 267q-27.5 0 -105.5 25.5t-97 25.5t-46 -10.5t-44 -21.5l-17 -10l-10 88q62 52 121 52q29 0 104 -25.5t95 -25.5q36 0 89 31l17 10l9 -87q-18 -18 -53 -35t-62.5 -17zM381.5 62q-27.5 0 -105.5 25.5t-97 25.5t-46 -10.5t-44 -20.5l-17 -11l-10 88q62 52 121 52
q29 0 104 -25.5t95 -25.5t46 10t43 20l17 11l9 -87q-18 -18 -53 -35t-62.5 -17z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M60 301v98h233l68 155l85 -34l-53 -121h105v-98h-148l-44 -100h192v-98h-235l-64 -147l-85 34l49 113h-103v98h146l44 100h-190z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M488 402l-300 -72l300 -79v-106l-422 133v95l422 135v-106zM66 20v98h422v-98h-422z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M372 330l-300 72v106l422 -135v-95l-422 -133v106zM494 118v-98h-422v98h422z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M206 172l-158 154l158 162h149l157 -162l-157 -154h-149zM290 247l112 79l-112 87h-20l-113 -87l113 -79h20z" />
    <glyph glyph-name="dotlessj" unicode="&#xf6be;" horiz-adv-x="242" 
d="M67 19v481h108v-482q0 -98 -34.5 -145t-138.5 -100l-43 81q70 44 89 72t19 93z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="170" 
d="M7 -300l44 223h106l-53 -223h-97z" />
    <glyph glyph-name="questiondown.cap" horiz-adv-x="431" 
d="M30 151q0 67 16.5 102t67.5 75t67.5 63.5t16.5 50.5v33h88q24 -31 24 -78q0 -25 -19 -50t-68 -65.5t-65 -63t-16 -58.5q0 -74 106 -74q50 0 126 14l23 5l6 -83q-93 -33 -169 -33q-105 0 -154.5 38.5t-49.5 123.5zM303 685v-140h-116v140h116z" />
    <glyph glyph-name="endash.cap" horiz-adv-x="624" 
d="M62 343h500v-93h-500v93z" />
    <glyph glyph-name="emdash.cap" horiz-adv-x="1124" 
d="M62 343h1000v-93h-1000v93z" />
    <glyph glyph-name="periodcentered.cap" horiz-adv-x="239" 
d="M61 259v144h117v-144h-117z" />
    <glyph glyph-name="exclamdown.cap" horiz-adv-x="266" 
d="M192 686v-140h-117v140h117zM181 445l10 -444h-116l10 444h96z" />
    <glyph glyph-name="parenleft.cap" horiz-adv-x="289" 
d="M163 331q0 -87 23.5 -193.5t47.5 -169.5l24 -63h-103q-14 23 -36 76.5t-37 101t-27 116t-12 151.5t28 195t56 173l28 62h103q-34 -86 -64.5 -224t-30.5 -225z" />
    <glyph glyph-name="parenright.cap" horiz-adv-x="289" 
d="M126 359q0 87 -23.5 193.5t-47.5 169.5l-24 63h103q14 -23 36 -76.5t37 -101t27 -116t12 -151.5t-28 -195t-56 -173l-28 -62h-103q34 86 64.5 224t30.5 225z" />
    <glyph glyph-name="bracketleft.cap" horiz-adv-x="344" 
d="M306 779v-97h-123v-678h123v-97h-233v872h233z" />
    <glyph glyph-name="bracketright.cap" horiz-adv-x="344" 
d="M38 682v97h233v-872h-233v97h123v678h-123z" />
    <glyph glyph-name="braceleft.cap" horiz-adv-x="357" 
d="M228 605l7 -125q0 -63 -21 -91t-88 -46q66 -17 87.5 -47.5t21.5 -94.5l-7 -117q0 -43 19 -66.5t71 -26.5v-92q-107 4 -151.5 43.5t-44.5 129.5l7 121q0 77 -110 107v85q57 13 83.5 37.5t26.5 63.5l-7 127q0 94 44.5 132.5t152.5 42.5l1 -92q-53 -4 -72.5 -26t-19.5 -65z
" />
    <glyph glyph-name="braceright.cap" horiz-adv-x="358" 
d="M129 79l-7 125q0 63 21 91t88 46q-66 17 -87.5 47.5t-21.5 94.5l7 117q0 43 -19 66.5t-71 26.5v92q107 -4 151.5 -43.5t44.5 -129.5l-7 -121q0 -77 110 -107v-85q-57 -13 -83.5 -37.5t-26.5 -63.5l7 -127q0 -94 -44.5 -132.5t-152.5 -42.5l-1 92q53 4 72.5 26t19.5 65z
" />
    <hkern u1="&#x22;" u2="&#x129;" k="-6" />
    <hkern u1="&#x22;" u2="&#xf0;" k="11" />
    <hkern u1="&#x22;" u2="&#xef;" k="-13" />
    <hkern u1="&#x22;" u2="&#xee;" k="-9" />
    <hkern u1="&#x22;" u2="&#xec;" k="-23" />
    <hkern u1="&#x22;" u2="&#xc6;" k="45" />
    <hkern u1="&#x22;" u2="&#x40;" k="11" />
    <hkern u1="&#x22;" u2="&#x2f;" k="63" />
    <hkern u1="&#x22;" u2="&#x26;" k="24" />
    <hkern u1="&#x26;" u2="&#x201d;" k="40" />
    <hkern u1="&#x26;" u2="&#x2019;" k="40" />
    <hkern u1="&#x26;" u2="&#x1ef9;" k="5" />
    <hkern u1="&#x26;" u2="&#x1ef8;" k="50" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="50" />
    <hkern u1="&#x26;" u2="&#x1e85;" k="4" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="16" />
    <hkern u1="&#x26;" u2="&#x1e83;" k="4" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="16" />
    <hkern u1="&#x26;" u2="&#x1e81;" k="4" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="16" />
    <hkern u1="&#x26;" u2="&#x21b;" k="3" />
    <hkern u1="&#x26;" u2="&#x21a;" k="37" />
    <hkern u1="&#x26;" u2="&#x1fe;" k="3" />
    <hkern u1="&#x26;" u2="&#x178;" k="50" />
    <hkern u1="&#x26;" u2="&#x177;" k="5" />
    <hkern u1="&#x26;" u2="&#x176;" k="50" />
    <hkern u1="&#x26;" u2="&#x175;" k="4" />
    <hkern u1="&#x26;" u2="&#x174;" k="16" />
    <hkern u1="&#x26;" u2="&#x172;" k="3" />
    <hkern u1="&#x26;" u2="&#x170;" k="3" />
    <hkern u1="&#x26;" u2="&#x16e;" k="3" />
    <hkern u1="&#x26;" u2="&#x16c;" k="3" />
    <hkern u1="&#x26;" u2="&#x16a;" k="3" />
    <hkern u1="&#x26;" u2="&#x168;" k="3" />
    <hkern u1="&#x26;" u2="&#x167;" k="3" />
    <hkern u1="&#x26;" u2="&#x166;" k="37" />
    <hkern u1="&#x26;" u2="&#x165;" k="3" />
    <hkern u1="&#x26;" u2="&#x164;" k="37" />
    <hkern u1="&#x26;" u2="&#x152;" k="3" />
    <hkern u1="&#x26;" u2="&#x150;" k="3" />
    <hkern u1="&#x26;" u2="&#x14e;" k="3" />
    <hkern u1="&#x26;" u2="&#x14c;" k="3" />
    <hkern u1="&#x26;" u2="&#x122;" k="3" />
    <hkern u1="&#x26;" u2="&#x120;" k="3" />
    <hkern u1="&#x26;" u2="&#x11e;" k="3" />
    <hkern u1="&#x26;" u2="&#x11c;" k="3" />
    <hkern u1="&#x26;" u2="&#x10c;" k="3" />
    <hkern u1="&#x26;" u2="&#x10a;" k="3" />
    <hkern u1="&#x26;" u2="&#x108;" k="3" />
    <hkern u1="&#x26;" u2="&#x106;" k="3" />
    <hkern u1="&#x26;" u2="&#xff;" k="5" />
    <hkern u1="&#x26;" u2="&#xfd;" k="5" />
    <hkern u1="&#x26;" u2="&#xdd;" k="50" />
    <hkern u1="&#x26;" u2="&#xdc;" k="3" />
    <hkern u1="&#x26;" u2="&#xdb;" k="3" />
    <hkern u1="&#x26;" u2="&#xda;" k="3" />
    <hkern u1="&#x26;" u2="&#xd9;" k="3" />
    <hkern u1="&#x26;" u2="&#xd8;" k="3" />
    <hkern u1="&#x26;" u2="&#xd6;" k="3" />
    <hkern u1="&#x26;" u2="&#xd5;" k="3" />
    <hkern u1="&#x26;" u2="&#xd4;" k="3" />
    <hkern u1="&#x26;" u2="&#xd3;" k="3" />
    <hkern u1="&#x26;" u2="&#xd2;" k="3" />
    <hkern u1="&#x26;" u2="&#xc7;" k="3" />
    <hkern u1="&#x26;" u2="y" k="5" />
    <hkern u1="&#x26;" u2="w" k="4" />
    <hkern u1="&#x26;" u2="v" k="5" />
    <hkern u1="&#x26;" u2="t" k="3" />
    <hkern u1="&#x26;" u2="Y" k="50" />
    <hkern u1="&#x26;" u2="W" k="16" />
    <hkern u1="&#x26;" u2="V" k="27" />
    <hkern u1="&#x26;" u2="U" k="3" />
    <hkern u1="&#x26;" u2="T" k="37" />
    <hkern u1="&#x26;" u2="Q" k="3" />
    <hkern u1="&#x26;" u2="O" k="3" />
    <hkern u1="&#x26;" u2="G" k="3" />
    <hkern u1="&#x26;" u2="C" k="3" />
    <hkern u1="&#x26;" u2="&#x27;" k="43" />
    <hkern u1="&#x26;" u2="&#x22;" k="43" />
    <hkern u1="&#x27;" u2="&#x129;" k="-6" />
    <hkern u1="&#x27;" u2="&#xf0;" k="11" />
    <hkern u1="&#x27;" u2="&#xef;" k="-13" />
    <hkern u1="&#x27;" u2="&#xee;" k="-9" />
    <hkern u1="&#x27;" u2="&#xec;" k="-23" />
    <hkern u1="&#x27;" u2="&#xc6;" k="45" />
    <hkern u1="&#x27;" u2="&#x40;" k="11" />
    <hkern u1="&#x27;" u2="&#x2f;" k="63" />
    <hkern u1="&#x27;" u2="&#x26;" k="24" />
    <hkern u1="&#x28;" u2="&#x1ef9;" k="7" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="7" />
    <hkern u1="&#x28;" u2="&#x1ed7;" k="19" />
    <hkern u1="&#x28;" u2="&#x1ec5;" k="19" />
    <hkern u1="&#x28;" u2="&#x1eab;" k="9" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="10" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="10" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="10" />
    <hkern u1="&#x28;" u2="&#x219;" k="7" />
    <hkern u1="&#x28;" u2="&#x1ff;" k="19" />
    <hkern u1="&#x28;" u2="&#x1fe;" k="14" />
    <hkern u1="&#x28;" u2="&#x1fd;" k="9" />
    <hkern u1="&#x28;" u2="&#x1fb;" k="9" />
    <hkern u1="&#x28;" u2="&#x177;" k="7" />
    <hkern u1="&#x28;" u2="&#x175;" k="10" />
    <hkern u1="&#x28;" u2="&#x173;" k="14" />
    <hkern u1="&#x28;" u2="&#x171;" k="14" />
    <hkern u1="&#x28;" u2="&#x16f;" k="14" />
    <hkern u1="&#x28;" u2="&#x16d;" k="14" />
    <hkern u1="&#x28;" u2="&#x16b;" k="14" />
    <hkern u1="&#x28;" u2="&#x169;" k="14" />
    <hkern u1="&#x28;" u2="&#x161;" k="7" />
    <hkern u1="&#x28;" u2="&#x15f;" k="7" />
    <hkern u1="&#x28;" u2="&#x15d;" k="7" />
    <hkern u1="&#x28;" u2="&#x15b;" k="7" />
    <hkern u1="&#x28;" u2="&#x159;" k="8" />
    <hkern u1="&#x28;" u2="&#x157;" k="8" />
    <hkern u1="&#x28;" u2="&#x155;" k="8" />
    <hkern u1="&#x28;" u2="&#x153;" k="19" />
    <hkern u1="&#x28;" u2="&#x152;" k="14" />
    <hkern u1="&#x28;" u2="&#x151;" k="19" />
    <hkern u1="&#x28;" u2="&#x150;" k="14" />
    <hkern u1="&#x28;" u2="&#x14f;" k="19" />
    <hkern u1="&#x28;" u2="&#x14e;" k="14" />
    <hkern u1="&#x28;" u2="&#x14d;" k="19" />
    <hkern u1="&#x28;" u2="&#x14c;" k="14" />
    <hkern u1="&#x28;" u2="&#x14b;" k="8" />
    <hkern u1="&#x28;" u2="&#x148;" k="8" />
    <hkern u1="&#x28;" u2="&#x146;" k="8" />
    <hkern u1="&#x28;" u2="&#x144;" k="8" />
    <hkern u1="&#x28;" u2="&#x135;" k="-16" />
    <hkern u1="&#x28;" u2="&#x12d;" k="-22" />
    <hkern u1="&#x28;" u2="&#x129;" k="-7" />
    <hkern u1="&#x28;" u2="&#x122;" k="14" />
    <hkern u1="&#x28;" u2="&#x120;" k="14" />
    <hkern u1="&#x28;" u2="&#x11e;" k="14" />
    <hkern u1="&#x28;" u2="&#x11c;" k="14" />
    <hkern u1="&#x28;" u2="&#x11b;" k="19" />
    <hkern u1="&#x28;" u2="&#x119;" k="19" />
    <hkern u1="&#x28;" u2="&#x117;" k="19" />
    <hkern u1="&#x28;" u2="&#x115;" k="19" />
    <hkern u1="&#x28;" u2="&#x113;" k="19" />
    <hkern u1="&#x28;" u2="&#x111;" k="18" />
    <hkern u1="&#x28;" u2="&#x10f;" k="18" />
    <hkern u1="&#x28;" u2="&#x10d;" k="19" />
    <hkern u1="&#x28;" u2="&#x10c;" k="13" />
    <hkern u1="&#x28;" u2="&#x10b;" k="19" />
    <hkern u1="&#x28;" u2="&#x10a;" k="13" />
    <hkern u1="&#x28;" u2="&#x109;" k="19" />
    <hkern u1="&#x28;" u2="&#x108;" k="13" />
    <hkern u1="&#x28;" u2="&#x107;" k="19" />
    <hkern u1="&#x28;" u2="&#x106;" k="13" />
    <hkern u1="&#x28;" u2="&#x105;" k="9" />
    <hkern u1="&#x28;" u2="&#x103;" k="9" />
    <hkern u1="&#x28;" u2="&#x101;" k="9" />
    <hkern u1="&#x28;" u2="&#xff;" k="7" />
    <hkern u1="&#x28;" u2="&#xfd;" k="7" />
    <hkern u1="&#x28;" u2="&#xfc;" k="14" />
    <hkern u1="&#x28;" u2="&#xfb;" k="14" />
    <hkern u1="&#x28;" u2="&#xfa;" k="14" />
    <hkern u1="&#x28;" u2="&#xf9;" k="14" />
    <hkern u1="&#x28;" u2="&#xf8;" k="19" />
    <hkern u1="&#x28;" u2="&#xf6;" k="19" />
    <hkern u1="&#x28;" u2="&#xf5;" k="19" />
    <hkern u1="&#x28;" u2="&#xf4;" k="19" />
    <hkern u1="&#x28;" u2="&#xf3;" k="19" />
    <hkern u1="&#x28;" u2="&#xf2;" k="19" />
    <hkern u1="&#x28;" u2="&#xf1;" k="8" />
    <hkern u1="&#x28;" u2="&#xf0;" k="8" />
    <hkern u1="&#x28;" u2="&#xef;" k="-28" />
    <hkern u1="&#x28;" u2="&#xec;" k="-21" />
    <hkern u1="&#x28;" u2="&#xeb;" k="19" />
    <hkern u1="&#x28;" u2="&#xea;" k="19" />
    <hkern u1="&#x28;" u2="&#xe9;" k="19" />
    <hkern u1="&#x28;" u2="&#xe8;" k="19" />
    <hkern u1="&#x28;" u2="&#xe7;" k="19" />
    <hkern u1="&#x28;" u2="&#xe6;" k="9" />
    <hkern u1="&#x28;" u2="&#xe5;" k="9" />
    <hkern u1="&#x28;" u2="&#xe4;" k="9" />
    <hkern u1="&#x28;" u2="&#xe3;" k="9" />
    <hkern u1="&#x28;" u2="&#xe2;" k="9" />
    <hkern u1="&#x28;" u2="&#xe1;" k="9" />
    <hkern u1="&#x28;" u2="&#xe0;" k="9" />
    <hkern u1="&#x28;" u2="&#xd8;" k="14" />
    <hkern u1="&#x28;" u2="&#xd6;" k="14" />
    <hkern u1="&#x28;" u2="&#xd5;" k="14" />
    <hkern u1="&#x28;" u2="&#xd4;" k="14" />
    <hkern u1="&#x28;" u2="&#xd3;" k="14" />
    <hkern u1="&#x28;" u2="&#xd2;" k="14" />
    <hkern u1="&#x28;" u2="&#xc7;" k="13" />
    <hkern u1="&#x28;" u2="&#x7b;" k="12" />
    <hkern u1="&#x28;" u2="y" k="7" />
    <hkern u1="&#x28;" u2="w" k="10" />
    <hkern u1="&#x28;" u2="v" k="7" />
    <hkern u1="&#x28;" u2="u" k="14" />
    <hkern u1="&#x28;" u2="s" k="7" />
    <hkern u1="&#x28;" u2="r" k="8" />
    <hkern u1="&#x28;" u2="q" k="18" />
    <hkern u1="&#x28;" u2="p" k="8" />
    <hkern u1="&#x28;" u2="o" k="19" />
    <hkern u1="&#x28;" u2="n" k="8" />
    <hkern u1="&#x28;" u2="m" k="8" />
    <hkern u1="&#x28;" u2="j" k="-16" />
    <hkern u1="&#x28;" u2="f" k="7" />
    <hkern u1="&#x28;" u2="e" k="19" />
    <hkern u1="&#x28;" u2="d" k="18" />
    <hkern u1="&#x28;" u2="c" k="19" />
    <hkern u1="&#x28;" u2="a" k="9" />
    <hkern u1="&#x28;" u2="Q" k="14" />
    <hkern u1="&#x28;" u2="O" k="14" />
    <hkern u1="&#x28;" u2="G" k="14" />
    <hkern u1="&#x28;" u2="C" k="13" />
    <hkern u1="&#x29;" u2="&#x7d;" k="4" />
    <hkern u1="&#x29;" u2="]" k="4" />
    <hkern u1="&#x2a;" u2="&#x1ef9;" k="-17" />
    <hkern u1="&#x2a;" u2="&#x1ed7;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1ec5;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1eb0;" k="33" />
    <hkern u1="&#x2a;" u2="&#x21a;" k="-7" />
    <hkern u1="&#x2a;" u2="&#x219;" k="11" />
    <hkern u1="&#x2a;" u2="&#x1ff;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1fc;" k="33" />
    <hkern u1="&#x2a;" u2="&#x1fa;" k="33" />
    <hkern u1="&#x2a;" u2="&#x17d;" k="5" />
    <hkern u1="&#x2a;" u2="&#x17b;" k="5" />
    <hkern u1="&#x2a;" u2="&#x179;" k="5" />
    <hkern u1="&#x2a;" u2="&#x167;" k="-14" />
    <hkern u1="&#x2a;" u2="&#x166;" k="-7" />
    <hkern u1="&#x2a;" u2="&#x165;" k="-13" />
    <hkern u1="&#x2a;" u2="&#x164;" k="-7" />
    <hkern u1="&#x2a;" u2="&#x161;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15f;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15d;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15b;" k="11" />
    <hkern u1="&#x2a;" u2="&#x153;" k="16" />
    <hkern u1="&#x2a;" u2="&#x151;" k="16" />
    <hkern u1="&#x2a;" u2="&#x14f;" k="16" />
    <hkern u1="&#x2a;" u2="&#x14d;" k="16" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-34" />
    <hkern u1="&#x2a;" u2="&#x134;" k="15" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-18" />
    <hkern u1="&#x2a;" u2="&#x123;" k="14" />
    <hkern u1="&#x2a;" u2="&#x121;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11f;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11d;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11b;" k="16" />
    <hkern u1="&#x2a;" u2="&#x119;" k="16" />
    <hkern u1="&#x2a;" u2="&#x117;" k="16" />
    <hkern u1="&#x2a;" u2="&#x115;" k="16" />
    <hkern u1="&#x2a;" u2="&#x113;" k="16" />
    <hkern u1="&#x2a;" u2="&#x111;" k="19" />
    <hkern u1="&#x2a;" u2="&#x10f;" k="19" />
    <hkern u1="&#x2a;" u2="&#x10d;" k="16" />
    <hkern u1="&#x2a;" u2="&#x10b;" k="16" />
    <hkern u1="&#x2a;" u2="&#x109;" k="16" />
    <hkern u1="&#x2a;" u2="&#x107;" k="16" />
    <hkern u1="&#x2a;" u2="&#x104;" k="33" />
    <hkern u1="&#x2a;" u2="&#x102;" k="33" />
    <hkern u1="&#x2a;" u2="&#x100;" k="33" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="15" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-30" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-39" />
    <hkern u1="&#x2a;" u2="&#xec;" k="-18" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="16" />
    <hkern u1="&#x2a;" u2="&#xea;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="16" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="41" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="33" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="33" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="33" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="33" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="33" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="33" />
    <hkern u1="&#x2a;" u2="s" k="11" />
    <hkern u1="&#x2a;" u2="q" k="19" />
    <hkern u1="&#x2a;" u2="o" k="16" />
    <hkern u1="&#x2a;" u2="g" k="14" />
    <hkern u1="&#x2a;" u2="e" k="16" />
    <hkern u1="&#x2a;" u2="d" k="19" />
    <hkern u1="&#x2a;" u2="c" k="16" />
    <hkern u1="&#x2a;" u2="Z" k="5" />
    <hkern u1="&#x2a;" u2="T" k="-7" />
    <hkern u1="&#x2a;" u2="J" k="15" />
    <hkern u1="&#x2a;" u2="A" k="33" />
    <hkern u1="&#x2c;" u2="v" k="28" />
    <hkern u1="&#x2c;" u2="f" k="10" />
    <hkern u1="&#x2c;" u2="V" k="45" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="13" />
    <hkern u1="&#x2d;" u2="x" k="26" />
    <hkern u1="&#x2d;" u2="v" k="11" />
    <hkern u1="&#x2d;" u2="f" k="11" />
    <hkern u1="&#x2d;" u2="X" k="35" />
    <hkern u1="&#x2d;" u2="V" k="27" />
    <hkern u1="&#x2e;" u2="v" k="28" />
    <hkern u1="&#x2e;" u2="f" k="10" />
    <hkern u1="&#x2e;" u2="V" k="45" />
    <hkern u1="&#x2f;" u2="&#x1ef9;" k="10" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x2f;" u2="&#x1ed7;" k="35" />
    <hkern u1="&#x2f;" u2="&#x1ec5;" k="35" />
    <hkern u1="&#x2f;" u2="&#x1eb0;" k="40" />
    <hkern u1="&#x2f;" u2="&#x1eab;" k="25" />
    <hkern u1="&#x2f;" u2="&#x1e85;" k="9" />
    <hkern u1="&#x2f;" u2="&#x1e83;" k="9" />
    <hkern u1="&#x2f;" u2="&#x1e81;" k="9" />
    <hkern u1="&#x2f;" u2="&#x219;" k="28" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="35" />
    <hkern u1="&#x2f;" u2="&#x1fe;" k="11" />
    <hkern u1="&#x2f;" u2="&#x1fd;" k="25" />
    <hkern u1="&#x2f;" u2="&#x1fc;" k="40" />
    <hkern u1="&#x2f;" u2="&#x1fb;" k="25" />
    <hkern u1="&#x2f;" u2="&#x1fa;" k="40" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="13" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="13" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="13" />
    <hkern u1="&#x2f;" u2="&#x177;" k="10" />
    <hkern u1="&#x2f;" u2="&#x175;" k="9" />
    <hkern u1="&#x2f;" u2="&#x173;" k="19" />
    <hkern u1="&#x2f;" u2="&#x171;" k="19" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="19" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="19" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="19" />
    <hkern u1="&#x2f;" u2="&#x169;" k="19" />
    <hkern u1="&#x2f;" u2="&#x161;" k="28" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="28" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="28" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="28" />
    <hkern u1="&#x2f;" u2="&#x159;" k="21" />
    <hkern u1="&#x2f;" u2="&#x157;" k="21" />
    <hkern u1="&#x2f;" u2="&#x155;" k="21" />
    <hkern u1="&#x2f;" u2="&#x153;" k="35" />
    <hkern u1="&#x2f;" u2="&#x152;" k="11" />
    <hkern u1="&#x2f;" u2="&#x151;" k="35" />
    <hkern u1="&#x2f;" u2="&#x150;" k="11" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="35" />
    <hkern u1="&#x2f;" u2="&#x14e;" k="11" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="35" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="11" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="21" />
    <hkern u1="&#x2f;" u2="&#x148;" k="21" />
    <hkern u1="&#x2f;" u2="&#x146;" k="21" />
    <hkern u1="&#x2f;" u2="&#x144;" k="21" />
    <hkern u1="&#x2f;" u2="&#x134;" k="16" />
    <hkern u1="&#x2f;" u2="&#x12d;" k="-20" />
    <hkern u1="&#x2f;" u2="&#x12b;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x129;" k="-32" />
    <hkern u1="&#x2f;" u2="&#x123;" k="33" />
    <hkern u1="&#x2f;" u2="&#x122;" k="11" />
    <hkern u1="&#x2f;" u2="&#x121;" k="33" />
    <hkern u1="&#x2f;" u2="&#x120;" k="11" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="33" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="11" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="33" />
    <hkern u1="&#x2f;" u2="&#x11c;" k="11" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="35" />
    <hkern u1="&#x2f;" u2="&#x119;" k="35" />
    <hkern u1="&#x2f;" u2="&#x117;" k="35" />
    <hkern u1="&#x2f;" u2="&#x115;" k="35" />
    <hkern u1="&#x2f;" u2="&#x113;" k="35" />
    <hkern u1="&#x2f;" u2="&#x111;" k="35" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="35" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="35" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="9" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="35" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="9" />
    <hkern u1="&#x2f;" u2="&#x109;" k="35" />
    <hkern u1="&#x2f;" u2="&#x108;" k="9" />
    <hkern u1="&#x2f;" u2="&#x107;" k="35" />
    <hkern u1="&#x2f;" u2="&#x106;" k="9" />
    <hkern u1="&#x2f;" u2="&#x105;" k="25" />
    <hkern u1="&#x2f;" u2="&#x104;" k="40" />
    <hkern u1="&#x2f;" u2="&#x103;" k="25" />
    <hkern u1="&#x2f;" u2="&#x102;" k="40" />
    <hkern u1="&#x2f;" u2="&#x101;" k="25" />
    <hkern u1="&#x2f;" u2="&#x100;" k="40" />
    <hkern u1="&#x2f;" u2="&#xff;" k="10" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="10" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="19" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="19" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="35" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="21" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="12" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-36" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-42" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="35" />
    <hkern u1="&#x2f;" u2="&#xea;" k="35" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="35" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="35" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="35" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="25" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="25" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="11" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="11" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="9" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="47" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="40" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="40" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="40" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="40" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="40" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="40" />
    <hkern u1="&#x2f;" u2="z" k="13" />
    <hkern u1="&#x2f;" u2="y" k="10" />
    <hkern u1="&#x2f;" u2="w" k="9" />
    <hkern u1="&#x2f;" u2="v" k="10" />
    <hkern u1="&#x2f;" u2="u" k="19" />
    <hkern u1="&#x2f;" u2="s" k="28" />
    <hkern u1="&#x2f;" u2="r" k="21" />
    <hkern u1="&#x2f;" u2="q" k="35" />
    <hkern u1="&#x2f;" u2="p" k="21" />
    <hkern u1="&#x2f;" u2="o" k="35" />
    <hkern u1="&#x2f;" u2="n" k="21" />
    <hkern u1="&#x2f;" u2="m" k="21" />
    <hkern u1="&#x2f;" u2="g" k="33" />
    <hkern u1="&#x2f;" u2="e" k="35" />
    <hkern u1="&#x2f;" u2="d" k="35" />
    <hkern u1="&#x2f;" u2="c" k="35" />
    <hkern u1="&#x2f;" u2="a" k="25" />
    <hkern u1="&#x2f;" u2="Q" k="11" />
    <hkern u1="&#x2f;" u2="O" k="11" />
    <hkern u1="&#x2f;" u2="J" k="16" />
    <hkern u1="&#x2f;" u2="G" k="11" />
    <hkern u1="&#x2f;" u2="C" k="9" />
    <hkern u1="&#x2f;" u2="A" k="40" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="292" />
    <hkern u1="&#x3a;" u2="V" k="12" />
    <hkern u1="&#x3b;" u2="V" k="12" />
    <hkern u1="&#x40;" u2="&#x1ef8;" k="27" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="27" />
    <hkern u1="&#x40;" u2="&#x21a;" k="7" />
    <hkern u1="&#x40;" u2="&#x178;" k="27" />
    <hkern u1="&#x40;" u2="&#x176;" k="27" />
    <hkern u1="&#x40;" u2="&#x166;" k="7" />
    <hkern u1="&#x40;" u2="&#x164;" k="7" />
    <hkern u1="&#x40;" u2="&#x134;" k="3" />
    <hkern u1="&#x40;" u2="&#xdd;" k="27" />
    <hkern u1="&#x40;" u2="&#xc6;" k="3" />
    <hkern u1="&#x40;" u2="Y" k="27" />
    <hkern u1="&#x40;" u2="V" k="3" />
    <hkern u1="&#x40;" u2="T" k="7" />
    <hkern u1="&#x40;" u2="J" k="3" />
    <hkern u1="A" g2="braceright.cap" k="6" />
    <hkern u1="A" g2="bracketright.cap" k="8" />
    <hkern u1="A" u2="&#x2122;" k="35" />
    <hkern u1="A" u2="&#xf0;" k="5" />
    <hkern u1="A" u2="&#xae;" k="21" />
    <hkern u1="A" u2="&#x7d;" k="9" />
    <hkern u1="A" u2="v" k="16" />
    <hkern u1="A" u2="f" k="8" />
    <hkern u1="A" u2="]" k="10" />
    <hkern u1="A" u2="\" k="44" />
    <hkern u1="A" u2="V" k="28" />
    <hkern u1="A" u2="&#x3f;" k="19" />
    <hkern u1="A" u2="&#x2a;" k="31" />
    <hkern u1="B" g2="braceright.cap" k="9" />
    <hkern u1="B" g2="bracketright.cap" k="20" />
    <hkern u1="B" u2="&#x1ef8;" k="23" />
    <hkern u1="B" u2="&#x1ef2;" k="23" />
    <hkern u1="B" u2="&#x1eb0;" k="9" />
    <hkern u1="B" u2="&#x21a;" k="7" />
    <hkern u1="B" u2="&#x1fc;" k="9" />
    <hkern u1="B" u2="&#x1fa;" k="9" />
    <hkern u1="B" u2="&#x178;" k="23" />
    <hkern u1="B" u2="&#x176;" k="23" />
    <hkern u1="B" u2="&#x166;" k="7" />
    <hkern u1="B" u2="&#x164;" k="7" />
    <hkern u1="B" u2="&#x134;" k="7" />
    <hkern u1="B" u2="&#x123;" k="8" />
    <hkern u1="B" u2="&#x121;" k="8" />
    <hkern u1="B" u2="&#x11f;" k="8" />
    <hkern u1="B" u2="&#x11d;" k="8" />
    <hkern u1="B" u2="&#x104;" k="9" />
    <hkern u1="B" u2="&#x102;" k="9" />
    <hkern u1="B" u2="&#x100;" k="9" />
    <hkern u1="B" u2="&#xdd;" k="23" />
    <hkern u1="B" u2="&#xc6;" k="12" />
    <hkern u1="B" u2="&#xc5;" k="9" />
    <hkern u1="B" u2="&#xc4;" k="9" />
    <hkern u1="B" u2="&#xc3;" k="9" />
    <hkern u1="B" u2="&#xc2;" k="9" />
    <hkern u1="B" u2="&#xc1;" k="9" />
    <hkern u1="B" u2="&#xc0;" k="9" />
    <hkern u1="B" u2="&#x7d;" k="9" />
    <hkern u1="B" u2="g" k="8" />
    <hkern u1="B" u2="]" k="19" />
    <hkern u1="B" u2="\" k="12" />
    <hkern u1="B" u2="Y" k="23" />
    <hkern u1="B" u2="X" k="12" />
    <hkern u1="B" u2="V" k="10" />
    <hkern u1="B" u2="T" k="7" />
    <hkern u1="B" u2="J" k="7" />
    <hkern u1="B" u2="A" k="9" />
    <hkern u1="B" u2="&#x3f;" k="3" />
    <hkern u1="C" u2="&#x135;" k="-16" />
    <hkern u1="C" u2="&#x12d;" k="-12" />
    <hkern u1="C" u2="&#x12b;" k="-4" />
    <hkern u1="C" u2="&#x129;" k="-26" />
    <hkern u1="C" u2="&#xf0;" k="7" />
    <hkern u1="C" u2="&#xef;" k="-26" />
    <hkern u1="C" u2="&#xee;" k="-21" />
    <hkern u1="C" u2="&#xec;" k="-39" />
    <hkern u1="C" u2="&#xae;" k="5" />
    <hkern u1="C" u2="v" k="10" />
    <hkern u1="C" u2="f" k="5" />
    <hkern u1="D" g2="braceright.cap" k="19" />
    <hkern u1="D" g2="bracketright.cap" k="25" />
    <hkern u1="D" g2="parenright.cap" k="13" />
    <hkern u1="D" u2="&#xc6;" k="15" />
    <hkern u1="D" u2="&#x7d;" k="18" />
    <hkern u1="D" u2="]" k="23" />
    <hkern u1="D" u2="\" k="13" />
    <hkern u1="D" u2="X" k="19" />
    <hkern u1="D" u2="V" k="11" />
    <hkern u1="D" u2="&#x3f;" k="4" />
    <hkern u1="D" u2="&#x2f;" k="11" />
    <hkern u1="D" u2="&#x29;" k="12" />
    <hkern u1="E" u2="&#x135;" k="-16" />
    <hkern u1="E" u2="&#x12d;" k="-8" />
    <hkern u1="E" u2="&#x12b;" k="-3" />
    <hkern u1="E" u2="&#x129;" k="-23" />
    <hkern u1="E" u2="&#xf0;" k="7" />
    <hkern u1="E" u2="&#xef;" k="-24" />
    <hkern u1="E" u2="&#xee;" k="-21" />
    <hkern u1="E" u2="&#xec;" k="-39" />
    <hkern u1="E" u2="v" k="5" />
    <hkern u1="F" g2="emdash.cap" k="7" />
    <hkern u1="F" g2="endash.cap" k="7" />
    <hkern u1="F" u2="&#x2026;" k="59" />
    <hkern u1="F" u2="&#x201e;" k="59" />
    <hkern u1="F" u2="&#x201a;" k="59" />
    <hkern u1="F" u2="&#x2014;" k="7" />
    <hkern u1="F" u2="&#x2013;" k="7" />
    <hkern u1="F" u2="&#x1ef9;" k="9" />
    <hkern u1="F" u2="&#x1ef3;" k="9" />
    <hkern u1="F" u2="&#x1ed7;" k="17" />
    <hkern u1="F" u2="&#x1ec5;" k="17" />
    <hkern u1="F" u2="&#x1eb0;" k="26" />
    <hkern u1="F" u2="&#x1eab;" k="24" />
    <hkern u1="F" u2="&#x1e85;" k="9" />
    <hkern u1="F" u2="&#x1e83;" k="9" />
    <hkern u1="F" u2="&#x1e81;" k="9" />
    <hkern u1="F" u2="&#x21b;" k="3" />
    <hkern u1="F" u2="&#x219;" k="17" />
    <hkern u1="F" u2="&#x218;" k="4" />
    <hkern u1="F" u2="&#x1ff;" k="17" />
    <hkern u1="F" u2="&#x1fe;" k="4" />
    <hkern u1="F" u2="&#x1fd;" k="24" />
    <hkern u1="F" u2="&#x1fc;" k="26" />
    <hkern u1="F" u2="&#x1fb;" k="24" />
    <hkern u1="F" u2="&#x1fa;" k="26" />
    <hkern u1="F" u2="&#x17e;" k="14" />
    <hkern u1="F" u2="&#x17c;" k="14" />
    <hkern u1="F" u2="&#x17a;" k="14" />
    <hkern u1="F" u2="&#x177;" k="9" />
    <hkern u1="F" u2="&#x175;" k="9" />
    <hkern u1="F" u2="&#x173;" k="15" />
    <hkern u1="F" u2="&#x171;" k="15" />
    <hkern u1="F" u2="&#x16f;" k="15" />
    <hkern u1="F" u2="&#x16d;" k="15" />
    <hkern u1="F" u2="&#x16b;" k="15" />
    <hkern u1="F" u2="&#x169;" k="15" />
    <hkern u1="F" u2="&#x167;" k="3" />
    <hkern u1="F" u2="&#x165;" k="3" />
    <hkern u1="F" u2="&#x161;" k="17" />
    <hkern u1="F" u2="&#x160;" k="4" />
    <hkern u1="F" u2="&#x15f;" k="17" />
    <hkern u1="F" u2="&#x15e;" k="4" />
    <hkern u1="F" u2="&#x15d;" k="17" />
    <hkern u1="F" u2="&#x15c;" k="4" />
    <hkern u1="F" u2="&#x15b;" k="17" />
    <hkern u1="F" u2="&#x15a;" k="4" />
    <hkern u1="F" u2="&#x159;" k="19" />
    <hkern u1="F" u2="&#x157;" k="19" />
    <hkern u1="F" u2="&#x155;" k="19" />
    <hkern u1="F" u2="&#x153;" k="17" />
    <hkern u1="F" u2="&#x152;" k="4" />
    <hkern u1="F" u2="&#x151;" k="17" />
    <hkern u1="F" u2="&#x150;" k="4" />
    <hkern u1="F" u2="&#x14f;" k="17" />
    <hkern u1="F" u2="&#x14e;" k="4" />
    <hkern u1="F" u2="&#x14d;" k="17" />
    <hkern u1="F" u2="&#x14c;" k="4" />
    <hkern u1="F" u2="&#x14b;" k="19" />
    <hkern u1="F" u2="&#x148;" k="19" />
    <hkern u1="F" u2="&#x146;" k="19" />
    <hkern u1="F" u2="&#x144;" k="19" />
    <hkern u1="F" u2="&#x135;" k="-29" />
    <hkern u1="F" u2="&#x134;" k="14" />
    <hkern u1="F" u2="&#x131;" k="19" />
    <hkern u1="F" u2="&#x12d;" k="-26" />
    <hkern u1="F" u2="&#x12b;" k="-19" />
    <hkern u1="F" u2="&#x129;" k="-41" />
    <hkern u1="F" u2="&#x123;" k="21" />
    <hkern u1="F" u2="&#x122;" k="4" />
    <hkern u1="F" u2="&#x121;" k="21" />
    <hkern u1="F" u2="&#x120;" k="4" />
    <hkern u1="F" u2="&#x11f;" k="21" />
    <hkern u1="F" u2="&#x11e;" k="4" />
    <hkern u1="F" u2="&#x11d;" k="21" />
    <hkern u1="F" u2="&#x11c;" k="4" />
    <hkern u1="F" u2="&#x11b;" k="17" />
    <hkern u1="F" u2="&#x119;" k="17" />
    <hkern u1="F" u2="&#x117;" k="17" />
    <hkern u1="F" u2="&#x115;" k="17" />
    <hkern u1="F" u2="&#x113;" k="17" />
    <hkern u1="F" u2="&#x111;" k="19" />
    <hkern u1="F" u2="&#x10f;" k="19" />
    <hkern u1="F" u2="&#x10d;" k="17" />
    <hkern u1="F" u2="&#x10c;" k="4" />
    <hkern u1="F" u2="&#x10b;" k="17" />
    <hkern u1="F" u2="&#x10a;" k="4" />
    <hkern u1="F" u2="&#x109;" k="17" />
    <hkern u1="F" u2="&#x108;" k="4" />
    <hkern u1="F" u2="&#x107;" k="17" />
    <hkern u1="F" u2="&#x106;" k="4" />
    <hkern u1="F" u2="&#x105;" k="24" />
    <hkern u1="F" u2="&#x104;" k="26" />
    <hkern u1="F" u2="&#x103;" k="24" />
    <hkern u1="F" u2="&#x102;" k="26" />
    <hkern u1="F" u2="&#x101;" k="24" />
    <hkern u1="F" u2="&#x100;" k="26" />
    <hkern u1="F" u2="&#xff;" k="9" />
    <hkern u1="F" u2="&#xfd;" k="9" />
    <hkern u1="F" u2="&#xfc;" k="15" />
    <hkern u1="F" u2="&#xfb;" k="15" />
    <hkern u1="F" u2="&#xfa;" k="15" />
    <hkern u1="F" u2="&#xf9;" k="15" />
    <hkern u1="F" u2="&#xf8;" k="17" />
    <hkern u1="F" u2="&#xf6;" k="17" />
    <hkern u1="F" u2="&#xf5;" k="17" />
    <hkern u1="F" u2="&#xf4;" k="17" />
    <hkern u1="F" u2="&#xf3;" k="17" />
    <hkern u1="F" u2="&#xf2;" k="17" />
    <hkern u1="F" u2="&#xf1;" k="19" />
    <hkern u1="F" u2="&#xf0;" k="16" />
    <hkern u1="F" u2="&#xef;" k="-40" />
    <hkern u1="F" u2="&#xee;" k="-34" />
    <hkern u1="F" u2="&#xec;" k="-57" />
    <hkern u1="F" u2="&#xeb;" k="17" />
    <hkern u1="F" u2="&#xea;" k="17" />
    <hkern u1="F" u2="&#xe9;" k="17" />
    <hkern u1="F" u2="&#xe8;" k="17" />
    <hkern u1="F" u2="&#xe7;" k="17" />
    <hkern u1="F" u2="&#xe6;" k="24" />
    <hkern u1="F" u2="&#xe5;" k="24" />
    <hkern u1="F" u2="&#xe4;" k="24" />
    <hkern u1="F" u2="&#xe3;" k="24" />
    <hkern u1="F" u2="&#xe2;" k="24" />
    <hkern u1="F" u2="&#xe1;" k="24" />
    <hkern u1="F" u2="&#xe0;" k="24" />
    <hkern u1="F" u2="&#xd8;" k="4" />
    <hkern u1="F" u2="&#xd6;" k="4" />
    <hkern u1="F" u2="&#xd5;" k="4" />
    <hkern u1="F" u2="&#xd4;" k="4" />
    <hkern u1="F" u2="&#xd3;" k="4" />
    <hkern u1="F" u2="&#xd2;" k="4" />
    <hkern u1="F" u2="&#xc7;" k="4" />
    <hkern u1="F" u2="&#xc6;" k="37" />
    <hkern u1="F" u2="&#xc5;" k="26" />
    <hkern u1="F" u2="&#xc4;" k="26" />
    <hkern u1="F" u2="&#xc3;" k="26" />
    <hkern u1="F" u2="&#xc2;" k="26" />
    <hkern u1="F" u2="&#xc1;" k="26" />
    <hkern u1="F" u2="&#xc0;" k="26" />
    <hkern u1="F" u2="z" k="14" />
    <hkern u1="F" u2="y" k="9" />
    <hkern u1="F" u2="x" k="14" />
    <hkern u1="F" u2="w" k="9" />
    <hkern u1="F" u2="v" k="5" />
    <hkern u1="F" u2="u" k="15" />
    <hkern u1="F" u2="t" k="3" />
    <hkern u1="F" u2="s" k="17" />
    <hkern u1="F" u2="r" k="19" />
    <hkern u1="F" u2="q" k="19" />
    <hkern u1="F" u2="p" k="19" />
    <hkern u1="F" u2="o" k="17" />
    <hkern u1="F" u2="n" k="19" />
    <hkern u1="F" u2="m" k="19" />
    <hkern u1="F" u2="g" k="21" />
    <hkern u1="F" u2="f" k="6" />
    <hkern u1="F" u2="e" k="17" />
    <hkern u1="F" u2="d" k="19" />
    <hkern u1="F" u2="c" k="17" />
    <hkern u1="F" u2="a" k="24" />
    <hkern u1="F" u2="S" k="4" />
    <hkern u1="F" u2="Q" k="4" />
    <hkern u1="F" u2="O" k="4" />
    <hkern u1="F" u2="J" k="14" />
    <hkern u1="F" u2="G" k="4" />
    <hkern u1="F" u2="C" k="4" />
    <hkern u1="F" u2="A" k="26" />
    <hkern u1="F" u2="&#x2f;" k="35" />
    <hkern u1="F" u2="&#x2e;" k="59" />
    <hkern u1="F" u2="&#x2d;" k="7" />
    <hkern u1="F" u2="&#x2c;" k="59" />
    <hkern u1="G" g2="braceright.cap" k="4" />
    <hkern u1="G" g2="bracketright.cap" k="5" />
    <hkern u1="G" u2="&#xef;" k="-11" />
    <hkern u1="G" u2="&#xee;" k="-7" />
    <hkern u1="G" u2="&#xec;" k="-21" />
    <hkern u1="G" u2="v" k="6" />
    <hkern u1="G" u2="f" k="6" />
    <hkern u1="G" u2="\" k="7" />
    <hkern u1="G" u2="V" k="9" />
    <hkern u1="H" g2="braceright.cap" k="4" />
    <hkern u1="H" g2="bracketright.cap" k="4" />
    <hkern u1="H" u2="&#xf0;" k="6" />
    <hkern u1="H" u2="&#xec;" k="-6" />
    <hkern u1="H" u2="f" k="4" />
    <hkern u1="I" g2="braceright.cap" k="4" />
    <hkern u1="I" g2="bracketright.cap" k="4" />
    <hkern u1="I" u2="&#xf0;" k="6" />
    <hkern u1="I" u2="&#xec;" k="-6" />
    <hkern u1="I" u2="f" k="4" />
    <hkern u1="J" u2="&#xf0;" k="5" />
    <hkern u1="J" u2="&#xec;" k="-8" />
    <hkern u1="J" u2="f" k="4" />
    <hkern u1="K" u2="&#x12d;" k="-27" />
    <hkern u1="K" u2="&#x12b;" k="-15" />
    <hkern u1="K" u2="&#x129;" k="-33" />
    <hkern u1="K" u2="&#xf0;" k="11" />
    <hkern u1="K" u2="&#xef;" k="-41" />
    <hkern u1="K" u2="&#xee;" k="-4" />
    <hkern u1="K" u2="&#xec;" k="-48" />
    <hkern u1="K" u2="&#xae;" k="5" />
    <hkern u1="K" u2="v" k="20" />
    <hkern u1="K" u2="f" k="8" />
    <hkern u1="L" g2="bracketright.cap" k="4" />
    <hkern u1="L" g2="periodcentered.cap" k="73" />
    <hkern u1="L" u2="&#x2122;" k="82" />
    <hkern u1="L" u2="&#xb7;" k="24" />
    <hkern u1="L" u2="&#xae;" k="71" />
    <hkern u1="L" u2="&#x7d;" k="5" />
    <hkern u1="L" u2="v" k="38" />
    <hkern u1="L" u2="f" k="6" />
    <hkern u1="L" u2="]" k="6" />
    <hkern u1="L" u2="\" k="71" />
    <hkern u1="L" u2="V" k="57" />
    <hkern u1="L" u2="&#x3f;" k="6" />
    <hkern u1="L" u2="&#x2a;" k="81" />
    <hkern u1="M" g2="braceright.cap" k="4" />
    <hkern u1="M" g2="bracketright.cap" k="4" />
    <hkern u1="M" u2="&#xf0;" k="6" />
    <hkern u1="M" u2="&#xec;" k="-6" />
    <hkern u1="M" u2="f" k="4" />
    <hkern u1="N" g2="braceright.cap" k="4" />
    <hkern u1="N" g2="bracketright.cap" k="4" />
    <hkern u1="N" u2="&#xf0;" k="6" />
    <hkern u1="N" u2="&#xec;" k="-6" />
    <hkern u1="N" u2="f" k="4" />
    <hkern u1="O" g2="braceright.cap" k="19" />
    <hkern u1="O" g2="bracketright.cap" k="24" />
    <hkern u1="O" g2="parenright.cap" k="13" />
    <hkern u1="O" u2="&#xc6;" k="14" />
    <hkern u1="O" u2="&#x7d;" k="17" />
    <hkern u1="O" u2="]" k="24" />
    <hkern u1="O" u2="\" k="14" />
    <hkern u1="O" u2="X" k="18" />
    <hkern u1="O" u2="V" k="11" />
    <hkern u1="O" u2="&#x3f;" k="3" />
    <hkern u1="O" u2="&#x2f;" k="10" />
    <hkern u1="O" u2="&#x29;" k="10" />
    <hkern u1="P" g2="braceright.cap" k="18" />
    <hkern u1="P" g2="bracketright.cap" k="26" />
    <hkern u1="P" g2="parenright.cap" k="10" />
    <hkern u1="P" u2="&#x2039;" k="4" />
    <hkern u1="P" u2="&#x2026;" k="70" />
    <hkern u1="P" u2="&#x201e;" k="70" />
    <hkern u1="P" u2="&#x201a;" k="70" />
    <hkern u1="P" u2="&#x2014;" k="4" />
    <hkern u1="P" u2="&#x2013;" k="4" />
    <hkern u1="P" u2="&#x1ef8;" k="20" />
    <hkern u1="P" u2="&#x1ef2;" k="20" />
    <hkern u1="P" u2="&#x1eb0;" k="24" />
    <hkern u1="P" u2="&#x1eab;" k="5" />
    <hkern u1="P" u2="&#x1fd;" k="5" />
    <hkern u1="P" u2="&#x1fc;" k="24" />
    <hkern u1="P" u2="&#x1fb;" k="5" />
    <hkern u1="P" u2="&#x1fa;" k="24" />
    <hkern u1="P" u2="&#x17d;" k="3" />
    <hkern u1="P" u2="&#x17b;" k="3" />
    <hkern u1="P" u2="&#x179;" k="3" />
    <hkern u1="P" u2="&#x178;" k="20" />
    <hkern u1="P" u2="&#x176;" k="20" />
    <hkern u1="P" u2="&#x135;" k="-8" />
    <hkern u1="P" u2="&#x134;" k="16" />
    <hkern u1="P" u2="&#x105;" k="5" />
    <hkern u1="P" u2="&#x104;" k="24" />
    <hkern u1="P" u2="&#x103;" k="5" />
    <hkern u1="P" u2="&#x102;" k="24" />
    <hkern u1="P" u2="&#x101;" k="5" />
    <hkern u1="P" u2="&#x100;" k="24" />
    <hkern u1="P" u2="&#xf0;" k="14" />
    <hkern u1="P" u2="&#xef;" k="-6" />
    <hkern u1="P" u2="&#xee;" k="-12" />
    <hkern u1="P" u2="&#xec;" k="-8" />
    <hkern u1="P" u2="&#xe6;" k="5" />
    <hkern u1="P" u2="&#xe5;" k="5" />
    <hkern u1="P" u2="&#xe4;" k="5" />
    <hkern u1="P" u2="&#xe3;" k="5" />
    <hkern u1="P" u2="&#xe2;" k="5" />
    <hkern u1="P" u2="&#xe1;" k="5" />
    <hkern u1="P" u2="&#xe0;" k="5" />
    <hkern u1="P" u2="&#xdd;" k="20" />
    <hkern u1="P" u2="&#xc6;" k="30" />
    <hkern u1="P" u2="&#xc5;" k="24" />
    <hkern u1="P" u2="&#xc4;" k="24" />
    <hkern u1="P" u2="&#xc3;" k="24" />
    <hkern u1="P" u2="&#xc2;" k="24" />
    <hkern u1="P" u2="&#xc1;" k="24" />
    <hkern u1="P" u2="&#xc0;" k="24" />
    <hkern u1="P" u2="&#xab;" k="4" />
    <hkern u1="P" u2="&#x7d;" k="15" />
    <hkern u1="P" u2="a" k="5" />
    <hkern u1="P" u2="]" k="17" />
    <hkern u1="P" u2="\" k="9" />
    <hkern u1="P" u2="Z" k="3" />
    <hkern u1="P" u2="Y" k="20" />
    <hkern u1="P" u2="X" k="17" />
    <hkern u1="P" u2="V" k="7" />
    <hkern u1="P" u2="J" k="16" />
    <hkern u1="P" u2="A" k="24" />
    <hkern u1="P" u2="&#x2f;" k="37" />
    <hkern u1="P" u2="&#x2e;" k="70" />
    <hkern u1="P" u2="&#x2d;" k="4" />
    <hkern u1="P" u2="&#x2c;" k="70" />
    <hkern u1="P" u2="&#x29;" k="7" />
    <hkern u1="Q" g2="braceright.cap" k="19" />
    <hkern u1="Q" g2="bracketright.cap" k="24" />
    <hkern u1="Q" g2="parenright.cap" k="13" />
    <hkern u1="Q" u2="&#xc6;" k="14" />
    <hkern u1="Q" u2="&#x7d;" k="17" />
    <hkern u1="Q" u2="]" k="24" />
    <hkern u1="Q" u2="\" k="14" />
    <hkern u1="Q" u2="X" k="18" />
    <hkern u1="Q" u2="V" k="11" />
    <hkern u1="Q" u2="&#x3f;" k="3" />
    <hkern u1="Q" u2="&#x2f;" k="10" />
    <hkern u1="Q" u2="&#x29;" k="10" />
    <hkern u1="R" g2="braceright.cap" k="5" />
    <hkern u1="R" g2="bracketright.cap" k="7" />
    <hkern u1="R" u2="&#xf0;" k="13" />
    <hkern u1="R" u2="&#xc6;" k="9" />
    <hkern u1="R" u2="&#x7d;" k="7" />
    <hkern u1="R" u2="]" k="8" />
    <hkern u1="R" u2="\" k="11" />
    <hkern u1="R" u2="X" k="4" />
    <hkern u1="R" u2="V" k="10" />
    <hkern u1="S" g2="braceright.cap" k="3" />
    <hkern u1="S" g2="bracketright.cap" k="4" />
    <hkern u1="S" u2="&#x129;" k="-7" />
    <hkern u1="S" u2="&#xef;" k="-15" />
    <hkern u1="S" u2="&#xee;" k="-6" />
    <hkern u1="S" u2="&#xec;" k="-24" />
    <hkern u1="S" u2="&#xc6;" k="11" />
    <hkern u1="S" u2="x" k="9" />
    <hkern u1="S" u2="v" k="8" />
    <hkern u1="S" u2="f" k="8" />
    <hkern u1="S" u2="X" k="5" />
    <hkern u1="S" u2="V" k="9" />
    <hkern u1="T" u2="&#x1ef9;" k="55" />
    <hkern u1="T" u2="&#x1eab;" k="63" />
    <hkern u1="T" u2="&#x16d;" k="66" />
    <hkern u1="T" u2="&#x169;" k="66" />
    <hkern u1="T" u2="&#x15d;" k="68" />
    <hkern u1="T" u2="&#x159;" k="42" />
    <hkern u1="T" u2="&#x155;" k="51" />
    <hkern u1="T" u2="&#x151;" k="59" />
    <hkern u1="T" u2="&#x135;" k="-36" />
    <hkern u1="T" u2="&#x131;" k="67" />
    <hkern u1="T" u2="&#x12d;" k="-37" />
    <hkern u1="T" u2="&#x12b;" k="-30" />
    <hkern u1="T" u2="&#x129;" k="-52" />
    <hkern u1="T" u2="&#x11f;" k="83" />
    <hkern u1="T" u2="&#x109;" k="59" />
    <hkern u1="T" u2="&#xf0;" k="25" />
    <hkern u1="T" u2="&#xef;" k="-51" />
    <hkern u1="T" u2="&#xee;" k="-41" />
    <hkern u1="T" u2="&#xec;" k="-69" />
    <hkern u1="T" u2="&#xe4;" k="66" />
    <hkern u1="T" u2="&#xe3;" k="54" />
    <hkern u1="T" u2="&#xc6;" k="51" />
    <hkern u1="T" u2="&#xae;" k="4" />
    <hkern u1="T" u2="x" k="59" />
    <hkern u1="T" u2="v" k="57" />
    <hkern u1="T" u2="f" k="15" />
    <hkern u1="T" u2="&#x40;" k="20" />
    <hkern u1="T" u2="&#x2f;" k="50" />
    <hkern u1="T" u2="&#x26;" k="15" />
    <hkern u1="U" g2="braceright.cap" k="5" />
    <hkern u1="U" g2="bracketright.cap" k="5" />
    <hkern u1="U" u2="&#xf0;" k="6" />
    <hkern u1="U" u2="&#xec;" k="-10" />
    <hkern u1="U" u2="&#xc6;" k="8" />
    <hkern u1="U" u2="f" k="4" />
    <hkern u1="U" u2="&#x2f;" k="12" />
    <hkern u1="V" g2="braceright.cap" k="5" />
    <hkern u1="V" g2="bracketright.cap" k="5" />
    <hkern u1="V" g2="emdash.cap" k="24" />
    <hkern u1="V" g2="endash.cap" k="24" />
    <hkern u1="V" u2="&#x203a;" k="15" />
    <hkern u1="V" u2="&#x2039;" k="26" />
    <hkern u1="V" u2="&#x2026;" k="45" />
    <hkern u1="V" u2="&#x201e;" k="45" />
    <hkern u1="V" u2="&#x201a;" k="45" />
    <hkern u1="V" u2="&#x2014;" k="27" />
    <hkern u1="V" u2="&#x2013;" k="27" />
    <hkern u1="V" u2="&#x1ef9;" k="7" />
    <hkern u1="V" u2="&#x1ef3;" k="7" />
    <hkern u1="V" u2="&#x1ed7;" k="30" />
    <hkern u1="V" u2="&#x1ec5;" k="30" />
    <hkern u1="V" u2="&#x1eb0;" k="28" />
    <hkern u1="V" u2="&#x1eab;" k="25" />
    <hkern u1="V" u2="&#x1e85;" k="9" />
    <hkern u1="V" u2="&#x1e83;" k="9" />
    <hkern u1="V" u2="&#x1e81;" k="9" />
    <hkern u1="V" u2="&#x219;" k="23" />
    <hkern u1="V" u2="&#x218;" k="8" />
    <hkern u1="V" u2="&#x1ff;" k="30" />
    <hkern u1="V" u2="&#x1fe;" k="11" />
    <hkern u1="V" u2="&#x1fd;" k="25" />
    <hkern u1="V" u2="&#x1fc;" k="28" />
    <hkern u1="V" u2="&#x1fb;" k="25" />
    <hkern u1="V" u2="&#x1fa;" k="28" />
    <hkern u1="V" u2="&#x17e;" k="13" />
    <hkern u1="V" u2="&#x17c;" k="13" />
    <hkern u1="V" u2="&#x17a;" k="13" />
    <hkern u1="V" u2="&#x177;" k="7" />
    <hkern u1="V" u2="&#x175;" k="9" />
    <hkern u1="V" u2="&#x173;" k="20" />
    <hkern u1="V" u2="&#x171;" k="20" />
    <hkern u1="V" u2="&#x16f;" k="20" />
    <hkern u1="V" u2="&#x16d;" k="20" />
    <hkern u1="V" u2="&#x16b;" k="20" />
    <hkern u1="V" u2="&#x169;" k="20" />
    <hkern u1="V" u2="&#x161;" k="23" />
    <hkern u1="V" u2="&#x160;" k="8" />
    <hkern u1="V" u2="&#x15f;" k="23" />
    <hkern u1="V" u2="&#x15e;" k="8" />
    <hkern u1="V" u2="&#x15d;" k="23" />
    <hkern u1="V" u2="&#x15c;" k="8" />
    <hkern u1="V" u2="&#x15b;" k="23" />
    <hkern u1="V" u2="&#x15a;" k="8" />
    <hkern u1="V" u2="&#x159;" k="21" />
    <hkern u1="V" u2="&#x157;" k="23" />
    <hkern u1="V" u2="&#x155;" k="22" />
    <hkern u1="V" u2="&#x153;" k="30" />
    <hkern u1="V" u2="&#x152;" k="11" />
    <hkern u1="V" u2="&#x151;" k="30" />
    <hkern u1="V" u2="&#x150;" k="11" />
    <hkern u1="V" u2="&#x14f;" k="30" />
    <hkern u1="V" u2="&#x14e;" k="11" />
    <hkern u1="V" u2="&#x14d;" k="30" />
    <hkern u1="V" u2="&#x14c;" k="11" />
    <hkern u1="V" u2="&#x14b;" k="23" />
    <hkern u1="V" u2="&#x148;" k="23" />
    <hkern u1="V" u2="&#x146;" k="23" />
    <hkern u1="V" u2="&#x144;" k="23" />
    <hkern u1="V" u2="&#x135;" k="-20" />
    <hkern u1="V" u2="&#x134;" k="18" />
    <hkern u1="V" u2="&#x131;" k="23" />
    <hkern u1="V" u2="&#x12d;" k="-31" />
    <hkern u1="V" u2="&#x12b;" k="-22" />
    <hkern u1="V" u2="&#x129;" k="-41" />
    <hkern u1="V" u2="&#x123;" k="33" />
    <hkern u1="V" u2="&#x122;" k="11" />
    <hkern u1="V" u2="&#x121;" k="33" />
    <hkern u1="V" u2="&#x120;" k="11" />
    <hkern u1="V" u2="&#x11f;" k="33" />
    <hkern u1="V" u2="&#x11e;" k="11" />
    <hkern u1="V" u2="&#x11d;" k="33" />
    <hkern u1="V" u2="&#x11c;" k="11" />
    <hkern u1="V" u2="&#x11b;" k="30" />
    <hkern u1="V" u2="&#x119;" k="30" />
    <hkern u1="V" u2="&#x117;" k="30" />
    <hkern u1="V" u2="&#x115;" k="30" />
    <hkern u1="V" u2="&#x113;" k="30" />
    <hkern u1="V" u2="&#x111;" k="30" />
    <hkern u1="V" u2="&#x10f;" k="30" />
    <hkern u1="V" u2="&#x10d;" k="30" />
    <hkern u1="V" u2="&#x10c;" k="10" />
    <hkern u1="V" u2="&#x10b;" k="30" />
    <hkern u1="V" u2="&#x10a;" k="10" />
    <hkern u1="V" u2="&#x109;" k="30" />
    <hkern u1="V" u2="&#x108;" k="10" />
    <hkern u1="V" u2="&#x107;" k="30" />
    <hkern u1="V" u2="&#x106;" k="10" />
    <hkern u1="V" u2="&#x105;" k="25" />
    <hkern u1="V" u2="&#x104;" k="28" />
    <hkern u1="V" u2="&#x103;" k="25" />
    <hkern u1="V" u2="&#x102;" k="28" />
    <hkern u1="V" u2="&#x101;" k="25" />
    <hkern u1="V" u2="&#x100;" k="28" />
    <hkern u1="V" u2="&#xff;" k="7" />
    <hkern u1="V" u2="&#xfd;" k="7" />
    <hkern u1="V" u2="&#xfc;" k="20" />
    <hkern u1="V" u2="&#xfb;" k="20" />
    <hkern u1="V" u2="&#xfa;" k="20" />
    <hkern u1="V" u2="&#xf9;" k="20" />
    <hkern u1="V" u2="&#xf8;" k="30" />
    <hkern u1="V" u2="&#xf6;" k="30" />
    <hkern u1="V" u2="&#xf5;" k="30" />
    <hkern u1="V" u2="&#xf4;" k="30" />
    <hkern u1="V" u2="&#xf3;" k="30" />
    <hkern u1="V" u2="&#xf2;" k="30" />
    <hkern u1="V" u2="&#xf1;" k="23" />
    <hkern u1="V" u2="&#xf0;" k="23" />
    <hkern u1="V" u2="&#xef;" k="-45" />
    <hkern u1="V" u2="&#xee;" k="-22" />
    <hkern u1="V" u2="&#xec;" k="-56" />
    <hkern u1="V" u2="&#xeb;" k="30" />
    <hkern u1="V" u2="&#xea;" k="30" />
    <hkern u1="V" u2="&#xe9;" k="30" />
    <hkern u1="V" u2="&#xe8;" k="30" />
    <hkern u1="V" u2="&#xe7;" k="30" />
    <hkern u1="V" u2="&#xe6;" k="25" />
    <hkern u1="V" u2="&#xe5;" k="25" />
    <hkern u1="V" u2="&#xe4;" k="25" />
    <hkern u1="V" u2="&#xe3;" k="25" />
    <hkern u1="V" u2="&#xe2;" k="25" />
    <hkern u1="V" u2="&#xe1;" k="25" />
    <hkern u1="V" u2="&#xe0;" k="25" />
    <hkern u1="V" u2="&#xd8;" k="11" />
    <hkern u1="V" u2="&#xd6;" k="11" />
    <hkern u1="V" u2="&#xd5;" k="11" />
    <hkern u1="V" u2="&#xd4;" k="11" />
    <hkern u1="V" u2="&#xd3;" k="11" />
    <hkern u1="V" u2="&#xd2;" k="11" />
    <hkern u1="V" u2="&#xc7;" k="10" />
    <hkern u1="V" u2="&#xc6;" k="32" />
    <hkern u1="V" u2="&#xc5;" k="28" />
    <hkern u1="V" u2="&#xc4;" k="28" />
    <hkern u1="V" u2="&#xc3;" k="28" />
    <hkern u1="V" u2="&#xc2;" k="28" />
    <hkern u1="V" u2="&#xc1;" k="28" />
    <hkern u1="V" u2="&#xc0;" k="28" />
    <hkern u1="V" u2="&#xbb;" k="15" />
    <hkern u1="V" u2="&#xae;" k="3" />
    <hkern u1="V" u2="&#xab;" k="26" />
    <hkern u1="V" u2="z" k="13" />
    <hkern u1="V" u2="y" k="7" />
    <hkern u1="V" u2="x" k="7" />
    <hkern u1="V" u2="w" k="9" />
    <hkern u1="V" u2="v" k="7" />
    <hkern u1="V" u2="u" k="20" />
    <hkern u1="V" u2="s" k="23" />
    <hkern u1="V" u2="r" k="23" />
    <hkern u1="V" u2="q" k="30" />
    <hkern u1="V" u2="p" k="23" />
    <hkern u1="V" u2="o" k="30" />
    <hkern u1="V" u2="n" k="23" />
    <hkern u1="V" u2="m" k="23" />
    <hkern u1="V" u2="g" k="33" />
    <hkern u1="V" u2="f" k="5" />
    <hkern u1="V" u2="e" k="30" />
    <hkern u1="V" u2="d" k="30" />
    <hkern u1="V" u2="c" k="30" />
    <hkern u1="V" u2="a" k="25" />
    <hkern u1="V" u2="S" k="8" />
    <hkern u1="V" u2="Q" k="11" />
    <hkern u1="V" u2="O" k="11" />
    <hkern u1="V" u2="J" k="18" />
    <hkern u1="V" u2="G" k="11" />
    <hkern u1="V" u2="C" k="10" />
    <hkern u1="V" u2="A" k="28" />
    <hkern u1="V" u2="&#x40;" k="13" />
    <hkern u1="V" u2="&#x3b;" k="12" />
    <hkern u1="V" u2="&#x3a;" k="12" />
    <hkern u1="V" u2="&#x2f;" k="41" />
    <hkern u1="V" u2="&#x2e;" k="45" />
    <hkern u1="V" u2="&#x2d;" k="27" />
    <hkern u1="V" u2="&#x2c;" k="45" />
    <hkern u1="V" u2="&#x26;" k="16" />
    <hkern u1="W" g2="braceright.cap" k="4" />
    <hkern u1="W" g2="bracketright.cap" k="4" />
    <hkern u1="W" u2="&#x135;" k="-20" />
    <hkern u1="W" u2="&#x131;" k="14" />
    <hkern u1="W" u2="&#x12d;" k="-26" />
    <hkern u1="W" u2="&#x12b;" k="-18" />
    <hkern u1="W" u2="&#x129;" k="-37" />
    <hkern u1="W" u2="&#xf0;" k="15" />
    <hkern u1="W" u2="&#xef;" k="-39" />
    <hkern u1="W" u2="&#xee;" k="-22" />
    <hkern u1="W" u2="&#xec;" k="-51" />
    <hkern u1="W" u2="&#xc6;" k="25" />
    <hkern u1="W" u2="&#x2f;" k="28" />
    <hkern u1="W" u2="&#x26;" k="3" />
    <hkern u1="X" g2="emdash.cap" k="41" />
    <hkern u1="X" g2="endash.cap" k="41" />
    <hkern u1="X" u2="&#x2039;" k="20" />
    <hkern u1="X" u2="&#x2014;" k="35" />
    <hkern u1="X" u2="&#x2013;" k="35" />
    <hkern u1="X" u2="&#x1ef9;" k="25" />
    <hkern u1="X" u2="&#x1ef3;" k="25" />
    <hkern u1="X" u2="&#x1ed7;" k="24" />
    <hkern u1="X" u2="&#x1ec5;" k="24" />
    <hkern u1="X" u2="&#x1eab;" k="4" />
    <hkern u1="X" u2="&#x1e85;" k="25" />
    <hkern u1="X" u2="&#x1e83;" k="25" />
    <hkern u1="X" u2="&#x1e81;" k="25" />
    <hkern u1="X" u2="&#x21b;" k="10" />
    <hkern u1="X" u2="&#x1ff;" k="24" />
    <hkern u1="X" u2="&#x1fe;" k="18" />
    <hkern u1="X" u2="&#x1fd;" k="4" />
    <hkern u1="X" u2="&#x1fb;" k="4" />
    <hkern u1="X" u2="&#x177;" k="25" />
    <hkern u1="X" u2="&#x175;" k="25" />
    <hkern u1="X" u2="&#x173;" k="18" />
    <hkern u1="X" u2="&#x171;" k="18" />
    <hkern u1="X" u2="&#x16f;" k="18" />
    <hkern u1="X" u2="&#x16d;" k="18" />
    <hkern u1="X" u2="&#x16b;" k="18" />
    <hkern u1="X" u2="&#x169;" k="18" />
    <hkern u1="X" u2="&#x167;" k="10" />
    <hkern u1="X" u2="&#x165;" k="10" />
    <hkern u1="X" u2="&#x159;" k="4" />
    <hkern u1="X" u2="&#x157;" k="4" />
    <hkern u1="X" u2="&#x155;" k="4" />
    <hkern u1="X" u2="&#x153;" k="24" />
    <hkern u1="X" u2="&#x152;" k="18" />
    <hkern u1="X" u2="&#x151;" k="24" />
    <hkern u1="X" u2="&#x150;" k="18" />
    <hkern u1="X" u2="&#x14f;" k="24" />
    <hkern u1="X" u2="&#x14e;" k="18" />
    <hkern u1="X" u2="&#x14d;" k="24" />
    <hkern u1="X" u2="&#x14c;" k="18" />
    <hkern u1="X" u2="&#x14b;" k="4" />
    <hkern u1="X" u2="&#x148;" k="4" />
    <hkern u1="X" u2="&#x146;" k="4" />
    <hkern u1="X" u2="&#x144;" k="4" />
    <hkern u1="X" u2="&#x135;" k="-5" />
    <hkern u1="X" u2="&#x12d;" k="-37" />
    <hkern u1="X" u2="&#x12b;" k="-26" />
    <hkern u1="X" u2="&#x129;" k="-40" />
    <hkern u1="X" u2="&#x123;" k="18" />
    <hkern u1="X" u2="&#x122;" k="18" />
    <hkern u1="X" u2="&#x121;" k="18" />
    <hkern u1="X" u2="&#x120;" k="18" />
    <hkern u1="X" u2="&#x11f;" k="18" />
    <hkern u1="X" u2="&#x11e;" k="18" />
    <hkern u1="X" u2="&#x11d;" k="18" />
    <hkern u1="X" u2="&#x11c;" k="18" />
    <hkern u1="X" u2="&#x11b;" k="24" />
    <hkern u1="X" u2="&#x119;" k="24" />
    <hkern u1="X" u2="&#x117;" k="24" />
    <hkern u1="X" u2="&#x115;" k="24" />
    <hkern u1="X" u2="&#x113;" k="24" />
    <hkern u1="X" u2="&#x111;" k="19" />
    <hkern u1="X" u2="&#x10f;" k="19" />
    <hkern u1="X" u2="&#x10d;" k="24" />
    <hkern u1="X" u2="&#x10c;" k="17" />
    <hkern u1="X" u2="&#x10b;" k="24" />
    <hkern u1="X" u2="&#x10a;" k="17" />
    <hkern u1="X" u2="&#x109;" k="24" />
    <hkern u1="X" u2="&#x108;" k="17" />
    <hkern u1="X" u2="&#x107;" k="24" />
    <hkern u1="X" u2="&#x106;" k="17" />
    <hkern u1="X" u2="&#x105;" k="4" />
    <hkern u1="X" u2="&#x103;" k="4" />
    <hkern u1="X" u2="&#x101;" k="4" />
    <hkern u1="X" u2="&#xff;" k="25" />
    <hkern u1="X" u2="&#xfd;" k="25" />
    <hkern u1="X" u2="&#xfc;" k="18" />
    <hkern u1="X" u2="&#xfb;" k="18" />
    <hkern u1="X" u2="&#xfa;" k="18" />
    <hkern u1="X" u2="&#xf9;" k="18" />
    <hkern u1="X" u2="&#xf8;" k="24" />
    <hkern u1="X" u2="&#xf6;" k="24" />
    <hkern u1="X" u2="&#xf5;" k="24" />
    <hkern u1="X" u2="&#xf4;" k="24" />
    <hkern u1="X" u2="&#xf3;" k="24" />
    <hkern u1="X" u2="&#xf2;" k="24" />
    <hkern u1="X" u2="&#xf1;" k="4" />
    <hkern u1="X" u2="&#xf0;" k="15" />
    <hkern u1="X" u2="&#xef;" k="-51" />
    <hkern u1="X" u2="&#xee;" k="-10" />
    <hkern u1="X" u2="&#xec;" k="-56" />
    <hkern u1="X" u2="&#xeb;" k="24" />
    <hkern u1="X" u2="&#xea;" k="24" />
    <hkern u1="X" u2="&#xe9;" k="24" />
    <hkern u1="X" u2="&#xe8;" k="24" />
    <hkern u1="X" u2="&#xe7;" k="24" />
    <hkern u1="X" u2="&#xe6;" k="4" />
    <hkern u1="X" u2="&#xe5;" k="4" />
    <hkern u1="X" u2="&#xe4;" k="4" />
    <hkern u1="X" u2="&#xe3;" k="4" />
    <hkern u1="X" u2="&#xe2;" k="4" />
    <hkern u1="X" u2="&#xe1;" k="4" />
    <hkern u1="X" u2="&#xe0;" k="4" />
    <hkern u1="X" u2="&#xd8;" k="18" />
    <hkern u1="X" u2="&#xd6;" k="18" />
    <hkern u1="X" u2="&#xd5;" k="18" />
    <hkern u1="X" u2="&#xd4;" k="18" />
    <hkern u1="X" u2="&#xd3;" k="18" />
    <hkern u1="X" u2="&#xd2;" k="18" />
    <hkern u1="X" u2="&#xc7;" k="17" />
    <hkern u1="X" u2="&#xae;" k="4" />
    <hkern u1="X" u2="&#xab;" k="20" />
    <hkern u1="X" u2="y" k="25" />
    <hkern u1="X" u2="w" k="25" />
    <hkern u1="X" u2="v" k="24" />
    <hkern u1="X" u2="u" k="18" />
    <hkern u1="X" u2="t" k="10" />
    <hkern u1="X" u2="r" k="4" />
    <hkern u1="X" u2="q" k="19" />
    <hkern u1="X" u2="p" k="4" />
    <hkern u1="X" u2="o" k="24" />
    <hkern u1="X" u2="n" k="4" />
    <hkern u1="X" u2="m" k="4" />
    <hkern u1="X" u2="g" k="18" />
    <hkern u1="X" u2="f" k="7" />
    <hkern u1="X" u2="e" k="24" />
    <hkern u1="X" u2="d" k="19" />
    <hkern u1="X" u2="c" k="24" />
    <hkern u1="X" u2="a" k="4" />
    <hkern u1="X" u2="Q" k="18" />
    <hkern u1="X" u2="O" k="18" />
    <hkern u1="X" u2="G" k="18" />
    <hkern u1="X" u2="C" k="17" />
    <hkern u1="X" u2="&#x2d;" k="35" />
    <hkern u1="Y" g2="braceright.cap" k="5" />
    <hkern u1="Y" g2="bracketright.cap" k="6" />
    <hkern u1="Y" u2="&#x1ef9;" k="26" />
    <hkern u1="Y" u2="&#x1ef3;" k="31" />
    <hkern u1="Y" u2="&#x159;" k="32" />
    <hkern u1="Y" u2="&#x155;" k="38" />
    <hkern u1="Y" u2="&#x151;" k="54" />
    <hkern u1="Y" u2="&#x142;" k="6" />
    <hkern u1="Y" u2="&#x135;" k="-11" />
    <hkern u1="Y" u2="&#x131;" k="60" />
    <hkern u1="Y" u2="&#x12d;" k="-45" />
    <hkern u1="Y" u2="&#x12b;" k="-34" />
    <hkern u1="Y" u2="&#x129;" k="-47" />
    <hkern u1="Y" u2="&#x103;" k="59" />
    <hkern u1="Y" u2="&#xff;" k="28" />
    <hkern u1="Y" u2="&#xf0;" k="32" />
    <hkern u1="Y" u2="&#xef;" k="-59" />
    <hkern u1="Y" u2="&#xee;" k="-16" />
    <hkern u1="Y" u2="&#xec;" k="-63" />
    <hkern u1="Y" u2="&#xeb;" k="64" />
    <hkern u1="Y" u2="&#xe4;" k="49" />
    <hkern u1="Y" u2="&#xe3;" k="42" />
    <hkern u1="Y" u2="&#xdf;" k="10" />
    <hkern u1="Y" u2="&#xc6;" k="56" />
    <hkern u1="Y" u2="&#xae;" k="20" />
    <hkern u1="Y" u2="x" k="37" />
    <hkern u1="Y" u2="v" k="36" />
    <hkern u1="Y" u2="f" k="21" />
    <hkern u1="Y" u2="&#x40;" k="36" />
    <hkern u1="Y" u2="&#x2f;" k="65" />
    <hkern u1="Y" u2="&#x2a;" k="-4" />
    <hkern u1="Y" u2="&#x26;" k="32" />
    <hkern u1="Z" u2="&#x135;" k="-18" />
    <hkern u1="Z" u2="&#x12d;" k="-5" />
    <hkern u1="Z" u2="&#x12b;" k="-4" />
    <hkern u1="Z" u2="&#x129;" k="-23" />
    <hkern u1="Z" u2="&#xf0;" k="8" />
    <hkern u1="Z" u2="&#xef;" k="-22" />
    <hkern u1="Z" u2="&#xee;" k="-23" />
    <hkern u1="Z" u2="&#xec;" k="-40" />
    <hkern u1="Z" u2="&#xae;" k="3" />
    <hkern u1="Z" u2="v" k="9" />
    <hkern u1="Z" u2="f" k="5" />
    <hkern u1="[" u2="&#x1ef9;" k="24" />
    <hkern u1="[" u2="&#x1ef3;" k="24" />
    <hkern u1="[" u2="&#x1ed7;" k="32" />
    <hkern u1="[" u2="&#x1ec5;" k="32" />
    <hkern u1="[" u2="&#x1eb0;" k="10" />
    <hkern u1="[" u2="&#x1eab;" k="23" />
    <hkern u1="[" u2="&#x1e85;" k="26" />
    <hkern u1="[" u2="&#x1e83;" k="26" />
    <hkern u1="[" u2="&#x1e81;" k="26" />
    <hkern u1="[" u2="&#x21b;" k="17" />
    <hkern u1="[" u2="&#x219;" k="11" />
    <hkern u1="[" u2="&#x218;" k="5" />
    <hkern u1="[" u2="&#x1ff;" k="32" />
    <hkern u1="[" u2="&#x1fe;" k="24" />
    <hkern u1="[" u2="&#x1fd;" k="23" />
    <hkern u1="[" u2="&#x1fc;" k="10" />
    <hkern u1="[" u2="&#x1fb;" k="23" />
    <hkern u1="[" u2="&#x1fa;" k="10" />
    <hkern u1="[" u2="&#x17e;" k="9" />
    <hkern u1="[" u2="&#x17c;" k="9" />
    <hkern u1="[" u2="&#x17a;" k="9" />
    <hkern u1="[" u2="&#x177;" k="24" />
    <hkern u1="[" u2="&#x175;" k="26" />
    <hkern u1="[" u2="&#x173;" k="26" />
    <hkern u1="[" u2="&#x171;" k="26" />
    <hkern u1="[" u2="&#x16f;" k="26" />
    <hkern u1="[" u2="&#x16d;" k="26" />
    <hkern u1="[" u2="&#x16b;" k="26" />
    <hkern u1="[" u2="&#x169;" k="26" />
    <hkern u1="[" u2="&#x167;" k="17" />
    <hkern u1="[" u2="&#x165;" k="17" />
    <hkern u1="[" u2="&#x161;" k="11" />
    <hkern u1="[" u2="&#x160;" k="5" />
    <hkern u1="[" u2="&#x15f;" k="11" />
    <hkern u1="[" u2="&#x15e;" k="5" />
    <hkern u1="[" u2="&#x15d;" k="11" />
    <hkern u1="[" u2="&#x15c;" k="5" />
    <hkern u1="[" u2="&#x15b;" k="11" />
    <hkern u1="[" u2="&#x15a;" k="5" />
    <hkern u1="[" u2="&#x159;" k="11" />
    <hkern u1="[" u2="&#x157;" k="11" />
    <hkern u1="[" u2="&#x155;" k="11" />
    <hkern u1="[" u2="&#x153;" k="32" />
    <hkern u1="[" u2="&#x152;" k="24" />
    <hkern u1="[" u2="&#x151;" k="32" />
    <hkern u1="[" u2="&#x150;" k="24" />
    <hkern u1="[" u2="&#x14f;" k="32" />
    <hkern u1="[" u2="&#x14e;" k="24" />
    <hkern u1="[" u2="&#x14d;" k="32" />
    <hkern u1="[" u2="&#x14c;" k="24" />
    <hkern u1="[" u2="&#x14b;" k="11" />
    <hkern u1="[" u2="&#x148;" k="11" />
    <hkern u1="[" u2="&#x146;" k="11" />
    <hkern u1="[" u2="&#x144;" k="11" />
    <hkern u1="[" u2="&#x135;" k="-7" />
    <hkern u1="[" u2="&#x12d;" k="-25" />
    <hkern u1="[" u2="&#x129;" k="-21" />
    <hkern u1="[" u2="&#x122;" k="24" />
    <hkern u1="[" u2="&#x120;" k="24" />
    <hkern u1="[" u2="&#x11e;" k="24" />
    <hkern u1="[" u2="&#x11c;" k="24" />
    <hkern u1="[" u2="&#x11b;" k="32" />
    <hkern u1="[" u2="&#x119;" k="32" />
    <hkern u1="[" u2="&#x117;" k="32" />
    <hkern u1="[" u2="&#x115;" k="32" />
    <hkern u1="[" u2="&#x113;" k="32" />
    <hkern u1="[" u2="&#x111;" k="31" />
    <hkern u1="[" u2="&#x10f;" k="31" />
    <hkern u1="[" u2="&#x10d;" k="32" />
    <hkern u1="[" u2="&#x10c;" k="20" />
    <hkern u1="[" u2="&#x10b;" k="32" />
    <hkern u1="[" u2="&#x10a;" k="20" />
    <hkern u1="[" u2="&#x109;" k="32" />
    <hkern u1="[" u2="&#x108;" k="20" />
    <hkern u1="[" u2="&#x107;" k="32" />
    <hkern u1="[" u2="&#x106;" k="20" />
    <hkern u1="[" u2="&#x105;" k="23" />
    <hkern u1="[" u2="&#x104;" k="10" />
    <hkern u1="[" u2="&#x103;" k="23" />
    <hkern u1="[" u2="&#x102;" k="10" />
    <hkern u1="[" u2="&#x101;" k="23" />
    <hkern u1="[" u2="&#x100;" k="10" />
    <hkern u1="[" u2="&#xff;" k="24" />
    <hkern u1="[" u2="&#xfd;" k="24" />
    <hkern u1="[" u2="&#xfc;" k="26" />
    <hkern u1="[" u2="&#xfb;" k="26" />
    <hkern u1="[" u2="&#xfa;" k="26" />
    <hkern u1="[" u2="&#xf9;" k="26" />
    <hkern u1="[" u2="&#xf8;" k="32" />
    <hkern u1="[" u2="&#xf6;" k="32" />
    <hkern u1="[" u2="&#xf5;" k="32" />
    <hkern u1="[" u2="&#xf4;" k="32" />
    <hkern u1="[" u2="&#xf3;" k="32" />
    <hkern u1="[" u2="&#xf2;" k="32" />
    <hkern u1="[" u2="&#xf1;" k="11" />
    <hkern u1="[" u2="&#xf0;" k="12" />
    <hkern u1="[" u2="&#xef;" k="-28" />
    <hkern u1="[" u2="&#xec;" k="-44" />
    <hkern u1="[" u2="&#xeb;" k="32" />
    <hkern u1="[" u2="&#xea;" k="32" />
    <hkern u1="[" u2="&#xe9;" k="32" />
    <hkern u1="[" u2="&#xe8;" k="32" />
    <hkern u1="[" u2="&#xe7;" k="32" />
    <hkern u1="[" u2="&#xe6;" k="23" />
    <hkern u1="[" u2="&#xe5;" k="23" />
    <hkern u1="[" u2="&#xe4;" k="23" />
    <hkern u1="[" u2="&#xe3;" k="23" />
    <hkern u1="[" u2="&#xe2;" k="23" />
    <hkern u1="[" u2="&#xe1;" k="23" />
    <hkern u1="[" u2="&#xe0;" k="23" />
    <hkern u1="[" u2="&#xd8;" k="24" />
    <hkern u1="[" u2="&#xd6;" k="24" />
    <hkern u1="[" u2="&#xd5;" k="24" />
    <hkern u1="[" u2="&#xd4;" k="24" />
    <hkern u1="[" u2="&#xd3;" k="24" />
    <hkern u1="[" u2="&#xd2;" k="24" />
    <hkern u1="[" u2="&#xc7;" k="20" />
    <hkern u1="[" u2="&#xc6;" k="10" />
    <hkern u1="[" u2="&#xc5;" k="10" />
    <hkern u1="[" u2="&#xc4;" k="10" />
    <hkern u1="[" u2="&#xc3;" k="10" />
    <hkern u1="[" u2="&#xc2;" k="10" />
    <hkern u1="[" u2="&#xc1;" k="10" />
    <hkern u1="[" u2="&#xc0;" k="10" />
    <hkern u1="[" u2="&#x7b;" k="19" />
    <hkern u1="[" u2="z" k="9" />
    <hkern u1="[" u2="y" k="24" />
    <hkern u1="[" u2="x" k="8" />
    <hkern u1="[" u2="w" k="26" />
    <hkern u1="[" u2="v" k="25" />
    <hkern u1="[" u2="u" k="26" />
    <hkern u1="[" u2="t" k="17" />
    <hkern u1="[" u2="s" k="11" />
    <hkern u1="[" u2="r" k="11" />
    <hkern u1="[" u2="q" k="31" />
    <hkern u1="[" u2="p" k="11" />
    <hkern u1="[" u2="o" k="32" />
    <hkern u1="[" u2="n" k="11" />
    <hkern u1="[" u2="m" k="11" />
    <hkern u1="[" u2="j" k="-7" />
    <hkern u1="[" u2="f" k="12" />
    <hkern u1="[" u2="e" k="32" />
    <hkern u1="[" u2="d" k="31" />
    <hkern u1="[" u2="c" k="32" />
    <hkern u1="[" u2="a" k="23" />
    <hkern u1="[" u2="S" k="5" />
    <hkern u1="[" u2="Q" k="24" />
    <hkern u1="[" u2="O" k="24" />
    <hkern u1="[" u2="G" k="24" />
    <hkern u1="[" u2="C" k="20" />
    <hkern u1="[" u2="A" k="10" />
    <hkern u1="[" u2="&#x28;" k="4" />
    <hkern u1="\" u2="&#x201d;" k="68" />
    <hkern u1="\" u2="&#x2019;" k="68" />
    <hkern u1="\" u2="&#x1ef9;" k="23" />
    <hkern u1="\" u2="&#x1ef8;" k="70" />
    <hkern u1="\" u2="&#x1ef3;" k="23" />
    <hkern u1="\" u2="&#x1ef2;" k="70" />
    <hkern u1="\" u2="&#x1ed7;" k="7" />
    <hkern u1="\" u2="&#x1ec5;" k="7" />
    <hkern u1="\" u2="&#x1e85;" k="18" />
    <hkern u1="\" u2="&#x1e84;" k="32" />
    <hkern u1="\" u2="&#x1e83;" k="18" />
    <hkern u1="\" u2="&#x1e82;" k="32" />
    <hkern u1="\" u2="&#x1e81;" k="18" />
    <hkern u1="\" u2="&#x1e80;" k="32" />
    <hkern u1="\" u2="&#x21b;" k="14" />
    <hkern u1="\" u2="&#x21a;" k="56" />
    <hkern u1="\" u2="&#x218;" k="7" />
    <hkern u1="\" u2="&#x1ff;" k="7" />
    <hkern u1="\" u2="&#x1fe;" k="15" />
    <hkern u1="\" u2="&#x178;" k="70" />
    <hkern u1="\" u2="&#x177;" k="23" />
    <hkern u1="\" u2="&#x176;" k="70" />
    <hkern u1="\" u2="&#x175;" k="18" />
    <hkern u1="\" u2="&#x174;" k="32" />
    <hkern u1="\" u2="&#x172;" k="16" />
    <hkern u1="\" u2="&#x170;" k="16" />
    <hkern u1="\" u2="&#x16e;" k="16" />
    <hkern u1="\" u2="&#x16c;" k="16" />
    <hkern u1="\" u2="&#x16a;" k="16" />
    <hkern u1="\" u2="&#x168;" k="16" />
    <hkern u1="\" u2="&#x167;" k="14" />
    <hkern u1="\" u2="&#x166;" k="56" />
    <hkern u1="\" u2="&#x165;" k="14" />
    <hkern u1="\" u2="&#x164;" k="56" />
    <hkern u1="\" u2="&#x160;" k="7" />
    <hkern u1="\" u2="&#x15e;" k="7" />
    <hkern u1="\" u2="&#x15c;" k="7" />
    <hkern u1="\" u2="&#x15a;" k="7" />
    <hkern u1="\" u2="&#x153;" k="7" />
    <hkern u1="\" u2="&#x152;" k="15" />
    <hkern u1="\" u2="&#x151;" k="7" />
    <hkern u1="\" u2="&#x150;" k="15" />
    <hkern u1="\" u2="&#x14f;" k="7" />
    <hkern u1="\" u2="&#x14e;" k="15" />
    <hkern u1="\" u2="&#x14d;" k="7" />
    <hkern u1="\" u2="&#x14c;" k="15" />
    <hkern u1="\" u2="&#x122;" k="15" />
    <hkern u1="\" u2="&#x120;" k="15" />
    <hkern u1="\" u2="&#x11e;" k="15" />
    <hkern u1="\" u2="&#x11c;" k="15" />
    <hkern u1="\" u2="&#x11b;" k="7" />
    <hkern u1="\" u2="&#x119;" k="7" />
    <hkern u1="\" u2="&#x117;" k="7" />
    <hkern u1="\" u2="&#x115;" k="7" />
    <hkern u1="\" u2="&#x113;" k="7" />
    <hkern u1="\" u2="&#x10d;" k="7" />
    <hkern u1="\" u2="&#x10c;" k="14" />
    <hkern u1="\" u2="&#x10b;" k="7" />
    <hkern u1="\" u2="&#x10a;" k="14" />
    <hkern u1="\" u2="&#x109;" k="7" />
    <hkern u1="\" u2="&#x108;" k="14" />
    <hkern u1="\" u2="&#x107;" k="7" />
    <hkern u1="\" u2="&#x106;" k="14" />
    <hkern u1="\" u2="&#xff;" k="23" />
    <hkern u1="\" u2="&#xfd;" k="23" />
    <hkern u1="\" u2="&#xf8;" k="7" />
    <hkern u1="\" u2="&#xf6;" k="7" />
    <hkern u1="\" u2="&#xf5;" k="7" />
    <hkern u1="\" u2="&#xf4;" k="7" />
    <hkern u1="\" u2="&#xf3;" k="7" />
    <hkern u1="\" u2="&#xf2;" k="7" />
    <hkern u1="\" u2="&#xeb;" k="7" />
    <hkern u1="\" u2="&#xea;" k="7" />
    <hkern u1="\" u2="&#xe9;" k="7" />
    <hkern u1="\" u2="&#xe8;" k="7" />
    <hkern u1="\" u2="&#xe7;" k="7" />
    <hkern u1="\" u2="&#xdd;" k="70" />
    <hkern u1="\" u2="&#xdc;" k="16" />
    <hkern u1="\" u2="&#xdb;" k="16" />
    <hkern u1="\" u2="&#xda;" k="16" />
    <hkern u1="\" u2="&#xd9;" k="16" />
    <hkern u1="\" u2="&#xd8;" k="15" />
    <hkern u1="\" u2="&#xd6;" k="15" />
    <hkern u1="\" u2="&#xd5;" k="15" />
    <hkern u1="\" u2="&#xd4;" k="15" />
    <hkern u1="\" u2="&#xd3;" k="15" />
    <hkern u1="\" u2="&#xd2;" k="15" />
    <hkern u1="\" u2="&#xc7;" k="14" />
    <hkern u1="\" u2="y" k="23" />
    <hkern u1="\" u2="w" k="18" />
    <hkern u1="\" u2="v" k="22" />
    <hkern u1="\" u2="t" k="14" />
    <hkern u1="\" u2="o" k="7" />
    <hkern u1="\" u2="f" k="9" />
    <hkern u1="\" u2="e" k="7" />
    <hkern u1="\" u2="c" k="7" />
    <hkern u1="\" u2="Y" k="70" />
    <hkern u1="\" u2="W" k="32" />
    <hkern u1="\" u2="V" k="45" />
    <hkern u1="\" u2="U" k="16" />
    <hkern u1="\" u2="T" k="56" />
    <hkern u1="\" u2="S" k="7" />
    <hkern u1="\" u2="Q" k="15" />
    <hkern u1="\" u2="O" k="15" />
    <hkern u1="\" u2="G" k="15" />
    <hkern u1="\" u2="C" k="14" />
    <hkern u1="\" u2="&#x27;" k="71" />
    <hkern u1="\" u2="&#x22;" k="71" />
    <hkern u1="a" u2="&#x2122;" k="14" />
    <hkern u1="a" u2="&#x7d;" k="4" />
    <hkern u1="a" u2="v" k="5" />
    <hkern u1="a" u2="]" k="5" />
    <hkern u1="a" u2="\" k="40" />
    <hkern u1="a" u2="V" k="27" />
    <hkern u1="a" u2="&#x3f;" k="14" />
    <hkern u1="a" u2="&#x2a;" k="6" />
    <hkern u1="b" u2="&#x2122;" k="16" />
    <hkern u1="b" u2="&#xc6;" k="7" />
    <hkern u1="b" u2="&#x7d;" k="25" />
    <hkern u1="b" u2="x" k="10" />
    <hkern u1="b" u2="v" k="7" />
    <hkern u1="b" u2="]" k="31" />
    <hkern u1="b" u2="\" k="38" />
    <hkern u1="b" u2="X" k="21" />
    <hkern u1="b" u2="V" k="29" />
    <hkern u1="b" u2="&#x3f;" k="20" />
    <hkern u1="b" u2="&#x2a;" k="8" />
    <hkern u1="b" u2="&#x29;" k="18" />
    <hkern u1="c" u2="&#xf0;" k="9" />
    <hkern u1="c" u2="&#x7d;" k="7" />
    <hkern u1="c" u2="]" k="8" />
    <hkern u1="c" u2="\" k="18" />
    <hkern u1="c" u2="V" k="12" />
    <hkern u1="c" u2="&#x3f;" k="4" />
    <hkern u1="d" u2="&#xef;" k="-6" />
    <hkern u1="d" u2="&#xec;" k="-13" />
    <hkern u1="e" u2="&#x2122;" k="13" />
    <hkern u1="e" u2="&#xc6;" k="5" />
    <hkern u1="e" u2="&#x7d;" k="18" />
    <hkern u1="e" u2="v" k="7" />
    <hkern u1="e" u2="]" k="11" />
    <hkern u1="e" u2="\" k="36" />
    <hkern u1="e" u2="X" k="4" />
    <hkern u1="e" u2="V" k="28" />
    <hkern u1="e" u2="&#x3f;" k="15" />
    <hkern u1="e" u2="&#x29;" k="9" />
    <hkern u1="f" u2="&#x203a;" k="9" />
    <hkern u1="f" u2="&#x2039;" k="28" />
    <hkern u1="f" u2="&#x2026;" k="37" />
    <hkern u1="f" u2="&#x201e;" k="37" />
    <hkern u1="f" u2="&#x201a;" k="37" />
    <hkern u1="f" u2="&#x2014;" k="36" />
    <hkern u1="f" u2="&#x2013;" k="36" />
    <hkern u1="f" u2="&#x1ef8;" k="-3" />
    <hkern u1="f" u2="&#x1ef2;" k="-3" />
    <hkern u1="f" u2="&#x1ed7;" k="6" />
    <hkern u1="f" u2="&#x1ec5;" k="6" />
    <hkern u1="f" u2="&#x1eb0;" k="23" />
    <hkern u1="f" u2="&#x21a;" k="13" />
    <hkern u1="f" u2="&#x1ff;" k="6" />
    <hkern u1="f" u2="&#x1fc;" k="23" />
    <hkern u1="f" u2="&#x1fa;" k="23" />
    <hkern u1="f" u2="&#x17d;" k="5" />
    <hkern u1="f" u2="&#x17b;" k="5" />
    <hkern u1="f" u2="&#x179;" k="5" />
    <hkern u1="f" u2="&#x178;" k="-3" />
    <hkern u1="f" u2="&#x176;" k="-3" />
    <hkern u1="f" u2="&#x166;" k="13" />
    <hkern u1="f" u2="&#x164;" k="13" />
    <hkern u1="f" u2="&#x153;" k="6" />
    <hkern u1="f" u2="&#x151;" k="6" />
    <hkern u1="f" u2="&#x14f;" k="6" />
    <hkern u1="f" u2="&#x14d;" k="6" />
    <hkern u1="f" u2="&#x135;" k="-27" />
    <hkern u1="f" u2="&#x134;" k="15" />
    <hkern u1="f" u2="&#x12d;" k="-47" />
    <hkern u1="f" u2="&#x12b;" k="-18" />
    <hkern u1="f" u2="&#x129;" k="-42" />
    <hkern u1="f" u2="&#x11b;" k="6" />
    <hkern u1="f" u2="&#x119;" k="6" />
    <hkern u1="f" u2="&#x117;" k="6" />
    <hkern u1="f" u2="&#x115;" k="6" />
    <hkern u1="f" u2="&#x113;" k="6" />
    <hkern u1="f" u2="&#x111;" k="4" />
    <hkern u1="f" u2="&#x10f;" k="4" />
    <hkern u1="f" u2="&#x10d;" k="6" />
    <hkern u1="f" u2="&#x10b;" k="6" />
    <hkern u1="f" u2="&#x109;" k="6" />
    <hkern u1="f" u2="&#x107;" k="6" />
    <hkern u1="f" u2="&#x104;" k="23" />
    <hkern u1="f" u2="&#x102;" k="23" />
    <hkern u1="f" u2="&#x100;" k="23" />
    <hkern u1="f" u2="&#xf8;" k="6" />
    <hkern u1="f" u2="&#xf6;" k="6" />
    <hkern u1="f" u2="&#xf5;" k="6" />
    <hkern u1="f" u2="&#xf4;" k="6" />
    <hkern u1="f" u2="&#xf3;" k="6" />
    <hkern u1="f" u2="&#xf2;" k="6" />
    <hkern u1="f" u2="&#xf0;" k="26" />
    <hkern u1="f" u2="&#xef;" k="-44" />
    <hkern u1="f" u2="&#xee;" k="-33" />
    <hkern u1="f" u2="&#xec;" k="-72" />
    <hkern u1="f" u2="&#xeb;" k="6" />
    <hkern u1="f" u2="&#xea;" k="6" />
    <hkern u1="f" u2="&#xe9;" k="6" />
    <hkern u1="f" u2="&#xe8;" k="6" />
    <hkern u1="f" u2="&#xe7;" k="6" />
    <hkern u1="f" u2="&#xdd;" k="-3" />
    <hkern u1="f" u2="&#xc6;" k="28" />
    <hkern u1="f" u2="&#xc5;" k="23" />
    <hkern u1="f" u2="&#xc4;" k="23" />
    <hkern u1="f" u2="&#xc3;" k="23" />
    <hkern u1="f" u2="&#xc2;" k="23" />
    <hkern u1="f" u2="&#xc1;" k="23" />
    <hkern u1="f" u2="&#xc0;" k="23" />
    <hkern u1="f" u2="&#xbb;" k="9" />
    <hkern u1="f" u2="&#xab;" k="28" />
    <hkern u1="f" u2="q" k="4" />
    <hkern u1="f" u2="o" k="6" />
    <hkern u1="f" u2="e" k="6" />
    <hkern u1="f" u2="d" k="4" />
    <hkern u1="f" u2="c" k="6" />
    <hkern u1="f" u2="Z" k="5" />
    <hkern u1="f" u2="Y" k="-3" />
    <hkern u1="f" u2="X" k="6" />
    <hkern u1="f" u2="T" k="13" />
    <hkern u1="f" u2="J" k="15" />
    <hkern u1="f" u2="A" k="23" />
    <hkern u1="f" u2="&#x2f;" k="26" />
    <hkern u1="f" u2="&#x2e;" k="37" />
    <hkern u1="f" u2="&#x2d;" k="36" />
    <hkern u1="f" u2="&#x2c;" k="37" />
    <hkern u1="f" u2="&#x26;" k="4" />
    <hkern u1="g" u2="&#x135;" k="-22" />
    <hkern u1="g" u2="&#xf0;" k="6" />
    <hkern u1="g" u2="j" k="-22" />
    <hkern u1="g" u2="\" k="11" />
    <hkern u1="g" u2="V" k="4" />
    <hkern u1="h" u2="&#x2122;" k="15" />
    <hkern u1="h" u2="&#x7d;" k="10" />
    <hkern u1="h" u2="v" k="5" />
    <hkern u1="h" u2="]" k="11" />
    <hkern u1="h" u2="\" k="38" />
    <hkern u1="h" u2="V" k="28" />
    <hkern u1="h" u2="&#x3f;" k="17" />
    <hkern u1="h" u2="&#x2a;" k="6" />
    <hkern u1="h" u2="&#x29;" k="8" />
    <hkern u1="i" u2="&#xef;" k="-6" />
    <hkern u1="i" u2="&#xec;" k="-13" />
    <hkern u1="j" u2="&#xef;" k="-6" />
    <hkern u1="j" u2="&#xec;" k="-13" />
    <hkern u1="k" u2="&#xf0;" k="14" />
    <hkern u1="k" u2="&#x7d;" k="6" />
    <hkern u1="k" u2="]" k="7" />
    <hkern u1="k" u2="\" k="13" />
    <hkern u1="k" u2="V" k="8" />
    <hkern u1="k" u2="&#x3f;" k="3" />
    <hkern u1="l" u2="&#xec;" k="-9" />
    <hkern u1="l" u2="&#xb7;" k="60" />
    <hkern u1="m" u2="&#x2122;" k="15" />
    <hkern u1="m" u2="&#x7d;" k="10" />
    <hkern u1="m" u2="v" k="5" />
    <hkern u1="m" u2="]" k="11" />
    <hkern u1="m" u2="\" k="38" />
    <hkern u1="m" u2="V" k="28" />
    <hkern u1="m" u2="&#x3f;" k="17" />
    <hkern u1="m" u2="&#x2a;" k="6" />
    <hkern u1="m" u2="&#x29;" k="8" />
    <hkern u1="n" u2="&#x2122;" k="15" />
    <hkern u1="n" u2="&#x7d;" k="10" />
    <hkern u1="n" u2="v" k="5" />
    <hkern u1="n" u2="]" k="11" />
    <hkern u1="n" u2="\" k="38" />
    <hkern u1="n" u2="V" k="28" />
    <hkern u1="n" u2="&#x3f;" k="17" />
    <hkern u1="n" u2="&#x2a;" k="6" />
    <hkern u1="n" u2="&#x29;" k="8" />
    <hkern u1="o" u2="&#x2122;" k="15" />
    <hkern u1="o" u2="&#xc6;" k="7" />
    <hkern u1="o" u2="&#x7d;" k="25" />
    <hkern u1="o" u2="x" k="11" />
    <hkern u1="o" u2="v" k="8" />
    <hkern u1="o" u2="]" k="32" />
    <hkern u1="o" u2="\" k="39" />
    <hkern u1="o" u2="X" k="24" />
    <hkern u1="o" u2="V" k="30" />
    <hkern u1="o" u2="&#x3f;" k="19" />
    <hkern u1="o" u2="&#x2a;" k="6" />
    <hkern u1="o" u2="&#x29;" k="19" />
    <hkern u1="p" u2="&#x2122;" k="16" />
    <hkern u1="p" u2="&#xc6;" k="7" />
    <hkern u1="p" u2="&#x7d;" k="25" />
    <hkern u1="p" u2="x" k="10" />
    <hkern u1="p" u2="v" k="7" />
    <hkern u1="p" u2="]" k="31" />
    <hkern u1="p" u2="\" k="38" />
    <hkern u1="p" u2="X" k="21" />
    <hkern u1="p" u2="V" k="29" />
    <hkern u1="p" u2="&#x3f;" k="20" />
    <hkern u1="p" u2="&#x2a;" k="8" />
    <hkern u1="p" u2="&#x29;" k="18" />
    <hkern u1="q" u2="&#x2122;" k="10" />
    <hkern u1="q" u2="&#x7d;" k="10" />
    <hkern u1="q" u2="]" k="11" />
    <hkern u1="q" u2="\" k="26" />
    <hkern u1="q" u2="X" k="4" />
    <hkern u1="q" u2="V" k="23" />
    <hkern u1="q" u2="&#x3f;" k="5" />
    <hkern u1="q" u2="&#x29;" k="8" />
    <hkern u1="r" u2="&#xf0;" k="28" />
    <hkern u1="r" u2="&#xc6;" k="36" />
    <hkern u1="r" u2="&#x7d;" k="17" />
    <hkern u1="r" u2="]" k="23" />
    <hkern u1="r" u2="\" k="8" />
    <hkern u1="r" u2="X" k="28" />
    <hkern u1="r" u2="&#x2f;" k="33" />
    <hkern u1="r" u2="&#x29;" k="7" />
    <hkern u1="r" u2="&#x26;" k="4" />
    <hkern u1="s" u2="&#x2122;" k="11" />
    <hkern u1="s" u2="&#xc6;" k="5" />
    <hkern u1="s" u2="&#x7d;" k="19" />
    <hkern u1="s" u2="v" k="6" />
    <hkern u1="s" u2="]" k="24" />
    <hkern u1="s" u2="\" k="26" />
    <hkern u1="s" u2="X" k="6" />
    <hkern u1="s" u2="V" k="20" />
    <hkern u1="s" u2="&#x3f;" k="5" />
    <hkern u1="s" u2="&#x29;" k="10" />
    <hkern u1="t" u2="&#x7d;" k="4" />
    <hkern u1="t" u2="]" k="5" />
    <hkern u1="t" u2="\" k="13" />
    <hkern u1="t" u2="V" k="4" />
    <hkern u1="u" u2="&#x2122;" k="10" />
    <hkern u1="u" u2="&#x7d;" k="10" />
    <hkern u1="u" u2="]" k="11" />
    <hkern u1="u" u2="\" k="26" />
    <hkern u1="u" u2="X" k="4" />
    <hkern u1="u" u2="V" k="23" />
    <hkern u1="u" u2="&#x3f;" k="5" />
    <hkern u1="u" u2="&#x29;" k="8" />
    <hkern u1="v" u2="&#x2039;" k="11" />
    <hkern u1="v" u2="&#x2026;" k="28" />
    <hkern u1="v" u2="&#x201e;" k="28" />
    <hkern u1="v" u2="&#x201a;" k="28" />
    <hkern u1="v" u2="&#x2014;" k="11" />
    <hkern u1="v" u2="&#x2013;" k="11" />
    <hkern u1="v" u2="&#x1ef8;" k="36" />
    <hkern u1="v" u2="&#x1ef2;" k="36" />
    <hkern u1="v" u2="&#x1ed7;" k="8" />
    <hkern u1="v" u2="&#x1ec5;" k="8" />
    <hkern u1="v" u2="&#x1eb0;" k="16" />
    <hkern u1="v" u2="&#x1eab;" k="7" />
    <hkern u1="v" u2="&#x21a;" k="58" />
    <hkern u1="v" u2="&#x219;" k="6" />
    <hkern u1="v" u2="&#x1ff;" k="8" />
    <hkern u1="v" u2="&#x1fd;" k="7" />
    <hkern u1="v" u2="&#x1fc;" k="16" />
    <hkern u1="v" u2="&#x1fb;" k="7" />
    <hkern u1="v" u2="&#x1fa;" k="16" />
    <hkern u1="v" u2="&#x17d;" k="11" />
    <hkern u1="v" u2="&#x17b;" k="11" />
    <hkern u1="v" u2="&#x179;" k="11" />
    <hkern u1="v" u2="&#x178;" k="36" />
    <hkern u1="v" u2="&#x176;" k="36" />
    <hkern u1="v" u2="&#x166;" k="58" />
    <hkern u1="v" u2="&#x164;" k="58" />
    <hkern u1="v" u2="&#x161;" k="6" />
    <hkern u1="v" u2="&#x15f;" k="6" />
    <hkern u1="v" u2="&#x15d;" k="6" />
    <hkern u1="v" u2="&#x15b;" k="6" />
    <hkern u1="v" u2="&#x153;" k="8" />
    <hkern u1="v" u2="&#x151;" k="8" />
    <hkern u1="v" u2="&#x14f;" k="8" />
    <hkern u1="v" u2="&#x14d;" k="8" />
    <hkern u1="v" u2="&#x134;" k="18" />
    <hkern u1="v" u2="&#x123;" k="8" />
    <hkern u1="v" u2="&#x121;" k="8" />
    <hkern u1="v" u2="&#x11f;" k="8" />
    <hkern u1="v" u2="&#x11d;" k="8" />
    <hkern u1="v" u2="&#x11b;" k="8" />
    <hkern u1="v" u2="&#x119;" k="8" />
    <hkern u1="v" u2="&#x117;" k="8" />
    <hkern u1="v" u2="&#x115;" k="8" />
    <hkern u1="v" u2="&#x113;" k="8" />
    <hkern u1="v" u2="&#x111;" k="7" />
    <hkern u1="v" u2="&#x10f;" k="7" />
    <hkern u1="v" u2="&#x10d;" k="8" />
    <hkern u1="v" u2="&#x10b;" k="8" />
    <hkern u1="v" u2="&#x109;" k="8" />
    <hkern u1="v" u2="&#x107;" k="8" />
    <hkern u1="v" u2="&#x105;" k="7" />
    <hkern u1="v" u2="&#x104;" k="16" />
    <hkern u1="v" u2="&#x103;" k="7" />
    <hkern u1="v" u2="&#x102;" k="16" />
    <hkern u1="v" u2="&#x101;" k="7" />
    <hkern u1="v" u2="&#x100;" k="16" />
    <hkern u1="v" u2="&#xf8;" k="8" />
    <hkern u1="v" u2="&#xf6;" k="8" />
    <hkern u1="v" u2="&#xf5;" k="8" />
    <hkern u1="v" u2="&#xf4;" k="8" />
    <hkern u1="v" u2="&#xf3;" k="8" />
    <hkern u1="v" u2="&#xf2;" k="8" />
    <hkern u1="v" u2="&#xf0;" k="12" />
    <hkern u1="v" u2="&#xeb;" k="8" />
    <hkern u1="v" u2="&#xea;" k="8" />
    <hkern u1="v" u2="&#xe9;" k="8" />
    <hkern u1="v" u2="&#xe8;" k="8" />
    <hkern u1="v" u2="&#xe7;" k="8" />
    <hkern u1="v" u2="&#xe6;" k="7" />
    <hkern u1="v" u2="&#xe5;" k="7" />
    <hkern u1="v" u2="&#xe4;" k="7" />
    <hkern u1="v" u2="&#xe3;" k="7" />
    <hkern u1="v" u2="&#xe2;" k="7" />
    <hkern u1="v" u2="&#xe1;" k="7" />
    <hkern u1="v" u2="&#xe0;" k="7" />
    <hkern u1="v" u2="&#xdd;" k="36" />
    <hkern u1="v" u2="&#xc6;" k="19" />
    <hkern u1="v" u2="&#xc5;" k="16" />
    <hkern u1="v" u2="&#xc4;" k="16" />
    <hkern u1="v" u2="&#xc3;" k="16" />
    <hkern u1="v" u2="&#xc2;" k="16" />
    <hkern u1="v" u2="&#xc1;" k="16" />
    <hkern u1="v" u2="&#xc0;" k="16" />
    <hkern u1="v" u2="&#xab;" k="11" />
    <hkern u1="v" u2="&#x7d;" k="19" />
    <hkern u1="v" u2="s" k="6" />
    <hkern u1="v" u2="q" k="7" />
    <hkern u1="v" u2="o" k="8" />
    <hkern u1="v" u2="g" k="8" />
    <hkern u1="v" u2="e" k="8" />
    <hkern u1="v" u2="d" k="7" />
    <hkern u1="v" u2="c" k="8" />
    <hkern u1="v" u2="a" k="7" />
    <hkern u1="v" u2="]" k="25" />
    <hkern u1="v" u2="\" k="13" />
    <hkern u1="v" u2="Z" k="11" />
    <hkern u1="v" u2="Y" k="36" />
    <hkern u1="v" u2="X" k="24" />
    <hkern u1="v" u2="V" k="7" />
    <hkern u1="v" u2="T" k="58" />
    <hkern u1="v" u2="J" k="18" />
    <hkern u1="v" u2="A" k="16" />
    <hkern u1="v" u2="&#x3f;" k="4" />
    <hkern u1="v" u2="&#x2f;" k="19" />
    <hkern u1="v" u2="&#x2e;" k="28" />
    <hkern u1="v" u2="&#x2d;" k="11" />
    <hkern u1="v" u2="&#x2c;" k="28" />
    <hkern u1="v" u2="&#x29;" k="8" />
    <hkern u1="w" u2="&#xf0;" k="8" />
    <hkern u1="w" u2="&#xc6;" k="16" />
    <hkern u1="w" u2="&#x7d;" k="21" />
    <hkern u1="w" u2="]" k="26" />
    <hkern u1="w" u2="\" k="13" />
    <hkern u1="w" u2="X" k="24" />
    <hkern u1="w" u2="V" k="9" />
    <hkern u1="w" u2="&#x3f;" k="4" />
    <hkern u1="w" u2="&#x2f;" k="15" />
    <hkern u1="w" u2="&#x29;" k="10" />
    <hkern u1="x" u2="&#x2039;" k="23" />
    <hkern u1="x" u2="&#x2014;" k="27" />
    <hkern u1="x" u2="&#x2013;" k="27" />
    <hkern u1="x" u2="&#x1ef8;" k="35" />
    <hkern u1="x" u2="&#x1ef2;" k="35" />
    <hkern u1="x" u2="&#x1ed7;" k="11" />
    <hkern u1="x" u2="&#x1ec5;" k="11" />
    <hkern u1="x" u2="&#x21a;" k="58" />
    <hkern u1="x" u2="&#x1ff;" k="11" />
    <hkern u1="x" u2="&#x178;" k="35" />
    <hkern u1="x" u2="&#x176;" k="35" />
    <hkern u1="x" u2="&#x166;" k="58" />
    <hkern u1="x" u2="&#x164;" k="58" />
    <hkern u1="x" u2="&#x153;" k="11" />
    <hkern u1="x" u2="&#x151;" k="11" />
    <hkern u1="x" u2="&#x14f;" k="11" />
    <hkern u1="x" u2="&#x14d;" k="11" />
    <hkern u1="x" u2="&#x123;" k="9" />
    <hkern u1="x" u2="&#x121;" k="9" />
    <hkern u1="x" u2="&#x11f;" k="9" />
    <hkern u1="x" u2="&#x11d;" k="9" />
    <hkern u1="x" u2="&#x11b;" k="11" />
    <hkern u1="x" u2="&#x119;" k="11" />
    <hkern u1="x" u2="&#x117;" k="11" />
    <hkern u1="x" u2="&#x115;" k="11" />
    <hkern u1="x" u2="&#x113;" k="11" />
    <hkern u1="x" u2="&#x111;" k="11" />
    <hkern u1="x" u2="&#x10f;" k="11" />
    <hkern u1="x" u2="&#x10d;" k="11" />
    <hkern u1="x" u2="&#x10b;" k="11" />
    <hkern u1="x" u2="&#x109;" k="11" />
    <hkern u1="x" u2="&#x107;" k="11" />
    <hkern u1="x" u2="&#xf8;" k="11" />
    <hkern u1="x" u2="&#xf6;" k="11" />
    <hkern u1="x" u2="&#xf5;" k="11" />
    <hkern u1="x" u2="&#xf4;" k="11" />
    <hkern u1="x" u2="&#xf3;" k="11" />
    <hkern u1="x" u2="&#xf2;" k="11" />
    <hkern u1="x" u2="&#xf0;" k="17" />
    <hkern u1="x" u2="&#xeb;" k="11" />
    <hkern u1="x" u2="&#xea;" k="11" />
    <hkern u1="x" u2="&#xe9;" k="11" />
    <hkern u1="x" u2="&#xe8;" k="11" />
    <hkern u1="x" u2="&#xe7;" k="11" />
    <hkern u1="x" u2="&#xdd;" k="35" />
    <hkern u1="x" u2="&#xab;" k="23" />
    <hkern u1="x" u2="&#x7d;" k="7" />
    <hkern u1="x" u2="q" k="11" />
    <hkern u1="x" u2="o" k="11" />
    <hkern u1="x" u2="g" k="9" />
    <hkern u1="x" u2="e" k="11" />
    <hkern u1="x" u2="d" k="11" />
    <hkern u1="x" u2="c" k="11" />
    <hkern u1="x" u2="]" k="8" />
    <hkern u1="x" u2="\" k="11" />
    <hkern u1="x" u2="Y" k="35" />
    <hkern u1="x" u2="V" k="6" />
    <hkern u1="x" u2="T" k="58" />
    <hkern u1="x" u2="&#x2d;" k="27" />
    <hkern u1="y" u2="&#xf0;" k="13" />
    <hkern u1="y" u2="&#xc6;" k="19" />
    <hkern u1="y" u2="&#x7d;" k="17" />
    <hkern u1="y" u2="]" k="23" />
    <hkern u1="y" u2="\" k="13" />
    <hkern u1="y" u2="X" k="24" />
    <hkern u1="y" u2="V" k="7" />
    <hkern u1="y" u2="&#x3f;" k="4" />
    <hkern u1="y" u2="&#x2f;" k="20" />
    <hkern u1="z" u2="&#xf0;" k="6" />
    <hkern u1="z" u2="&#x7d;" k="8" />
    <hkern u1="z" u2="]" k="9" />
    <hkern u1="z" u2="\" k="19" />
    <hkern u1="z" u2="V" k="13" />
    <hkern u1="z" u2="&#x3f;" k="4" />
    <hkern u1="&#x7b;" u2="&#x1ef9;" k="18" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="18" />
    <hkern u1="&#x7b;" u2="&#x1ed7;" k="25" />
    <hkern u1="&#x7b;" u2="&#x1ec5;" k="25" />
    <hkern u1="&#x7b;" u2="&#x1eb0;" k="9" />
    <hkern u1="&#x7b;" u2="&#x1eab;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="21" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="21" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="21" />
    <hkern u1="&#x7b;" u2="&#x21b;" k="8" />
    <hkern u1="&#x7b;" u2="&#x219;" k="10" />
    <hkern u1="&#x7b;" u2="&#x218;" k="5" />
    <hkern u1="&#x7b;" u2="&#x1ff;" k="25" />
    <hkern u1="&#x7b;" u2="&#x1fe;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1fd;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1fc;" k="9" />
    <hkern u1="&#x7b;" u2="&#x1fb;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1fa;" k="9" />
    <hkern u1="&#x7b;" u2="&#x17e;" k="8" />
    <hkern u1="&#x7b;" u2="&#x17c;" k="8" />
    <hkern u1="&#x7b;" u2="&#x17a;" k="8" />
    <hkern u1="&#x7b;" u2="&#x177;" k="18" />
    <hkern u1="&#x7b;" u2="&#x175;" k="21" />
    <hkern u1="&#x7b;" u2="&#x173;" k="22" />
    <hkern u1="&#x7b;" u2="&#x171;" k="22" />
    <hkern u1="&#x7b;" u2="&#x16f;" k="22" />
    <hkern u1="&#x7b;" u2="&#x16d;" k="22" />
    <hkern u1="&#x7b;" u2="&#x16b;" k="22" />
    <hkern u1="&#x7b;" u2="&#x169;" k="22" />
    <hkern u1="&#x7b;" u2="&#x167;" k="8" />
    <hkern u1="&#x7b;" u2="&#x165;" k="8" />
    <hkern u1="&#x7b;" u2="&#x161;" k="10" />
    <hkern u1="&#x7b;" u2="&#x160;" k="5" />
    <hkern u1="&#x7b;" u2="&#x15f;" k="10" />
    <hkern u1="&#x7b;" u2="&#x15e;" k="5" />
    <hkern u1="&#x7b;" u2="&#x15d;" k="10" />
    <hkern u1="&#x7b;" u2="&#x15c;" k="5" />
    <hkern u1="&#x7b;" u2="&#x15b;" k="10" />
    <hkern u1="&#x7b;" u2="&#x15a;" k="5" />
    <hkern u1="&#x7b;" u2="&#x159;" k="10" />
    <hkern u1="&#x7b;" u2="&#x157;" k="10" />
    <hkern u1="&#x7b;" u2="&#x155;" k="10" />
    <hkern u1="&#x7b;" u2="&#x153;" k="25" />
    <hkern u1="&#x7b;" u2="&#x152;" k="19" />
    <hkern u1="&#x7b;" u2="&#x151;" k="25" />
    <hkern u1="&#x7b;" u2="&#x150;" k="19" />
    <hkern u1="&#x7b;" u2="&#x14f;" k="25" />
    <hkern u1="&#x7b;" u2="&#x14e;" k="19" />
    <hkern u1="&#x7b;" u2="&#x14d;" k="25" />
    <hkern u1="&#x7b;" u2="&#x14c;" k="19" />
    <hkern u1="&#x7b;" u2="&#x14b;" k="10" />
    <hkern u1="&#x7b;" u2="&#x148;" k="10" />
    <hkern u1="&#x7b;" u2="&#x146;" k="10" />
    <hkern u1="&#x7b;" u2="&#x144;" k="10" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-16" />
    <hkern u1="&#x7b;" u2="&#x12d;" k="-24" />
    <hkern u1="&#x7b;" u2="&#x129;" k="-20" />
    <hkern u1="&#x7b;" u2="&#x122;" k="19" />
    <hkern u1="&#x7b;" u2="&#x120;" k="19" />
    <hkern u1="&#x7b;" u2="&#x11e;" k="19" />
    <hkern u1="&#x7b;" u2="&#x11c;" k="19" />
    <hkern u1="&#x7b;" u2="&#x11b;" k="25" />
    <hkern u1="&#x7b;" u2="&#x119;" k="25" />
    <hkern u1="&#x7b;" u2="&#x117;" k="25" />
    <hkern u1="&#x7b;" u2="&#x115;" k="25" />
    <hkern u1="&#x7b;" u2="&#x113;" k="25" />
    <hkern u1="&#x7b;" u2="&#x111;" k="25" />
    <hkern u1="&#x7b;" u2="&#x10f;" k="25" />
    <hkern u1="&#x7b;" u2="&#x10d;" k="25" />
    <hkern u1="&#x7b;" u2="&#x10c;" k="17" />
    <hkern u1="&#x7b;" u2="&#x10b;" k="25" />
    <hkern u1="&#x7b;" u2="&#x10a;" k="17" />
    <hkern u1="&#x7b;" u2="&#x109;" k="25" />
    <hkern u1="&#x7b;" u2="&#x108;" k="17" />
    <hkern u1="&#x7b;" u2="&#x107;" k="25" />
    <hkern u1="&#x7b;" u2="&#x106;" k="17" />
    <hkern u1="&#x7b;" u2="&#x105;" k="19" />
    <hkern u1="&#x7b;" u2="&#x104;" k="9" />
    <hkern u1="&#x7b;" u2="&#x103;" k="19" />
    <hkern u1="&#x7b;" u2="&#x102;" k="9" />
    <hkern u1="&#x7b;" u2="&#x101;" k="19" />
    <hkern u1="&#x7b;" u2="&#x100;" k="9" />
    <hkern u1="&#x7b;" u2="&#xff;" k="18" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="18" />
    <hkern u1="&#x7b;" u2="&#xfc;" k="22" />
    <hkern u1="&#x7b;" u2="&#xfb;" k="22" />
    <hkern u1="&#x7b;" u2="&#xfa;" k="22" />
    <hkern u1="&#x7b;" u2="&#xf9;" k="22" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf1;" k="10" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="4" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-27" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-42" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="25" />
    <hkern u1="&#x7b;" u2="&#xea;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe6;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe5;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe4;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe3;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe2;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe1;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe0;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="19" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="19" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="17" />
    <hkern u1="&#x7b;" u2="&#xc6;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc5;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc4;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc3;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc2;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc1;" k="9" />
    <hkern u1="&#x7b;" u2="&#xc0;" k="9" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="15" />
    <hkern u1="&#x7b;" u2="z" k="8" />
    <hkern u1="&#x7b;" u2="y" k="18" />
    <hkern u1="&#x7b;" u2="x" k="7" />
    <hkern u1="&#x7b;" u2="w" k="21" />
    <hkern u1="&#x7b;" u2="v" k="18" />
    <hkern u1="&#x7b;" u2="u" k="22" />
    <hkern u1="&#x7b;" u2="t" k="8" />
    <hkern u1="&#x7b;" u2="s" k="10" />
    <hkern u1="&#x7b;" u2="r" k="10" />
    <hkern u1="&#x7b;" u2="q" k="25" />
    <hkern u1="&#x7b;" u2="p" k="10" />
    <hkern u1="&#x7b;" u2="o" k="25" />
    <hkern u1="&#x7b;" u2="n" k="10" />
    <hkern u1="&#x7b;" u2="m" k="10" />
    <hkern u1="&#x7b;" u2="j" k="-16" />
    <hkern u1="&#x7b;" u2="f" k="5" />
    <hkern u1="&#x7b;" u2="e" k="25" />
    <hkern u1="&#x7b;" u2="d" k="25" />
    <hkern u1="&#x7b;" u2="c" k="25" />
    <hkern u1="&#x7b;" u2="a" k="19" />
    <hkern u1="&#x7b;" u2="S" k="5" />
    <hkern u1="&#x7b;" u2="Q" k="19" />
    <hkern u1="&#x7b;" u2="O" k="19" />
    <hkern u1="&#x7b;" u2="G" k="19" />
    <hkern u1="&#x7b;" u2="C" k="17" />
    <hkern u1="&#x7b;" u2="A" k="9" />
    <hkern u1="&#x7b;" u2="&#x28;" k="4" />
    <hkern u1="&#x7c;" u2="&#xec;" k="-11" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="15" />
    <hkern u1="&#x7d;" u2="]" k="19" />
    <hkern u1="&#x7d;" u2="&#x29;" k="12" />
    <hkern u1="&#xa1;" u2="&#x1ef8;" k="29" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="29" />
    <hkern u1="&#xa1;" u2="&#x21a;" k="38" />
    <hkern u1="&#xa1;" u2="&#x178;" k="29" />
    <hkern u1="&#xa1;" u2="&#x176;" k="29" />
    <hkern u1="&#xa1;" u2="&#x166;" k="38" />
    <hkern u1="&#xa1;" u2="&#x164;" k="38" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="29" />
    <hkern u1="&#xa1;" u2="Y" k="29" />
    <hkern u1="&#xa1;" u2="V" k="3" />
    <hkern u1="&#xa1;" u2="T" k="38" />
    <hkern u1="&#xab;" u2="V" k="15" />
    <hkern u1="&#xae;" u2="&#x1ef8;" k="21" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="21" />
    <hkern u1="&#xae;" u2="&#x1eb0;" k="23" />
    <hkern u1="&#xae;" u2="&#x21a;" k="4" />
    <hkern u1="&#xae;" u2="&#x1fc;" k="23" />
    <hkern u1="&#xae;" u2="&#x1fa;" k="23" />
    <hkern u1="&#xae;" u2="&#x17d;" k="6" />
    <hkern u1="&#xae;" u2="&#x17b;" k="6" />
    <hkern u1="&#xae;" u2="&#x179;" k="6" />
    <hkern u1="&#xae;" u2="&#x178;" k="21" />
    <hkern u1="&#xae;" u2="&#x176;" k="21" />
    <hkern u1="&#xae;" u2="&#x166;" k="4" />
    <hkern u1="&#xae;" u2="&#x164;" k="4" />
    <hkern u1="&#xae;" u2="&#x134;" k="18" />
    <hkern u1="&#xae;" u2="&#x104;" k="23" />
    <hkern u1="&#xae;" u2="&#x102;" k="23" />
    <hkern u1="&#xae;" u2="&#x100;" k="23" />
    <hkern u1="&#xae;" u2="&#xdd;" k="21" />
    <hkern u1="&#xae;" u2="&#xc6;" k="29" />
    <hkern u1="&#xae;" u2="&#xc5;" k="23" />
    <hkern u1="&#xae;" u2="&#xc4;" k="23" />
    <hkern u1="&#xae;" u2="&#xc3;" k="23" />
    <hkern u1="&#xae;" u2="&#xc2;" k="23" />
    <hkern u1="&#xae;" u2="&#xc1;" k="23" />
    <hkern u1="&#xae;" u2="&#xc0;" k="23" />
    <hkern u1="&#xae;" u2="Z" k="6" />
    <hkern u1="&#xae;" u2="Y" k="21" />
    <hkern u1="&#xae;" u2="X" k="4" />
    <hkern u1="&#xae;" u2="V" k="3" />
    <hkern u1="&#xae;" u2="T" k="4" />
    <hkern u1="&#xae;" u2="J" k="18" />
    <hkern u1="&#xae;" u2="A" k="23" />
    <hkern u1="&#xb7;" u2="&#x142;" k="60" />
    <hkern u1="&#xb7;" u2="&#x13e;" k="60" />
    <hkern u1="&#xb7;" u2="&#x13c;" k="60" />
    <hkern u1="&#xb7;" u2="&#x13a;" k="60" />
    <hkern u1="&#xb7;" u2="l" k="60" />
    <hkern u1="&#xbb;" u2="&#x141;" k="-5" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="3" />
    <hkern u1="&#xbb;" u2="x" k="23" />
    <hkern u1="&#xbb;" u2="v" k="3" />
    <hkern u1="&#xbb;" u2="f" k="7" />
    <hkern u1="&#xbb;" u2="X" k="22" />
    <hkern u1="&#xbb;" u2="V" k="25" />
    <hkern u1="&#xbf;" u2="&#x1ef9;" k="27" />
    <hkern u1="&#xbf;" u2="&#x1ef8;" k="60" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="27" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="60" />
    <hkern u1="&#xbf;" u2="&#x1ed7;" k="27" />
    <hkern u1="&#xbf;" u2="&#x1ec5;" k="27" />
    <hkern u1="&#xbf;" u2="&#x1eb0;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1eab;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="31" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="31" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="31" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="24" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="68" />
    <hkern u1="&#xbf;" u2="&#x219;" k="25" />
    <hkern u1="&#xbf;" u2="&#x218;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ff;" k="27" />
    <hkern u1="&#xbf;" u2="&#x1fe;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1fd;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1fc;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1fb;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1fa;" k="26" />
    <hkern u1="&#xbf;" u2="&#x17e;" k="23" />
    <hkern u1="&#xbf;" u2="&#x17d;" k="25" />
    <hkern u1="&#xbf;" u2="&#x17c;" k="23" />
    <hkern u1="&#xbf;" u2="&#x17b;" k="25" />
    <hkern u1="&#xbf;" u2="&#x17a;" k="23" />
    <hkern u1="&#xbf;" u2="&#x179;" k="25" />
    <hkern u1="&#xbf;" u2="&#x178;" k="60" />
    <hkern u1="&#xbf;" u2="&#x177;" k="27" />
    <hkern u1="&#xbf;" u2="&#x176;" k="60" />
    <hkern u1="&#xbf;" u2="&#x175;" k="26" />
    <hkern u1="&#xbf;" u2="&#x174;" k="31" />
    <hkern u1="&#xbf;" u2="&#x173;" k="25" />
    <hkern u1="&#xbf;" u2="&#x172;" k="23" />
    <hkern u1="&#xbf;" u2="&#x171;" k="25" />
    <hkern u1="&#xbf;" u2="&#x170;" k="23" />
    <hkern u1="&#xbf;" u2="&#x16f;" k="25" />
    <hkern u1="&#xbf;" u2="&#x16e;" k="23" />
    <hkern u1="&#xbf;" u2="&#x16d;" k="25" />
    <hkern u1="&#xbf;" u2="&#x16c;" k="23" />
    <hkern u1="&#xbf;" u2="&#x16b;" k="25" />
    <hkern u1="&#xbf;" u2="&#x16a;" k="23" />
    <hkern u1="&#xbf;" u2="&#x169;" k="25" />
    <hkern u1="&#xbf;" u2="&#x168;" k="23" />
    <hkern u1="&#xbf;" u2="&#x167;" k="24" />
    <hkern u1="&#xbf;" u2="&#x166;" k="68" />
    <hkern u1="&#xbf;" u2="&#x165;" k="24" />
    <hkern u1="&#xbf;" u2="&#x164;" k="68" />
    <hkern u1="&#xbf;" u2="&#x161;" k="25" />
    <hkern u1="&#xbf;" u2="&#x160;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15f;" k="25" />
    <hkern u1="&#xbf;" u2="&#x15e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15d;" k="25" />
    <hkern u1="&#xbf;" u2="&#x15c;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15b;" k="25" />
    <hkern u1="&#xbf;" u2="&#x15a;" k="19" />
    <hkern u1="&#xbf;" u2="&#x159;" k="24" />
    <hkern u1="&#xbf;" u2="&#x158;" k="20" />
    <hkern u1="&#xbf;" u2="&#x157;" k="24" />
    <hkern u1="&#xbf;" u2="&#x156;" k="20" />
    <hkern u1="&#xbf;" u2="&#x155;" k="24" />
    <hkern u1="&#xbf;" u2="&#x154;" k="20" />
    <hkern u1="&#xbf;" u2="&#x153;" k="27" />
    <hkern u1="&#xbf;" u2="&#x152;" k="22" />
    <hkern u1="&#xbf;" u2="&#x151;" k="27" />
    <hkern u1="&#xbf;" u2="&#x150;" k="22" />
    <hkern u1="&#xbf;" u2="&#x14f;" k="27" />
    <hkern u1="&#xbf;" u2="&#x14e;" k="22" />
    <hkern u1="&#xbf;" u2="&#x14d;" k="27" />
    <hkern u1="&#xbf;" u2="&#x14c;" k="22" />
    <hkern u1="&#xbf;" u2="&#x14b;" k="24" />
    <hkern u1="&#xbf;" u2="&#x14a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x148;" k="24" />
    <hkern u1="&#xbf;" u2="&#x147;" k="20" />
    <hkern u1="&#xbf;" u2="&#x146;" k="24" />
    <hkern u1="&#xbf;" u2="&#x145;" k="20" />
    <hkern u1="&#xbf;" u2="&#x144;" k="24" />
    <hkern u1="&#xbf;" u2="&#x143;" k="20" />
    <hkern u1="&#xbf;" u2="&#x142;" k="24" />
    <hkern u1="&#xbf;" u2="&#x141;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13e;" k="24" />
    <hkern u1="&#xbf;" u2="&#x13d;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13c;" k="24" />
    <hkern u1="&#xbf;" u2="&#x13b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x13a;" k="24" />
    <hkern u1="&#xbf;" u2="&#x139;" k="20" />
    <hkern u1="&#xbf;" u2="&#x137;" k="24" />
    <hkern u1="&#xbf;" u2="&#x136;" k="20" />
    <hkern u1="&#xbf;" u2="&#x135;" k="24" />
    <hkern u1="&#xbf;" u2="&#x134;" k="6" />
    <hkern u1="&#xbf;" u2="&#x131;" k="24" />
    <hkern u1="&#xbf;" u2="&#x130;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12f;" k="13" />
    <hkern u1="&#xbf;" u2="&#x12e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x12d;" k="24" />
    <hkern u1="&#xbf;" u2="&#x12c;" k="20" />
    <hkern u1="&#xbf;" u2="&#x12b;" k="24" />
    <hkern u1="&#xbf;" u2="&#x12a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x129;" k="24" />
    <hkern u1="&#xbf;" u2="&#x128;" k="20" />
    <hkern u1="&#xbf;" u2="&#x127;" k="24" />
    <hkern u1="&#xbf;" u2="&#x126;" k="20" />
    <hkern u1="&#xbf;" u2="&#x125;" k="24" />
    <hkern u1="&#xbf;" u2="&#x124;" k="20" />
    <hkern u1="&#xbf;" u2="&#x122;" k="22" />
    <hkern u1="&#xbf;" u2="&#x120;" k="22" />
    <hkern u1="&#xbf;" u2="&#x11e;" k="22" />
    <hkern u1="&#xbf;" u2="&#x11c;" k="22" />
    <hkern u1="&#xbf;" u2="&#x11b;" k="27" />
    <hkern u1="&#xbf;" u2="&#x11a;" k="20" />
    <hkern u1="&#xbf;" u2="&#x119;" k="27" />
    <hkern u1="&#xbf;" u2="&#x118;" k="20" />
    <hkern u1="&#xbf;" u2="&#x117;" k="27" />
    <hkern u1="&#xbf;" u2="&#x116;" k="20" />
    <hkern u1="&#xbf;" u2="&#x115;" k="27" />
    <hkern u1="&#xbf;" u2="&#x114;" k="20" />
    <hkern u1="&#xbf;" u2="&#x113;" k="27" />
    <hkern u1="&#xbf;" u2="&#x112;" k="20" />
    <hkern u1="&#xbf;" u2="&#x111;" k="27" />
    <hkern u1="&#xbf;" u2="&#x110;" k="20" />
    <hkern u1="&#xbf;" u2="&#x10f;" k="27" />
    <hkern u1="&#xbf;" u2="&#x10e;" k="20" />
    <hkern u1="&#xbf;" u2="&#x10d;" k="27" />
    <hkern u1="&#xbf;" u2="&#x10c;" k="21" />
    <hkern u1="&#xbf;" u2="&#x10b;" k="27" />
    <hkern u1="&#xbf;" u2="&#x10a;" k="21" />
    <hkern u1="&#xbf;" u2="&#x109;" k="27" />
    <hkern u1="&#xbf;" u2="&#x108;" k="21" />
    <hkern u1="&#xbf;" u2="&#x107;" k="27" />
    <hkern u1="&#xbf;" u2="&#x106;" k="21" />
    <hkern u1="&#xbf;" u2="&#x105;" k="26" />
    <hkern u1="&#xbf;" u2="&#x104;" k="26" />
    <hkern u1="&#xbf;" u2="&#x103;" k="26" />
    <hkern u1="&#xbf;" u2="&#x102;" k="26" />
    <hkern u1="&#xbf;" u2="&#x101;" k="26" />
    <hkern u1="&#xbf;" u2="&#x100;" k="26" />
    <hkern u1="&#xbf;" u2="&#xff;" k="27" />
    <hkern u1="&#xbf;" u2="&#xfe;" k="24" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="27" />
    <hkern u1="&#xbf;" u2="&#xfc;" k="25" />
    <hkern u1="&#xbf;" u2="&#xfb;" k="25" />
    <hkern u1="&#xbf;" u2="&#xfa;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf9;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf8;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="27" />
    <hkern u1="&#xbf;" u2="&#xf1;" k="24" />
    <hkern u1="&#xbf;" u2="&#xf0;" k="28" />
    <hkern u1="&#xbf;" u2="&#xef;" k="24" />
    <hkern u1="&#xbf;" u2="&#xee;" k="24" />
    <hkern u1="&#xbf;" u2="&#xed;" k="24" />
    <hkern u1="&#xbf;" u2="&#xec;" k="24" />
    <hkern u1="&#xbf;" u2="&#xeb;" k="27" />
    <hkern u1="&#xbf;" u2="&#xea;" k="27" />
    <hkern u1="&#xbf;" u2="&#xe9;" k="27" />
    <hkern u1="&#xbf;" u2="&#xe8;" k="27" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="27" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe5;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe4;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe3;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe2;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe1;" k="26" />
    <hkern u1="&#xbf;" u2="&#xe0;" k="26" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="24" />
    <hkern u1="&#xbf;" u2="&#xde;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="60" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="23" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="23" />
    <hkern u1="&#xbf;" u2="&#xda;" k="23" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="23" />
    <hkern u1="&#xbf;" u2="&#xd8;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="22" />
    <hkern u1="&#xbf;" u2="&#xd1;" k="20" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcf;" k="20" />
    <hkern u1="&#xbf;" u2="&#xce;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcd;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcc;" k="20" />
    <hkern u1="&#xbf;" u2="&#xcb;" k="20" />
    <hkern u1="&#xbf;" u2="&#xca;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc9;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc8;" k="20" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="21" />
    <hkern u1="&#xbf;" u2="&#xc6;" k="27" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="26" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="26" />
    <hkern u1="&#xbf;" u2="z" k="23" />
    <hkern u1="&#xbf;" u2="y" k="27" />
    <hkern u1="&#xbf;" u2="x" k="21" />
    <hkern u1="&#xbf;" u2="w" k="26" />
    <hkern u1="&#xbf;" u2="v" k="28" />
    <hkern u1="&#xbf;" u2="u" k="25" />
    <hkern u1="&#xbf;" u2="t" k="24" />
    <hkern u1="&#xbf;" u2="s" k="25" />
    <hkern u1="&#xbf;" u2="r" k="24" />
    <hkern u1="&#xbf;" u2="q" k="27" />
    <hkern u1="&#xbf;" u2="p" k="24" />
    <hkern u1="&#xbf;" u2="o" k="27" />
    <hkern u1="&#xbf;" u2="n" k="24" />
    <hkern u1="&#xbf;" u2="m" k="24" />
    <hkern u1="&#xbf;" u2="l" k="24" />
    <hkern u1="&#xbf;" u2="k" k="24" />
    <hkern u1="&#xbf;" u2="j" k="24" />
    <hkern u1="&#xbf;" u2="i" k="24" />
    <hkern u1="&#xbf;" u2="h" k="24" />
    <hkern u1="&#xbf;" u2="f" k="23" />
    <hkern u1="&#xbf;" u2="e" k="27" />
    <hkern u1="&#xbf;" u2="d" k="27" />
    <hkern u1="&#xbf;" u2="c" k="27" />
    <hkern u1="&#xbf;" u2="b" k="24" />
    <hkern u1="&#xbf;" u2="a" k="26" />
    <hkern u1="&#xbf;" u2="Z" k="25" />
    <hkern u1="&#xbf;" u2="Y" k="60" />
    <hkern u1="&#xbf;" u2="X" k="26" />
    <hkern u1="&#xbf;" u2="W" k="31" />
    <hkern u1="&#xbf;" u2="V" k="38" />
    <hkern u1="&#xbf;" u2="U" k="23" />
    <hkern u1="&#xbf;" u2="T" k="68" />
    <hkern u1="&#xbf;" u2="S" k="19" />
    <hkern u1="&#xbf;" u2="R" k="20" />
    <hkern u1="&#xbf;" u2="Q" k="22" />
    <hkern u1="&#xbf;" u2="P" k="20" />
    <hkern u1="&#xbf;" u2="O" k="22" />
    <hkern u1="&#xbf;" u2="N" k="20" />
    <hkern u1="&#xbf;" u2="M" k="20" />
    <hkern u1="&#xbf;" u2="L" k="20" />
    <hkern u1="&#xbf;" u2="K" k="20" />
    <hkern u1="&#xbf;" u2="J" k="6" />
    <hkern u1="&#xbf;" u2="I" k="20" />
    <hkern u1="&#xbf;" u2="H" k="20" />
    <hkern u1="&#xbf;" u2="G" k="22" />
    <hkern u1="&#xbf;" u2="F" k="20" />
    <hkern u1="&#xbf;" u2="E" k="20" />
    <hkern u1="&#xbf;" u2="D" k="20" />
    <hkern u1="&#xbf;" u2="C" k="21" />
    <hkern u1="&#xbf;" u2="B" k="20" />
    <hkern u1="&#xbf;" u2="A" k="26" />
    <hkern u1="&#xc0;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc0;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc0;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc0;" u2="&#xae;" k="21" />
    <hkern u1="&#xc0;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc0;" u2="v" k="16" />
    <hkern u1="&#xc0;" u2="f" k="8" />
    <hkern u1="&#xc0;" u2="]" k="10" />
    <hkern u1="&#xc0;" u2="\" k="44" />
    <hkern u1="&#xc0;" u2="V" k="28" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc1;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc1;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc1;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc1;" u2="&#xae;" k="21" />
    <hkern u1="&#xc1;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc1;" u2="v" k="16" />
    <hkern u1="&#xc1;" u2="f" k="8" />
    <hkern u1="&#xc1;" u2="]" k="10" />
    <hkern u1="&#xc1;" u2="\" k="44" />
    <hkern u1="&#xc1;" u2="V" k="28" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc2;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc2;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc2;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc2;" u2="&#xae;" k="21" />
    <hkern u1="&#xc2;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc2;" u2="v" k="16" />
    <hkern u1="&#xc2;" u2="f" k="8" />
    <hkern u1="&#xc2;" u2="]" k="10" />
    <hkern u1="&#xc2;" u2="\" k="44" />
    <hkern u1="&#xc2;" u2="V" k="28" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc3;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc3;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc3;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc3;" u2="&#xae;" k="21" />
    <hkern u1="&#xc3;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc3;" u2="v" k="16" />
    <hkern u1="&#xc3;" u2="f" k="8" />
    <hkern u1="&#xc3;" u2="]" k="10" />
    <hkern u1="&#xc3;" u2="\" k="44" />
    <hkern u1="&#xc3;" u2="V" k="28" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc4;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc4;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc4;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc4;" u2="&#xae;" k="21" />
    <hkern u1="&#xc4;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc4;" u2="v" k="16" />
    <hkern u1="&#xc4;" u2="f" k="8" />
    <hkern u1="&#xc4;" u2="]" k="10" />
    <hkern u1="&#xc4;" u2="\" k="44" />
    <hkern u1="&#xc4;" u2="V" k="28" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc5;" g2="braceright.cap" k="6" />
    <hkern u1="&#xc5;" g2="bracketright.cap" k="8" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="35" />
    <hkern u1="&#xc5;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc5;" u2="&#xae;" k="21" />
    <hkern u1="&#xc5;" u2="&#x7d;" k="9" />
    <hkern u1="&#xc5;" u2="v" k="16" />
    <hkern u1="&#xc5;" u2="f" k="8" />
    <hkern u1="&#xc5;" u2="]" k="10" />
    <hkern u1="&#xc5;" u2="\" k="44" />
    <hkern u1="&#xc5;" u2="V" k="28" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="19" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="31" />
    <hkern u1="&#xc6;" u2="&#x135;" k="-16" />
    <hkern u1="&#xc6;" u2="&#x12d;" k="-8" />
    <hkern u1="&#xc6;" u2="&#x12b;" k="-3" />
    <hkern u1="&#xc6;" u2="&#x129;" k="-23" />
    <hkern u1="&#xc6;" u2="&#xf0;" k="7" />
    <hkern u1="&#xc6;" u2="&#xef;" k="-24" />
    <hkern u1="&#xc6;" u2="&#xee;" k="-21" />
    <hkern u1="&#xc6;" u2="&#xec;" k="-39" />
    <hkern u1="&#xc6;" u2="v" k="5" />
    <hkern u1="&#xc7;" u2="&#x135;" k="-16" />
    <hkern u1="&#xc7;" u2="&#x12d;" k="-12" />
    <hkern u1="&#xc7;" u2="&#x12b;" k="-4" />
    <hkern u1="&#xc7;" u2="&#x129;" k="-26" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="7" />
    <hkern u1="&#xc7;" u2="&#xef;" k="-26" />
    <hkern u1="&#xc7;" u2="&#xee;" k="-21" />
    <hkern u1="&#xc7;" u2="&#xec;" k="-39" />
    <hkern u1="&#xc7;" u2="&#xae;" k="5" />
    <hkern u1="&#xc7;" u2="v" k="10" />
    <hkern u1="&#xc7;" u2="f" k="5" />
    <hkern u1="&#xc8;" u2="&#x135;" k="-16" />
    <hkern u1="&#xc8;" u2="&#x12d;" k="-8" />
    <hkern u1="&#xc8;" u2="&#x12b;" k="-3" />
    <hkern u1="&#xc8;" u2="&#x129;" k="-23" />
    <hkern u1="&#xc8;" u2="&#xf0;" k="7" />
    <hkern u1="&#xc8;" u2="&#xef;" k="-24" />
    <hkern u1="&#xc8;" u2="&#xee;" k="-21" />
    <hkern u1="&#xc8;" u2="&#xec;" k="-39" />
    <hkern u1="&#xc8;" u2="v" k="5" />
    <hkern u1="&#xc9;" u2="&#x135;" k="-16" />
    <hkern u1="&#xc9;" u2="&#x12d;" k="-8" />
    <hkern u1="&#xc9;" u2="&#x12b;" k="-3" />
    <hkern u1="&#xc9;" u2="&#x129;" k="-23" />
    <hkern u1="&#xc9;" u2="&#xf0;" k="7" />
    <hkern u1="&#xc9;" u2="&#xef;" k="-24" />
    <hkern u1="&#xc9;" u2="&#xee;" k="-21" />
    <hkern u1="&#xc9;" u2="&#xec;" k="-39" />
    <hkern u1="&#xc9;" u2="v" k="5" />
    <hkern u1="&#xca;" u2="&#x135;" k="-16" />
    <hkern u1="&#xca;" u2="&#x12d;" k="-8" />
    <hkern u1="&#xca;" u2="&#x12b;" k="-3" />
    <hkern u1="&#xca;" u2="&#x129;" k="-23" />
    <hkern u1="&#xca;" u2="&#xf0;" k="7" />
    <hkern u1="&#xca;" u2="&#xef;" k="-24" />
    <hkern u1="&#xca;" u2="&#xee;" k="-21" />
    <hkern u1="&#xca;" u2="&#xec;" k="-39" />
    <hkern u1="&#xca;" u2="v" k="5" />
    <hkern u1="&#xcb;" u2="&#x135;" k="-16" />
    <hkern u1="&#xcb;" u2="&#x12d;" k="-8" />
    <hkern u1="&#xcb;" u2="&#x12b;" k="-3" />
    <hkern u1="&#xcb;" u2="&#x129;" k="-23" />
    <hkern u1="&#xcb;" u2="&#xf0;" k="7" />
    <hkern u1="&#xcb;" u2="&#xef;" k="-24" />
    <hkern u1="&#xcb;" u2="&#xee;" k="-21" />
    <hkern u1="&#xcb;" u2="&#xec;" k="-39" />
    <hkern u1="&#xcb;" u2="v" k="5" />
    <hkern u1="&#xcc;" g2="braceright.cap" k="4" />
    <hkern u1="&#xcc;" g2="bracketright.cap" k="4" />
    <hkern u1="&#xcc;" u2="&#xf0;" k="6" />
    <hkern u1="&#xcc;" u2="&#xec;" k="-6" />
    <hkern u1="&#xcc;" u2="f" k="4" />
    <hkern u1="&#xcd;" g2="braceright.cap" k="4" />
    <hkern u1="&#xcd;" g2="bracketright.cap" k="4" />
    <hkern u1="&#xcd;" u2="&#xf0;" k="6" />
    <hkern u1="&#xcd;" u2="&#xec;" k="-6" />
    <hkern u1="&#xcd;" u2="f" k="4" />
    <hkern u1="&#xce;" g2="braceright.cap" k="4" />
    <hkern u1="&#xce;" g2="bracketright.cap" k="4" />
    <hkern u1="&#xce;" g2="parenright.cap" k="-30" />
    <hkern u1="&#xce;" u2="&#xf0;" k="6" />
    <hkern u1="&#xce;" u2="&#xec;" k="-6" />
    <hkern u1="&#xce;" u2="f" k="4" />
    <hkern u1="&#xcf;" g2="braceright.cap" k="4" />
    <hkern u1="&#xcf;" g2="bracketright.cap" k="4" />
    <hkern u1="&#xcf;" g2="parenright.cap" k="-22" />
    <hkern u1="&#xcf;" u2="&#xf0;" k="6" />
    <hkern u1="&#xcf;" u2="&#xec;" k="-6" />
    <hkern u1="&#xcf;" u2="f" k="4" />
    <hkern u1="&#xd0;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd0;" g2="bracketright.cap" k="25" />
    <hkern u1="&#xd0;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="15" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="18" />
    <hkern u1="&#xd0;" u2="]" k="23" />
    <hkern u1="&#xd0;" u2="\" k="13" />
    <hkern u1="&#xd0;" u2="X" k="19" />
    <hkern u1="&#xd0;" u2="V" k="11" />
    <hkern u1="&#xd0;" u2="&#x3f;" k="4" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="11" />
    <hkern u1="&#xd0;" u2="&#x29;" k="12" />
    <hkern u1="&#xd1;" g2="braceright.cap" k="4" />
    <hkern u1="&#xd1;" g2="bracketright.cap" k="4" />
    <hkern u1="&#xd1;" u2="&#xf0;" k="6" />
    <hkern u1="&#xd1;" u2="&#xec;" k="-6" />
    <hkern u1="&#xd1;" u2="f" k="4" />
    <hkern u1="&#xd2;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd2;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd2;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd2;" u2="]" k="24" />
    <hkern u1="&#xd2;" u2="\" k="14" />
    <hkern u1="&#xd2;" u2="X" k="18" />
    <hkern u1="&#xd2;" u2="V" k="11" />
    <hkern u1="&#xd2;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd2;" u2="&#x29;" k="10" />
    <hkern u1="&#xd3;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd3;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd3;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd3;" u2="]" k="24" />
    <hkern u1="&#xd3;" u2="\" k="14" />
    <hkern u1="&#xd3;" u2="X" k="18" />
    <hkern u1="&#xd3;" u2="V" k="11" />
    <hkern u1="&#xd3;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd3;" u2="&#x29;" k="10" />
    <hkern u1="&#xd4;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd4;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd4;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd4;" u2="]" k="24" />
    <hkern u1="&#xd4;" u2="\" k="14" />
    <hkern u1="&#xd4;" u2="X" k="18" />
    <hkern u1="&#xd4;" u2="V" k="11" />
    <hkern u1="&#xd4;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd4;" u2="&#x29;" k="10" />
    <hkern u1="&#xd5;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd5;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd5;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd5;" u2="]" k="24" />
    <hkern u1="&#xd5;" u2="\" k="14" />
    <hkern u1="&#xd5;" u2="X" k="18" />
    <hkern u1="&#xd5;" u2="V" k="11" />
    <hkern u1="&#xd5;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd5;" u2="&#x29;" k="10" />
    <hkern u1="&#xd6;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd6;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd6;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd6;" u2="]" k="24" />
    <hkern u1="&#xd6;" u2="\" k="14" />
    <hkern u1="&#xd6;" u2="X" k="18" />
    <hkern u1="&#xd6;" u2="V" k="11" />
    <hkern u1="&#xd6;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd6;" u2="&#x29;" k="10" />
    <hkern u1="&#xd8;" g2="braceright.cap" k="19" />
    <hkern u1="&#xd8;" g2="bracketright.cap" k="24" />
    <hkern u1="&#xd8;" g2="parenright.cap" k="13" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd8;" u2="&#x7d;" k="17" />
    <hkern u1="&#xd8;" u2="]" k="24" />
    <hkern u1="&#xd8;" u2="\" k="14" />
    <hkern u1="&#xd8;" u2="X" k="18" />
    <hkern u1="&#xd8;" u2="V" k="11" />
    <hkern u1="&#xd8;" u2="&#x3f;" k="3" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="10" />
    <hkern u1="&#xd8;" u2="&#x29;" k="10" />
    <hkern u1="&#xd9;" g2="braceright.cap" k="5" />
    <hkern u1="&#xd9;" g2="bracketright.cap" k="5" />
    <hkern u1="&#xd9;" u2="&#xf0;" k="6" />
    <hkern u1="&#xd9;" u2="&#xec;" k="-10" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="8" />
    <hkern u1="&#xd9;" u2="f" k="4" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="12" />
    <hkern u1="&#xda;" g2="braceright.cap" k="5" />
    <hkern u1="&#xda;" g2="bracketright.cap" k="5" />
    <hkern u1="&#xda;" u2="&#xf0;" k="6" />
    <hkern u1="&#xda;" u2="&#xec;" k="-10" />
    <hkern u1="&#xda;" u2="&#xc6;" k="8" />
    <hkern u1="&#xda;" u2="f" k="4" />
    <hkern u1="&#xda;" u2="&#x2f;" k="12" />
    <hkern u1="&#xdb;" g2="braceright.cap" k="5" />
    <hkern u1="&#xdb;" g2="bracketright.cap" k="5" />
    <hkern u1="&#xdb;" u2="&#xf0;" k="6" />
    <hkern u1="&#xdb;" u2="&#xec;" k="-10" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="8" />
    <hkern u1="&#xdb;" u2="f" k="4" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="12" />
    <hkern u1="&#xdc;" g2="braceright.cap" k="5" />
    <hkern u1="&#xdc;" g2="bracketright.cap" k="5" />
    <hkern u1="&#xdc;" u2="&#xf0;" k="6" />
    <hkern u1="&#xdc;" u2="&#xec;" k="-10" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="8" />
    <hkern u1="&#xdc;" u2="f" k="4" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="12" />
    <hkern u1="&#xdd;" g2="braceright.cap" k="5" />
    <hkern u1="&#xdd;" g2="bracketright.cap" k="6" />
    <hkern u1="&#xdd;" u2="&#x1ef9;" k="26" />
    <hkern u1="&#xdd;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#xdd;" u2="&#x159;" k="32" />
    <hkern u1="&#xdd;" u2="&#x155;" k="38" />
    <hkern u1="&#xdd;" u2="&#x151;" k="54" />
    <hkern u1="&#xdd;" u2="&#x142;" k="6" />
    <hkern u1="&#xdd;" u2="&#x135;" k="-11" />
    <hkern u1="&#xdd;" u2="&#x131;" k="60" />
    <hkern u1="&#xdd;" u2="&#x12d;" k="-45" />
    <hkern u1="&#xdd;" u2="&#x12b;" k="-34" />
    <hkern u1="&#xdd;" u2="&#x129;" k="-47" />
    <hkern u1="&#xdd;" u2="&#x103;" k="59" />
    <hkern u1="&#xdd;" u2="&#xff;" k="28" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="32" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-59" />
    <hkern u1="&#xdd;" u2="&#xee;" k="-16" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-63" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="64" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="49" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="42" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="10" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="56" />
    <hkern u1="&#xdd;" u2="&#xae;" k="20" />
    <hkern u1="&#xdd;" u2="x" k="37" />
    <hkern u1="&#xdd;" u2="v" k="36" />
    <hkern u1="&#xdd;" u2="f" k="21" />
    <hkern u1="&#xdd;" u2="&#x40;" k="36" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="65" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-4" />
    <hkern u1="&#xdd;" u2="&#x26;" k="32" />
    <hkern u1="&#xde;" g2="braceright.cap" k="23" />
    <hkern u1="&#xde;" g2="bracketright.cap" k="32" />
    <hkern u1="&#xde;" g2="parenright.cap" k="17" />
    <hkern u1="&#xde;" u2="&#x2122;" k="3" />
    <hkern u1="&#xde;" u2="&#x2026;" k="24" />
    <hkern u1="&#xde;" u2="&#x201e;" k="24" />
    <hkern u1="&#xde;" u2="&#x201a;" k="24" />
    <hkern u1="&#xde;" u2="&#x1ef8;" k="37" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="37" />
    <hkern u1="&#xde;" u2="&#x1eb0;" k="16" />
    <hkern u1="&#xde;" u2="&#x1e84;" k="6" />
    <hkern u1="&#xde;" u2="&#x1e82;" k="6" />
    <hkern u1="&#xde;" u2="&#x1e80;" k="6" />
    <hkern u1="&#xde;" u2="&#x21a;" k="32" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="16" />
    <hkern u1="&#xde;" u2="&#x1fa;" k="16" />
    <hkern u1="&#xde;" u2="&#x17d;" k="14" />
    <hkern u1="&#xde;" u2="&#x17b;" k="14" />
    <hkern u1="&#xde;" u2="&#x179;" k="14" />
    <hkern u1="&#xde;" u2="&#x178;" k="37" />
    <hkern u1="&#xde;" u2="&#x176;" k="37" />
    <hkern u1="&#xde;" u2="&#x174;" k="6" />
    <hkern u1="&#xde;" u2="&#x166;" k="32" />
    <hkern u1="&#xde;" u2="&#x164;" k="32" />
    <hkern u1="&#xde;" u2="&#x134;" k="14" />
    <hkern u1="&#xde;" u2="&#x104;" k="16" />
    <hkern u1="&#xde;" u2="&#x102;" k="16" />
    <hkern u1="&#xde;" u2="&#x100;" k="16" />
    <hkern u1="&#xde;" u2="&#xdd;" k="37" />
    <hkern u1="&#xde;" u2="&#xc6;" k="19" />
    <hkern u1="&#xde;" u2="&#xc5;" k="16" />
    <hkern u1="&#xde;" u2="&#xc4;" k="16" />
    <hkern u1="&#xde;" u2="&#xc3;" k="16" />
    <hkern u1="&#xde;" u2="&#xc2;" k="16" />
    <hkern u1="&#xde;" u2="&#xc1;" k="16" />
    <hkern u1="&#xde;" u2="&#xc0;" k="16" />
    <hkern u1="&#xde;" u2="&#x7d;" k="23" />
    <hkern u1="&#xde;" u2="]" k="32" />
    <hkern u1="&#xde;" u2="\" k="21" />
    <hkern u1="&#xde;" u2="Z" k="14" />
    <hkern u1="&#xde;" u2="Y" k="37" />
    <hkern u1="&#xde;" u2="X" k="36" />
    <hkern u1="&#xde;" u2="W" k="6" />
    <hkern u1="&#xde;" u2="V" k="14" />
    <hkern u1="&#xde;" u2="T" k="32" />
    <hkern u1="&#xde;" u2="J" k="14" />
    <hkern u1="&#xde;" u2="A" k="16" />
    <hkern u1="&#xde;" u2="&#x3f;" k="8" />
    <hkern u1="&#xde;" u2="&#x2f;" k="21" />
    <hkern u1="&#xde;" u2="&#x2e;" k="24" />
    <hkern u1="&#xde;" u2="&#x2c;" k="24" />
    <hkern u1="&#xde;" u2="&#x29;" k="16" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="10" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="13" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="15" />
    <hkern u1="&#xdf;" u2="&#x2019;" k="13" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="15" />
    <hkern u1="&#xdf;" u2="&#x1ef9;" k="15" />
    <hkern u1="&#xdf;" u2="&#x1ef8;" k="38" />
    <hkern u1="&#xdf;" u2="&#x1ef3;" k="15" />
    <hkern u1="&#xdf;" u2="&#x1ef2;" k="38" />
    <hkern u1="&#xdf;" u2="&#x1e85;" k="8" />
    <hkern u1="&#xdf;" u2="&#x1e84;" k="16" />
    <hkern u1="&#xdf;" u2="&#x1e83;" k="8" />
    <hkern u1="&#xdf;" u2="&#x1e82;" k="16" />
    <hkern u1="&#xdf;" u2="&#x1e81;" k="8" />
    <hkern u1="&#xdf;" u2="&#x1e80;" k="16" />
    <hkern u1="&#xdf;" u2="&#x21b;" k="7" />
    <hkern u1="&#xdf;" u2="&#x21a;" k="23" />
    <hkern u1="&#xdf;" u2="&#x178;" k="38" />
    <hkern u1="&#xdf;" u2="&#x177;" k="15" />
    <hkern u1="&#xdf;" u2="&#x176;" k="38" />
    <hkern u1="&#xdf;" u2="&#x175;" k="8" />
    <hkern u1="&#xdf;" u2="&#x174;" k="16" />
    <hkern u1="&#xdf;" u2="&#x172;" k="4" />
    <hkern u1="&#xdf;" u2="&#x170;" k="4" />
    <hkern u1="&#xdf;" u2="&#x16e;" k="4" />
    <hkern u1="&#xdf;" u2="&#x16c;" k="4" />
    <hkern u1="&#xdf;" u2="&#x16a;" k="4" />
    <hkern u1="&#xdf;" u2="&#x168;" k="4" />
    <hkern u1="&#xdf;" u2="&#x167;" k="7" />
    <hkern u1="&#xdf;" u2="&#x166;" k="23" />
    <hkern u1="&#xdf;" u2="&#x165;" k="7" />
    <hkern u1="&#xdf;" u2="&#x164;" k="23" />
    <hkern u1="&#xdf;" u2="&#x134;" k="9" />
    <hkern u1="&#xdf;" u2="&#x123;" k="5" />
    <hkern u1="&#xdf;" u2="&#x121;" k="5" />
    <hkern u1="&#xdf;" u2="&#x11f;" k="5" />
    <hkern u1="&#xdf;" u2="&#x11d;" k="5" />
    <hkern u1="&#xdf;" u2="&#xff;" k="15" />
    <hkern u1="&#xdf;" u2="&#xfd;" k="15" />
    <hkern u1="&#xdf;" u2="&#xdd;" k="38" />
    <hkern u1="&#xdf;" u2="&#xdc;" k="4" />
    <hkern u1="&#xdf;" u2="&#xdb;" k="4" />
    <hkern u1="&#xdf;" u2="&#xda;" k="4" />
    <hkern u1="&#xdf;" u2="&#xd9;" k="4" />
    <hkern u1="&#xdf;" u2="&#xae;" k="12" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="7" />
    <hkern u1="&#xdf;" u2="y" k="15" />
    <hkern u1="&#xdf;" u2="x" k="6" />
    <hkern u1="&#xdf;" u2="w" k="8" />
    <hkern u1="&#xdf;" u2="v" k="13" />
    <hkern u1="&#xdf;" u2="t" k="7" />
    <hkern u1="&#xdf;" u2="g" k="5" />
    <hkern u1="&#xdf;" u2="f" k="6" />
    <hkern u1="&#xdf;" u2="]" k="15" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="Y" k="38" />
    <hkern u1="&#xdf;" u2="X" k="5" />
    <hkern u1="&#xdf;" u2="W" k="16" />
    <hkern u1="&#xdf;" u2="V" k="24" />
    <hkern u1="&#xdf;" u2="U" k="4" />
    <hkern u1="&#xdf;" u2="T" k="23" />
    <hkern u1="&#xdf;" u2="J" k="9" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="4" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="13" />
    <hkern u1="&#xdf;" u2="&#x27;" k="12" />
    <hkern u1="&#xdf;" u2="&#x22;" k="12" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe0;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe0;" u2="v" k="5" />
    <hkern u1="&#xe0;" u2="]" k="5" />
    <hkern u1="&#xe0;" u2="\" k="40" />
    <hkern u1="&#xe0;" u2="V" k="27" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe1;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe1;" u2="v" k="5" />
    <hkern u1="&#xe1;" u2="]" k="5" />
    <hkern u1="&#xe1;" u2="\" k="40" />
    <hkern u1="&#xe1;" u2="V" k="27" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe2;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe2;" u2="v" k="5" />
    <hkern u1="&#xe2;" u2="]" k="5" />
    <hkern u1="&#xe2;" u2="\" k="40" />
    <hkern u1="&#xe2;" u2="V" k="27" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe3;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe3;" u2="v" k="5" />
    <hkern u1="&#xe3;" u2="]" k="5" />
    <hkern u1="&#xe3;" u2="\" k="40" />
    <hkern u1="&#xe3;" u2="V" k="27" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe4;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe4;" u2="v" k="5" />
    <hkern u1="&#xe4;" u2="]" k="5" />
    <hkern u1="&#xe4;" u2="\" k="40" />
    <hkern u1="&#xe4;" u2="V" k="27" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="14" />
    <hkern u1="&#xe5;" u2="&#x7d;" k="4" />
    <hkern u1="&#xe5;" u2="v" k="5" />
    <hkern u1="&#xe5;" u2="]" k="5" />
    <hkern u1="&#xe5;" u2="\" k="40" />
    <hkern u1="&#xe5;" u2="V" k="27" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="14" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="6" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="13" />
    <hkern u1="&#xe6;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="18" />
    <hkern u1="&#xe6;" u2="v" k="7" />
    <hkern u1="&#xe6;" u2="]" k="11" />
    <hkern u1="&#xe6;" u2="\" k="36" />
    <hkern u1="&#xe6;" u2="X" k="4" />
    <hkern u1="&#xe6;" u2="V" k="28" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe6;" u2="&#x29;" k="9" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="9" />
    <hkern u1="&#xe7;" u2="&#x7d;" k="7" />
    <hkern u1="&#xe7;" u2="]" k="8" />
    <hkern u1="&#xe7;" u2="\" k="18" />
    <hkern u1="&#xe7;" u2="V" k="12" />
    <hkern u1="&#xe7;" u2="&#x3f;" k="4" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="13" />
    <hkern u1="&#xe8;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="18" />
    <hkern u1="&#xe8;" u2="v" k="7" />
    <hkern u1="&#xe8;" u2="]" k="11" />
    <hkern u1="&#xe8;" u2="\" k="36" />
    <hkern u1="&#xe8;" u2="X" k="4" />
    <hkern u1="&#xe8;" u2="V" k="28" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe8;" u2="&#x29;" k="9" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="13" />
    <hkern u1="&#xe9;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="18" />
    <hkern u1="&#xe9;" u2="v" k="7" />
    <hkern u1="&#xe9;" u2="]" k="11" />
    <hkern u1="&#xe9;" u2="\" k="36" />
    <hkern u1="&#xe9;" u2="X" k="4" />
    <hkern u1="&#xe9;" u2="V" k="28" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe9;" u2="&#x29;" k="9" />
    <hkern u1="&#xea;" u2="&#x2122;" k="13" />
    <hkern u1="&#xea;" u2="&#xc6;" k="5" />
    <hkern u1="&#xea;" u2="&#x7d;" k="18" />
    <hkern u1="&#xea;" u2="v" k="7" />
    <hkern u1="&#xea;" u2="]" k="11" />
    <hkern u1="&#xea;" u2="\" k="36" />
    <hkern u1="&#xea;" u2="X" k="4" />
    <hkern u1="&#xea;" u2="V" k="28" />
    <hkern u1="&#xea;" u2="&#x3f;" k="15" />
    <hkern u1="&#xea;" u2="&#x29;" k="9" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="13" />
    <hkern u1="&#xeb;" u2="&#xc6;" k="5" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="18" />
    <hkern u1="&#xeb;" u2="v" k="7" />
    <hkern u1="&#xeb;" u2="]" k="11" />
    <hkern u1="&#xeb;" u2="\" k="36" />
    <hkern u1="&#xeb;" u2="X" k="4" />
    <hkern u1="&#xeb;" u2="V" k="28" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="15" />
    <hkern u1="&#xeb;" u2="&#x29;" k="9" />
    <hkern u1="&#xec;" u2="&#xef;" k="-6" />
    <hkern u1="&#xec;" u2="&#xec;" k="-13" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-27" />
    <hkern u1="&#xed;" u2="&#x201d;" k="-6" />
    <hkern u1="&#xed;" u2="&#x2019;" k="-6" />
    <hkern u1="&#xed;" u2="&#x165;" k="-10" />
    <hkern u1="&#xed;" u2="&#x159;" k="-42" />
    <hkern u1="&#xed;" u2="&#x142;" k="-13" />
    <hkern u1="&#xed;" u2="&#x13e;" k="-13" />
    <hkern u1="&#xed;" u2="&#x13c;" k="-13" />
    <hkern u1="&#xed;" u2="&#x13a;" k="-13" />
    <hkern u1="&#xed;" u2="&#x137;" k="-18" />
    <hkern u1="&#xed;" u2="&#x135;" k="-18" />
    <hkern u1="&#xed;" u2="&#x131;" k="-18" />
    <hkern u1="&#xed;" u2="&#x12f;" k="-18" />
    <hkern u1="&#xed;" u2="&#x12d;" k="-18" />
    <hkern u1="&#xed;" u2="&#x12b;" k="-18" />
    <hkern u1="&#xed;" u2="&#x129;" k="-18" />
    <hkern u1="&#xed;" u2="&#x127;" k="-18" />
    <hkern u1="&#xed;" u2="&#x125;" k="-18" />
    <hkern u1="&#xed;" u2="&#xfe;" k="-18" />
    <hkern u1="&#xed;" u2="&#xef;" k="-6" />
    <hkern u1="&#xed;" u2="&#xee;" k="-18" />
    <hkern u1="&#xed;" u2="&#xed;" k="-18" />
    <hkern u1="&#xed;" u2="&#xec;" k="-13" />
    <hkern u1="&#xed;" u2="&#xdf;" k="-18" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-46" />
    <hkern u1="&#xed;" u2="&#x7c;" k="-16" />
    <hkern u1="&#xed;" u2="l" k="-13" />
    <hkern u1="&#xed;" u2="k" k="-18" />
    <hkern u1="&#xed;" u2="j" k="-18" />
    <hkern u1="&#xed;" u2="i" k="-18" />
    <hkern u1="&#xed;" u2="h" k="-18" />
    <hkern u1="&#xed;" u2="b" k="-18" />
    <hkern u1="&#xed;" u2="]" k="-48" />
    <hkern u1="&#xed;" u2="\" k="-47" />
    <hkern u1="&#xed;" u2="&#x3f;" k="-50" />
    <hkern u1="&#xed;" u2="&#x2a;" k="-36" />
    <hkern u1="&#xed;" u2="&#x29;" k="-26" />
    <hkern u1="&#xed;" u2="&#x27;" k="-25" />
    <hkern u1="&#xed;" u2="&#x22;" k="-25" />
    <hkern u1="&#xed;" u2="&#x21;" k="-15" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-5" />
    <hkern u1="&#xee;" u2="&#xef;" k="-6" />
    <hkern u1="&#xee;" u2="&#xec;" k="-13" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-19" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-27" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-7" />
    <hkern u1="&#xef;" u2="&#xef;" k="-6" />
    <hkern u1="&#xef;" u2="&#xec;" k="-13" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-20" />
    <hkern u1="&#xef;" u2="]" k="-21" />
    <hkern u1="&#xef;" u2="\" k="-26" />
    <hkern u1="&#xef;" u2="&#x3f;" k="-25" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-33" />
    <hkern u1="&#xef;" u2="&#x29;" k="-21" />
    <hkern u1="&#xef;" u2="&#x27;" k="-4" />
    <hkern u1="&#xef;" u2="&#x22;" k="-4" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="3" />
    <hkern u1="&#xf0;" u2="&#x2026;" k="4" />
    <hkern u1="&#xf0;" u2="&#x201e;" k="4" />
    <hkern u1="&#xf0;" u2="&#x201a;" k="4" />
    <hkern u1="&#xf0;" u2="&#x1ef9;" k="6" />
    <hkern u1="&#xf0;" u2="&#x1ef8;" k="37" />
    <hkern u1="&#xf0;" u2="&#x1ef3;" k="6" />
    <hkern u1="&#xf0;" u2="&#x1ef2;" k="37" />
    <hkern u1="&#xf0;" u2="&#x1eb0;" k="8" />
    <hkern u1="&#xf0;" u2="&#x1e84;" k="10" />
    <hkern u1="&#xf0;" u2="&#x1e82;" k="10" />
    <hkern u1="&#xf0;" u2="&#x1e80;" k="10" />
    <hkern u1="&#xf0;" u2="&#x21a;" k="34" />
    <hkern u1="&#xf0;" u2="&#x1fc;" k="8" />
    <hkern u1="&#xf0;" u2="&#x1fa;" k="8" />
    <hkern u1="&#xf0;" u2="&#x17d;" k="12" />
    <hkern u1="&#xf0;" u2="&#x17b;" k="12" />
    <hkern u1="&#xf0;" u2="&#x179;" k="12" />
    <hkern u1="&#xf0;" u2="&#x178;" k="37" />
    <hkern u1="&#xf0;" u2="&#x177;" k="6" />
    <hkern u1="&#xf0;" u2="&#x176;" k="37" />
    <hkern u1="&#xf0;" u2="&#x174;" k="10" />
    <hkern u1="&#xf0;" u2="&#x166;" k="34" />
    <hkern u1="&#xf0;" u2="&#x164;" k="34" />
    <hkern u1="&#xf0;" u2="&#x158;" k="5" />
    <hkern u1="&#xf0;" u2="&#x156;" k="5" />
    <hkern u1="&#xf0;" u2="&#x154;" k="5" />
    <hkern u1="&#xf0;" u2="&#x14a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x147;" k="5" />
    <hkern u1="&#xf0;" u2="&#x145;" k="5" />
    <hkern u1="&#xf0;" u2="&#x143;" k="5" />
    <hkern u1="&#xf0;" u2="&#x141;" k="5" />
    <hkern u1="&#xf0;" u2="&#x13d;" k="5" />
    <hkern u1="&#xf0;" u2="&#x13b;" k="5" />
    <hkern u1="&#xf0;" u2="&#x139;" k="5" />
    <hkern u1="&#xf0;" u2="&#x136;" k="5" />
    <hkern u1="&#xf0;" u2="&#x134;" k="19" />
    <hkern u1="&#xf0;" u2="&#x130;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12e;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12c;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x128;" k="5" />
    <hkern u1="&#xf0;" u2="&#x126;" k="5" />
    <hkern u1="&#xf0;" u2="&#x124;" k="5" />
    <hkern u1="&#xf0;" u2="&#x11a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x118;" k="5" />
    <hkern u1="&#xf0;" u2="&#x116;" k="5" />
    <hkern u1="&#xf0;" u2="&#x114;" k="5" />
    <hkern u1="&#xf0;" u2="&#x112;" k="5" />
    <hkern u1="&#xf0;" u2="&#x110;" k="5" />
    <hkern u1="&#xf0;" u2="&#x10e;" k="5" />
    <hkern u1="&#xf0;" u2="&#x104;" k="8" />
    <hkern u1="&#xf0;" u2="&#x102;" k="8" />
    <hkern u1="&#xf0;" u2="&#x100;" k="8" />
    <hkern u1="&#xf0;" u2="&#xff;" k="6" />
    <hkern u1="&#xf0;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf0;" u2="&#xde;" k="5" />
    <hkern u1="&#xf0;" u2="&#xdd;" k="37" />
    <hkern u1="&#xf0;" u2="&#xd1;" k="5" />
    <hkern u1="&#xf0;" u2="&#xd0;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcf;" k="5" />
    <hkern u1="&#xf0;" u2="&#xce;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcd;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcc;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcb;" k="5" />
    <hkern u1="&#xf0;" u2="&#xca;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc9;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc8;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf0;" u2="&#xc5;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc4;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc3;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc2;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc1;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc0;" k="8" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="17" />
    <hkern u1="&#xf0;" u2="y" k="6" />
    <hkern u1="&#xf0;" u2="x" k="5" />
    <hkern u1="&#xf0;" u2="v" k="5" />
    <hkern u1="&#xf0;" u2="]" k="21" />
    <hkern u1="&#xf0;" u2="\" k="18" />
    <hkern u1="&#xf0;" u2="Z" k="12" />
    <hkern u1="&#xf0;" u2="Y" k="37" />
    <hkern u1="&#xf0;" u2="X" k="28" />
    <hkern u1="&#xf0;" u2="W" k="10" />
    <hkern u1="&#xf0;" u2="V" k="17" />
    <hkern u1="&#xf0;" u2="T" k="34" />
    <hkern u1="&#xf0;" u2="R" k="5" />
    <hkern u1="&#xf0;" u2="P" k="5" />
    <hkern u1="&#xf0;" u2="N" k="5" />
    <hkern u1="&#xf0;" u2="M" k="5" />
    <hkern u1="&#xf0;" u2="L" k="5" />
    <hkern u1="&#xf0;" u2="K" k="5" />
    <hkern u1="&#xf0;" u2="J" k="19" />
    <hkern u1="&#xf0;" u2="I" k="5" />
    <hkern u1="&#xf0;" u2="H" k="5" />
    <hkern u1="&#xf0;" u2="F" k="5" />
    <hkern u1="&#xf0;" u2="E" k="5" />
    <hkern u1="&#xf0;" u2="D" k="5" />
    <hkern u1="&#xf0;" u2="B" k="5" />
    <hkern u1="&#xf0;" u2="A" k="8" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="5" />
    <hkern u1="&#xf0;" u2="&#x2f;" k="9" />
    <hkern u1="&#xf0;" u2="&#x2e;" k="4" />
    <hkern u1="&#xf0;" u2="&#x2c;" k="4" />
    <hkern u1="&#xf0;" u2="&#x29;" k="15" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf1;" u2="&#x7d;" k="10" />
    <hkern u1="&#xf1;" u2="v" k="5" />
    <hkern u1="&#xf1;" u2="]" k="11" />
    <hkern u1="&#xf1;" u2="\" k="38" />
    <hkern u1="&#xf1;" u2="V" k="28" />
    <hkern u1="&#xf1;" u2="&#x3f;" k="17" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf1;" u2="&#x29;" k="8" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf2;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf2;" u2="x" k="11" />
    <hkern u1="&#xf2;" u2="v" k="8" />
    <hkern u1="&#xf2;" u2="]" k="32" />
    <hkern u1="&#xf2;" u2="\" k="39" />
    <hkern u1="&#xf2;" u2="X" k="24" />
    <hkern u1="&#xf2;" u2="V" k="30" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf2;" u2="&#x29;" k="19" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf3;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf3;" u2="x" k="11" />
    <hkern u1="&#xf3;" u2="v" k="8" />
    <hkern u1="&#xf3;" u2="]" k="32" />
    <hkern u1="&#xf3;" u2="\" k="39" />
    <hkern u1="&#xf3;" u2="X" k="24" />
    <hkern u1="&#xf3;" u2="V" k="30" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf3;" u2="&#x29;" k="19" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf4;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf4;" u2="x" k="11" />
    <hkern u1="&#xf4;" u2="v" k="8" />
    <hkern u1="&#xf4;" u2="]" k="32" />
    <hkern u1="&#xf4;" u2="\" k="39" />
    <hkern u1="&#xf4;" u2="X" k="24" />
    <hkern u1="&#xf4;" u2="V" k="30" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf4;" u2="&#x29;" k="19" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf5;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf5;" u2="x" k="11" />
    <hkern u1="&#xf5;" u2="v" k="8" />
    <hkern u1="&#xf5;" u2="]" k="32" />
    <hkern u1="&#xf5;" u2="\" k="39" />
    <hkern u1="&#xf5;" u2="X" k="24" />
    <hkern u1="&#xf5;" u2="V" k="30" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf5;" u2="&#x29;" k="19" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf6;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf6;" u2="x" k="11" />
    <hkern u1="&#xf6;" u2="v" k="8" />
    <hkern u1="&#xf6;" u2="]" k="32" />
    <hkern u1="&#xf6;" u2="\" k="39" />
    <hkern u1="&#xf6;" u2="X" k="24" />
    <hkern u1="&#xf6;" u2="V" k="30" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf6;" u2="&#x29;" k="19" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="15" />
    <hkern u1="&#xf8;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf8;" u2="x" k="11" />
    <hkern u1="&#xf8;" u2="v" k="8" />
    <hkern u1="&#xf8;" u2="]" k="32" />
    <hkern u1="&#xf8;" u2="\" k="39" />
    <hkern u1="&#xf8;" u2="X" k="24" />
    <hkern u1="&#xf8;" u2="V" k="30" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="19" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf8;" u2="&#x29;" k="19" />
    <hkern u1="&#xf9;" u2="&#x2122;" k="10" />
    <hkern u1="&#xf9;" u2="&#x7d;" k="10" />
    <hkern u1="&#xf9;" u2="]" k="11" />
    <hkern u1="&#xf9;" u2="\" k="26" />
    <hkern u1="&#xf9;" u2="X" k="4" />
    <hkern u1="&#xf9;" u2="V" k="23" />
    <hkern u1="&#xf9;" u2="&#x3f;" k="5" />
    <hkern u1="&#xf9;" u2="&#x29;" k="8" />
    <hkern u1="&#xfa;" u2="&#x2122;" k="10" />
    <hkern u1="&#xfa;" u2="&#x7d;" k="10" />
    <hkern u1="&#xfa;" u2="]" k="11" />
    <hkern u1="&#xfa;" u2="\" k="26" />
    <hkern u1="&#xfa;" u2="X" k="4" />
    <hkern u1="&#xfa;" u2="V" k="23" />
    <hkern u1="&#xfa;" u2="&#x3f;" k="5" />
    <hkern u1="&#xfa;" u2="&#x29;" k="8" />
    <hkern u1="&#xfb;" u2="&#x2122;" k="10" />
    <hkern u1="&#xfb;" u2="&#x7d;" k="10" />
    <hkern u1="&#xfb;" u2="]" k="11" />
    <hkern u1="&#xfb;" u2="\" k="26" />
    <hkern u1="&#xfb;" u2="X" k="4" />
    <hkern u1="&#xfb;" u2="V" k="23" />
    <hkern u1="&#xfb;" u2="&#x3f;" k="5" />
    <hkern u1="&#xfb;" u2="&#x29;" k="8" />
    <hkern u1="&#xfc;" u2="&#x2122;" k="10" />
    <hkern u1="&#xfc;" u2="&#x7d;" k="10" />
    <hkern u1="&#xfc;" u2="]" k="11" />
    <hkern u1="&#xfc;" u2="\" k="26" />
    <hkern u1="&#xfc;" u2="X" k="4" />
    <hkern u1="&#xfc;" u2="V" k="23" />
    <hkern u1="&#xfc;" u2="&#x3f;" k="5" />
    <hkern u1="&#xfc;" u2="&#x29;" k="8" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="13" />
    <hkern u1="&#xfd;" u2="&#xc6;" k="19" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="17" />
    <hkern u1="&#xfd;" u2="]" k="23" />
    <hkern u1="&#xfd;" u2="\" k="13" />
    <hkern u1="&#xfd;" u2="X" k="24" />
    <hkern u1="&#xfd;" u2="V" k="7" />
    <hkern u1="&#xfd;" u2="&#x3f;" k="4" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="20" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="16" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="7" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="25" />
    <hkern u1="&#xfe;" u2="x" k="10" />
    <hkern u1="&#xfe;" u2="v" k="7" />
    <hkern u1="&#xfe;" u2="]" k="31" />
    <hkern u1="&#xfe;" u2="\" k="38" />
    <hkern u1="&#xfe;" u2="X" k="21" />
    <hkern u1="&#xfe;" u2="V" k="29" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="20" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="8" />
    <hkern u1="&#xfe;" u2="&#x29;" k="18" />
    <hkern u1="&#xff;" u2="&#xf0;" k="13" />
    <hkern u1="&#xff;" u2="&#xc6;" k="19" />
    <hkern u1="&#xff;" u2="&#x7d;" k="17" />
    <hkern u1="&#xff;" u2="]" k="23" />
    <hkern u1="&#xff;" u2="\" k="13" />
    <hkern u1="&#xff;" u2="X" k="24" />
    <hkern u1="&#xff;" u2="V" k="7" />
    <hkern u1="&#xff;" u2="&#x3f;" k="4" />
    <hkern u1="&#xff;" u2="&#x2f;" k="20" />
    <hkern u1="&#x100;" g2="braceright.cap" k="6" />
    <hkern u1="&#x100;" g2="bracketright.cap" k="8" />
    <hkern u1="&#x100;" u2="&#x2122;" k="35" />
    <hkern u1="&#x100;" u2="&#xf0;" k="5" />
    <hkern u1="&#x100;" u2="&#xae;" k="21" />
    <hkern u1="&#x100;" u2="&#x7d;" k="9" />
    <hkern u1="&#x100;" u2="v" k="16" />
    <hkern u1="&#x100;" u2="f" k="8" />
    <hkern u1="&#x100;" u2="]" k="10" />
    <hkern u1="&#x100;" u2="\" k="44" />
    <hkern u1="&#x100;" u2="V" k="28" />
    <hkern u1="&#x100;" u2="&#x3f;" k="19" />
    <hkern u1="&#x100;" u2="&#x2a;" k="31" />
    <hkern u1="&#x101;" u2="&#x2122;" k="14" />
    <hkern u1="&#x101;" u2="&#x7d;" k="4" />
    <hkern u1="&#x101;" u2="v" k="5" />
    <hkern u1="&#x101;" u2="]" k="5" />
    <hkern u1="&#x101;" u2="\" k="40" />
    <hkern u1="&#x101;" u2="V" k="27" />
    <hkern u1="&#x101;" u2="&#x3f;" k="14" />
    <hkern u1="&#x101;" u2="&#x2a;" k="6" />
    <hkern u1="&#x102;" g2="braceright.cap" k="6" />
    <hkern u1="&#x102;" g2="bracketright.cap" k="8" />
    <hkern u1="&#x102;" u2="&#x2122;" k="35" />
    <hkern u1="&#x102;" u2="&#xf0;" k="5" />
    <hkern u1="&#x102;" u2="&#xae;" k="21" />
    <hkern u1="&#x102;" u2="&#x7d;" k="9" />
    <hkern u1="&#x102;" u2="v" k="16" />
    <hkern u1="&#x102;" u2="f" k="8" />
    <hkern u1="&#x102;" u2="]" k="10" />
    <hkern u1="&#x102;" u2="\" k="44" />
    <hkern u1="&#x102;" u2="V" k="28" />
    <hkern u1="&#x102;" u2="&#x3f;" k="19" />
    <hkern u1="&#x102;" u2="&#x2a;" k="31" />
    <hkern u1="&#x103;" u2="&#x2122;" k="14" />
    <hkern u1="&#x103;" u2="&#x7d;" k="4" />
    <hkern u1="&#x103;" u2="v" k="5" />
    <hkern u1="&#x103;" u2="]" k="5" />
    <hkern u1="&#x103;" u2="\" k="40" />
    <hkern u1="&#x103;" u2="V" k="27" />
    <hkern u1="&#x103;" u2="&#x3f;" k="14" />
    <hkern u1="&#x103;" u2="&#x2a;" k="6" />
    <hkern u1="&#x104;" g2="braceright.cap" k="6" />
    <hkern u1="&#x104;" g2="bracketright.cap" k="8" />
    <hkern u1="&#x104;" u2="&#x2122;" k="35" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-11" />
    <hkern u1="&#x104;" u2="&#xf0;" k="5" />
    <hkern u1="&#x104;" u2="&#xae;" k="21" />
    <hkern u1="&#x104;" u2="&#x7d;" k="9" />
    <hkern u1="&#x104;" u2="v" k="16" />
    <hkern u1="&#x104;" u2="j" k="-53" />
    <hkern u1="&#x104;" u2="f" k="8" />
    <hkern u1="&#x104;" u2="]" k="10" />
    <hkern u1="&#x104;" u2="\" k="44" />
    <hkern u1="&#x104;" u2="V" k="28" />
    <hkern u1="&#x104;" u2="&#x3f;" k="19" />
    <hkern u1="&#x104;" u2="&#x2a;" k="31" />
    <hkern u1="&#x105;" u2="&#x2122;" k="14" />
    <hkern u1="&#x105;" u2="&#x7d;" k="4" />
    <hkern u1="&#x105;" u2="v" k="5" />
    <hkern u1="&#x105;" u2="j" k="-31" />
    <hkern u1="&#x105;" u2="]" k="5" />
    <hkern u1="&#x105;" u2="\" k="40" />
    <hkern u1="&#x105;" u2="V" k="27" />
    <hkern u1="&#x105;" u2="&#x3f;" k="14" />
    <hkern u1="&#x105;" u2="&#x2a;" k="6" />
    <hkern u1="&#x106;" u2="&#x135;" k="-16" />
    <hkern u1="&#x106;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x106;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x106;" u2="&#x129;" k="-26" />
    <hkern u1="&#x106;" u2="&#xf0;" k="7" />
    <hkern u1="&#x106;" u2="&#xef;" k="-26" />
    <hkern u1="&#x106;" u2="&#xee;" k="-21" />
    <hkern u1="&#x106;" u2="&#xec;" k="-39" />
    <hkern u1="&#x106;" u2="&#xae;" k="5" />
    <hkern u1="&#x106;" u2="v" k="10" />
    <hkern u1="&#x106;" u2="f" k="5" />
    <hkern u1="&#x107;" u2="&#xf0;" k="9" />
    <hkern u1="&#x107;" u2="&#x7d;" k="7" />
    <hkern u1="&#x107;" u2="]" k="8" />
    <hkern u1="&#x107;" u2="\" k="18" />
    <hkern u1="&#x107;" u2="V" k="12" />
    <hkern u1="&#x107;" u2="&#x3f;" k="4" />
    <hkern u1="&#x108;" u2="&#x135;" k="-16" />
    <hkern u1="&#x108;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x108;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x108;" u2="&#x129;" k="-26" />
    <hkern u1="&#x108;" u2="&#xf0;" k="7" />
    <hkern u1="&#x108;" u2="&#xef;" k="-26" />
    <hkern u1="&#x108;" u2="&#xee;" k="-21" />
    <hkern u1="&#x108;" u2="&#xec;" k="-39" />
    <hkern u1="&#x108;" u2="&#xae;" k="5" />
    <hkern u1="&#x108;" u2="v" k="10" />
    <hkern u1="&#x108;" u2="f" k="5" />
    <hkern u1="&#x109;" u2="&#xf0;" k="9" />
    <hkern u1="&#x109;" u2="&#x7d;" k="7" />
    <hkern u1="&#x109;" u2="]" k="8" />
    <hkern u1="&#x109;" u2="\" k="18" />
    <hkern u1="&#x109;" u2="V" k="12" />
    <hkern u1="&#x109;" u2="&#x3f;" k="4" />
    <hkern u1="&#x10a;" u2="&#x135;" k="-16" />
    <hkern u1="&#x10a;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x10a;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x10a;" u2="&#x129;" k="-26" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="7" />
    <hkern u1="&#x10a;" u2="&#xef;" k="-26" />
    <hkern u1="&#x10a;" u2="&#xee;" k="-21" />
    <hkern u1="&#x10a;" u2="&#xec;" k="-39" />
    <hkern u1="&#x10a;" u2="&#xae;" k="5" />
    <hkern u1="&#x10a;" u2="v" k="10" />
    <hkern u1="&#x10a;" u2="f" k="5" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="9" />
    <hkern u1="&#x10b;" u2="&#x7d;" k="7" />
    <hkern u1="&#x10b;" u2="]" k="8" />
    <hkern u1="&#x10b;" u2="\" k="18" />
    <hkern u1="&#x10b;" u2="V" k="12" />
    <hkern u1="&#x10b;" u2="&#x3f;" k="4" />
    <hkern u1="&#x10c;" u2="&#x135;" k="-16" />
    <hkern u1="&#x10c;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x10c;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x10c;" u2="&#x129;" k="-26" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="7" />
    <hkern u1="&#x10c;" u2="&#xef;" k="-26" />
    <hkern u1="&#x10c;" u2="&#xee;" k="-21" />
    <hkern u1="&#x10c;" u2="&#xec;" k="-39" />
    <hkern u1="&#x10c;" u2="&#xae;" k="5" />
    <hkern u1="&#x10c;" u2="v" k="10" />
    <hkern u1="&#x10c;" u2="f" k="5" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="9" />
    <hkern u1="&#x10d;" u2="&#x7d;" k="7" />
    <hkern u1="&#x10d;" u2="]" k="8" />
    <hkern u1="&#x10d;" u2="\" k="18" />
    <hkern u1="&#x10d;" u2="V" k="12" />
    <hkern u1="&#x10d;" u2="&#x3f;" k="4" />
    <hkern u1="&#x10e;" g2="braceright.cap" k="19" />
    <hkern u1="&#x10e;" g2="bracketright.cap" k="25" />
    <hkern u1="&#x10e;" g2="parenright.cap" k="13" />
    <hkern u1="&#x10e;" u2="&#xc6;" k="15" />
    <hkern u1="&#x10e;" u2="&#x7d;" k="18" />
    <hkern u1="&#x10e;" u2="]" k="23" />
    <hkern u1="&#x10e;" u2="\" k="13" />
    <hkern u1="&#x10e;" u2="X" k="19" />
    <hkern u1="&#x10e;" u2="V" k="11" />
    <hkern u1="&#x10e;" u2="&#x3f;" k="4" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="11" />
    <hkern u1="&#x10e;" u2="&#x29;" k="12" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-18" />
    <hkern u1="&#x10f;" u2="&#x161;" k="-13" />
    <hkern u1="&#x10f;" u2="&#x10d;" k="-12" />
    <hkern u1="&#x10f;" u2="&#xf0;" k="-16" />
    <hkern u1="&#x10f;" u2="&#xe1;" k="-7" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-22" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-35" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-10" />
    <hkern u1="&#x10f;" u2="x" k="-20" />
    <hkern u1="&#x10f;" u2="v" k="-18" />
    <hkern u1="&#x10f;" u2="f" k="-6" />
    <hkern u1="&#x10f;" u2="]" k="-36" />
    <hkern u1="&#x10f;" u2="\" k="-49" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-46" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="26" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-44" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-27" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-10" />
    <hkern u1="&#x110;" g2="braceright.cap" k="19" />
    <hkern u1="&#x110;" g2="bracketright.cap" k="25" />
    <hkern u1="&#x110;" g2="parenright.cap" k="13" />
    <hkern u1="&#x110;" u2="&#xc6;" k="15" />
    <hkern u1="&#x110;" u2="&#x7d;" k="18" />
    <hkern u1="&#x110;" u2="]" k="23" />
    <hkern u1="&#x110;" u2="\" k="13" />
    <hkern u1="&#x110;" u2="X" k="19" />
    <hkern u1="&#x110;" u2="V" k="11" />
    <hkern u1="&#x110;" u2="&#x3f;" k="4" />
    <hkern u1="&#x110;" u2="&#x2f;" k="11" />
    <hkern u1="&#x110;" u2="&#x29;" k="12" />
    <hkern u1="&#x111;" u2="&#xef;" k="-6" />
    <hkern u1="&#x111;" u2="&#xec;" k="-13" />
    <hkern u1="&#x112;" u2="&#x135;" k="-16" />
    <hkern u1="&#x112;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x112;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x112;" u2="&#x129;" k="-23" />
    <hkern u1="&#x112;" u2="&#xf0;" k="7" />
    <hkern u1="&#x112;" u2="&#xef;" k="-24" />
    <hkern u1="&#x112;" u2="&#xee;" k="-21" />
    <hkern u1="&#x112;" u2="&#xec;" k="-39" />
    <hkern u1="&#x112;" u2="v" k="5" />
    <hkern u1="&#x113;" u2="&#x2122;" k="13" />
    <hkern u1="&#x113;" u2="&#xc6;" k="5" />
    <hkern u1="&#x113;" u2="&#x7d;" k="18" />
    <hkern u1="&#x113;" u2="v" k="7" />
    <hkern u1="&#x113;" u2="]" k="11" />
    <hkern u1="&#x113;" u2="\" k="36" />
    <hkern u1="&#x113;" u2="X" k="4" />
    <hkern u1="&#x113;" u2="V" k="28" />
    <hkern u1="&#x113;" u2="&#x3f;" k="15" />
    <hkern u1="&#x113;" u2="&#x29;" k="9" />
    <hkern u1="&#x114;" u2="&#x135;" k="-16" />
    <hkern u1="&#x114;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x114;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x114;" u2="&#x129;" k="-23" />
    <hkern u1="&#x114;" u2="&#xf0;" k="7" />
    <hkern u1="&#x114;" u2="&#xef;" k="-24" />
    <hkern u1="&#x114;" u2="&#xee;" k="-21" />
    <hkern u1="&#x114;" u2="&#xec;" k="-39" />
    <hkern u1="&#x114;" u2="v" k="5" />
    <hkern u1="&#x115;" u2="&#x2122;" k="13" />
    <hkern u1="&#x115;" u2="&#xc6;" k="5" />
    <hkern u1="&#x115;" u2="&#x7d;" k="18" />
    <hkern u1="&#x115;" u2="v" k="7" />
    <hkern u1="&#x115;" u2="]" k="11" />
    <hkern u1="&#x115;" u2="\" k="36" />
    <hkern u1="&#x115;" u2="X" k="4" />
    <hkern u1="&#x115;" u2="V" k="28" />
    <hkern u1="&#x115;" u2="&#x3f;" k="15" />
    <hkern u1="&#x115;" u2="&#x29;" k="9" />
    <hkern u1="&#x116;" u2="&#x135;" k="-16" />
    <hkern u1="&#x116;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x116;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x116;" u2="&#x129;" k="-23" />
    <hkern u1="&#x116;" u2="&#xf0;" k="7" />
    <hkern u1="&#x116;" u2="&#xef;" k="-24" />
    <hkern u1="&#x116;" u2="&#xee;" k="-21" />
    <hkern u1="&#x116;" u2="&#xec;" k="-39" />
    <hkern u1="&#x116;" u2="v" k="5" />
    <hkern u1="&#x117;" u2="&#x2122;" k="13" />
    <hkern u1="&#x117;" u2="&#xc6;" k="5" />
    <hkern u1="&#x117;" u2="&#x7d;" k="18" />
    <hkern u1="&#x117;" u2="v" k="7" />
    <hkern u1="&#x117;" u2="]" k="11" />
    <hkern u1="&#x117;" u2="\" k="36" />
    <hkern u1="&#x117;" u2="X" k="4" />
    <hkern u1="&#x117;" u2="V" k="28" />
    <hkern u1="&#x117;" u2="&#x3f;" k="15" />
    <hkern u1="&#x117;" u2="&#x29;" k="9" />
    <hkern u1="&#x118;" u2="&#x135;" k="-16" />
    <hkern u1="&#x118;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x118;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x118;" u2="&#x129;" k="-23" />
    <hkern u1="&#x118;" u2="&#xf0;" k="7" />
    <hkern u1="&#x118;" u2="&#xef;" k="-24" />
    <hkern u1="&#x118;" u2="&#xee;" k="-21" />
    <hkern u1="&#x118;" u2="&#xec;" k="-39" />
    <hkern u1="&#x118;" u2="v" k="5" />
    <hkern u1="&#x118;" u2="j" k="-16" />
    <hkern u1="&#x119;" u2="&#x2122;" k="13" />
    <hkern u1="&#x119;" u2="&#xc6;" k="5" />
    <hkern u1="&#x119;" u2="&#x7d;" k="18" />
    <hkern u1="&#x119;" u2="v" k="7" />
    <hkern u1="&#x119;" u2="]" k="11" />
    <hkern u1="&#x119;" u2="\" k="36" />
    <hkern u1="&#x119;" u2="X" k="4" />
    <hkern u1="&#x119;" u2="V" k="28" />
    <hkern u1="&#x119;" u2="&#x3f;" k="15" />
    <hkern u1="&#x119;" u2="&#x29;" k="9" />
    <hkern u1="&#x11a;" u2="&#x135;" k="-16" />
    <hkern u1="&#x11a;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x11a;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x11a;" u2="&#x129;" k="-23" />
    <hkern u1="&#x11a;" u2="&#xf0;" k="7" />
    <hkern u1="&#x11a;" u2="&#xef;" k="-24" />
    <hkern u1="&#x11a;" u2="&#xee;" k="-21" />
    <hkern u1="&#x11a;" u2="&#xec;" k="-39" />
    <hkern u1="&#x11a;" u2="v" k="5" />
    <hkern u1="&#x11b;" u2="&#x2122;" k="13" />
    <hkern u1="&#x11b;" u2="&#xc6;" k="5" />
    <hkern u1="&#x11b;" u2="&#x7d;" k="18" />
    <hkern u1="&#x11b;" u2="v" k="7" />
    <hkern u1="&#x11b;" u2="]" k="11" />
    <hkern u1="&#x11b;" u2="\" k="36" />
    <hkern u1="&#x11b;" u2="X" k="4" />
    <hkern u1="&#x11b;" u2="V" k="28" />
    <hkern u1="&#x11b;" u2="&#x3f;" k="15" />
    <hkern u1="&#x11b;" u2="&#x29;" k="9" />
    <hkern u1="&#x11c;" g2="braceright.cap" k="4" />
    <hkern u1="&#x11c;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x11c;" u2="&#xef;" k="-11" />
    <hkern u1="&#x11c;" u2="&#xee;" k="-7" />
    <hkern u1="&#x11c;" u2="&#xec;" k="-21" />
    <hkern u1="&#x11c;" u2="v" k="6" />
    <hkern u1="&#x11c;" u2="f" k="6" />
    <hkern u1="&#x11c;" u2="\" k="7" />
    <hkern u1="&#x11c;" u2="V" k="9" />
    <hkern u1="&#x11d;" u2="&#x135;" k="-22" />
    <hkern u1="&#x11d;" u2="&#xf0;" k="6" />
    <hkern u1="&#x11d;" u2="j" k="-22" />
    <hkern u1="&#x11d;" u2="\" k="11" />
    <hkern u1="&#x11d;" u2="V" k="4" />
    <hkern u1="&#x11e;" g2="braceright.cap" k="4" />
    <hkern u1="&#x11e;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x11e;" u2="&#xef;" k="-11" />
    <hkern u1="&#x11e;" u2="&#xee;" k="-7" />
    <hkern u1="&#x11e;" u2="&#xec;" k="-21" />
    <hkern u1="&#x11e;" u2="v" k="6" />
    <hkern u1="&#x11e;" u2="f" k="6" />
    <hkern u1="&#x11e;" u2="\" k="7" />
    <hkern u1="&#x11e;" u2="V" k="9" />
    <hkern u1="&#x11f;" u2="&#x135;" k="-22" />
    <hkern u1="&#x11f;" u2="&#xf0;" k="6" />
    <hkern u1="&#x11f;" u2="j" k="-22" />
    <hkern u1="&#x11f;" u2="\" k="11" />
    <hkern u1="&#x11f;" u2="V" k="4" />
    <hkern u1="&#x120;" g2="braceright.cap" k="4" />
    <hkern u1="&#x120;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x120;" u2="&#xef;" k="-11" />
    <hkern u1="&#x120;" u2="&#xee;" k="-7" />
    <hkern u1="&#x120;" u2="&#xec;" k="-21" />
    <hkern u1="&#x120;" u2="v" k="6" />
    <hkern u1="&#x120;" u2="f" k="6" />
    <hkern u1="&#x120;" u2="\" k="7" />
    <hkern u1="&#x120;" u2="V" k="9" />
    <hkern u1="&#x121;" u2="&#x135;" k="-22" />
    <hkern u1="&#x121;" u2="&#xf0;" k="6" />
    <hkern u1="&#x121;" u2="j" k="-22" />
    <hkern u1="&#x121;" u2="\" k="11" />
    <hkern u1="&#x121;" u2="V" k="4" />
    <hkern u1="&#x122;" g2="braceright.cap" k="4" />
    <hkern u1="&#x122;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x122;" u2="&#xef;" k="-11" />
    <hkern u1="&#x122;" u2="&#xee;" k="-7" />
    <hkern u1="&#x122;" u2="&#xec;" k="-21" />
    <hkern u1="&#x122;" u2="v" k="6" />
    <hkern u1="&#x122;" u2="f" k="6" />
    <hkern u1="&#x122;" u2="\" k="7" />
    <hkern u1="&#x122;" u2="V" k="9" />
    <hkern u1="&#x123;" u2="&#x135;" k="-22" />
    <hkern u1="&#x123;" u2="&#xf0;" k="6" />
    <hkern u1="&#x123;" u2="j" k="-22" />
    <hkern u1="&#x123;" u2="\" k="11" />
    <hkern u1="&#x123;" u2="V" k="4" />
    <hkern u1="&#x124;" g2="braceright.cap" k="4" />
    <hkern u1="&#x124;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x124;" u2="&#xf0;" k="6" />
    <hkern u1="&#x124;" u2="&#xec;" k="-6" />
    <hkern u1="&#x124;" u2="f" k="4" />
    <hkern u1="&#x125;" u2="&#x2122;" k="15" />
    <hkern u1="&#x125;" u2="&#x7d;" k="10" />
    <hkern u1="&#x125;" u2="v" k="5" />
    <hkern u1="&#x125;" u2="]" k="11" />
    <hkern u1="&#x125;" u2="\" k="38" />
    <hkern u1="&#x125;" u2="V" k="28" />
    <hkern u1="&#x125;" u2="&#x3f;" k="17" />
    <hkern u1="&#x125;" u2="&#x2a;" k="6" />
    <hkern u1="&#x125;" u2="&#x29;" k="8" />
    <hkern u1="&#x126;" g2="braceright.cap" k="4" />
    <hkern u1="&#x126;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x126;" u2="&#xf0;" k="6" />
    <hkern u1="&#x126;" u2="&#xec;" k="-6" />
    <hkern u1="&#x126;" u2="f" k="4" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-16" />
    <hkern u1="&#x127;" u2="&#x2122;" k="15" />
    <hkern u1="&#x127;" u2="&#x7d;" k="10" />
    <hkern u1="&#x127;" u2="v" k="5" />
    <hkern u1="&#x127;" u2="]" k="11" />
    <hkern u1="&#x127;" u2="\" k="38" />
    <hkern u1="&#x127;" u2="V" k="28" />
    <hkern u1="&#x127;" u2="&#x3f;" k="17" />
    <hkern u1="&#x127;" u2="&#x2a;" k="6" />
    <hkern u1="&#x127;" u2="&#x29;" k="8" />
    <hkern u1="&#x128;" g2="braceright.cap" k="4" />
    <hkern u1="&#x128;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x128;" g2="parenright.cap" k="-7" />
    <hkern u1="&#x128;" u2="&#xf0;" k="6" />
    <hkern u1="&#x128;" u2="&#xec;" k="-6" />
    <hkern u1="&#x128;" u2="f" k="4" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-10" />
    <hkern u1="&#x129;" u2="&#xef;" k="-6" />
    <hkern u1="&#x129;" u2="&#xec;" k="-13" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-27" />
    <hkern u1="&#x129;" u2="]" k="-27" />
    <hkern u1="&#x129;" u2="\" k="-35" />
    <hkern u1="&#x129;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-26" />
    <hkern u1="&#x129;" u2="&#x29;" k="-10" />
    <hkern u1="&#x129;" u2="&#x27;" k="-8" />
    <hkern u1="&#x129;" u2="&#x22;" k="-8" />
    <hkern u1="&#x12a;" g2="braceright.cap" k="4" />
    <hkern u1="&#x12a;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x12a;" g2="parenright.cap" k="-27" />
    <hkern u1="&#x12a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x12a;" u2="&#xec;" k="-6" />
    <hkern u1="&#x12a;" u2="f" k="4" />
    <hkern u1="&#x12b;" u2="&#xef;" k="-6" />
    <hkern u1="&#x12b;" u2="&#xec;" k="-13" />
    <hkern u1="&#x12b;" u2="\" k="-13" />
    <hkern u1="&#x12b;" u2="&#x3f;" k="-14" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-16" />
    <hkern u1="&#x12c;" g2="braceright.cap" k="4" />
    <hkern u1="&#x12c;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x12c;" u2="&#xf0;" k="6" />
    <hkern u1="&#x12c;" u2="&#xec;" k="-6" />
    <hkern u1="&#x12c;" u2="f" k="4" />
    <hkern u1="&#x12d;" u2="&#xef;" k="-6" />
    <hkern u1="&#x12d;" u2="&#xec;" k="-13" />
    <hkern u1="&#x12d;" u2="&#x7d;" k="-25" />
    <hkern u1="&#x12d;" u2="]" k="-25" />
    <hkern u1="&#x12d;" u2="\" k="-14" />
    <hkern u1="&#x12d;" u2="&#x3f;" k="-14" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-10" />
    <hkern u1="&#x12d;" u2="&#x29;" k="-22" />
    <hkern u1="&#x12e;" g2="braceright.cap" k="4" />
    <hkern u1="&#x12e;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x12e;" u2="&#xf0;" k="6" />
    <hkern u1="&#x12e;" u2="&#xec;" k="-6" />
    <hkern u1="&#x12e;" u2="j" k="-5" />
    <hkern u1="&#x12e;" u2="f" k="4" />
    <hkern u1="&#x12f;" u2="&#xef;" k="-6" />
    <hkern u1="&#x12f;" u2="&#xec;" k="-13" />
    <hkern u1="&#x12f;" u2="j" k="-8" />
    <hkern u1="&#x130;" g2="braceright.cap" k="4" />
    <hkern u1="&#x130;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x130;" u2="&#xf0;" k="6" />
    <hkern u1="&#x130;" u2="&#xec;" k="-6" />
    <hkern u1="&#x130;" u2="f" k="4" />
    <hkern u1="&#x131;" u2="&#xef;" k="-6" />
    <hkern u1="&#x131;" u2="&#xec;" k="-13" />
    <hkern u1="&#x134;" g2="braceright.cap" k="-31" />
    <hkern u1="&#x134;" g2="bracketright.cap" k="-33" />
    <hkern u1="&#x134;" g2="parenright.cap" k="-34" />
    <hkern u1="&#x134;" u2="&#xf0;" k="5" />
    <hkern u1="&#x134;" u2="&#xec;" k="-8" />
    <hkern u1="&#x134;" u2="f" k="4" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-10" />
    <hkern u1="&#x135;" u2="&#xef;" k="-6" />
    <hkern u1="&#x135;" u2="&#xec;" k="-13" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-24" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-27" />
    <hkern u1="&#x135;" u2="&#x27;" k="-6" />
    <hkern u1="&#x135;" u2="&#x22;" k="-6" />
    <hkern u1="&#x136;" u2="&#x12d;" k="-27" />
    <hkern u1="&#x136;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x136;" u2="&#x129;" k="-33" />
    <hkern u1="&#x136;" u2="&#xf0;" k="11" />
    <hkern u1="&#x136;" u2="&#xef;" k="-41" />
    <hkern u1="&#x136;" u2="&#xee;" k="-4" />
    <hkern u1="&#x136;" u2="&#xec;" k="-48" />
    <hkern u1="&#x136;" u2="&#xae;" k="5" />
    <hkern u1="&#x136;" u2="v" k="20" />
    <hkern u1="&#x136;" u2="f" k="8" />
    <hkern u1="&#x137;" u2="&#xf0;" k="14" />
    <hkern u1="&#x137;" u2="&#x7d;" k="6" />
    <hkern u1="&#x137;" u2="]" k="7" />
    <hkern u1="&#x137;" u2="\" k="13" />
    <hkern u1="&#x137;" u2="V" k="8" />
    <hkern u1="&#x137;" u2="&#x3f;" k="3" />
    <hkern u1="&#x139;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x139;" g2="periodcentered.cap" k="73" />
    <hkern u1="&#x139;" u2="&#x2122;" k="82" />
    <hkern u1="&#x139;" u2="&#xb7;" k="24" />
    <hkern u1="&#x139;" u2="&#xae;" k="71" />
    <hkern u1="&#x139;" u2="&#x7d;" k="5" />
    <hkern u1="&#x139;" u2="v" k="38" />
    <hkern u1="&#x139;" u2="f" k="6" />
    <hkern u1="&#x139;" u2="]" k="6" />
    <hkern u1="&#x139;" u2="\" k="71" />
    <hkern u1="&#x139;" u2="V" k="57" />
    <hkern u1="&#x139;" u2="&#x3f;" k="6" />
    <hkern u1="&#x139;" u2="&#x2a;" k="81" />
    <hkern u1="&#x13a;" u2="&#xec;" k="-9" />
    <hkern u1="&#x13a;" u2="&#xb7;" k="60" />
    <hkern u1="&#x13b;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x13b;" g2="periodcentered.cap" k="73" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="82" />
    <hkern u1="&#x13b;" u2="&#xb7;" k="24" />
    <hkern u1="&#x13b;" u2="&#xae;" k="71" />
    <hkern u1="&#x13b;" u2="&#x7d;" k="5" />
    <hkern u1="&#x13b;" u2="v" k="38" />
    <hkern u1="&#x13b;" u2="f" k="6" />
    <hkern u1="&#x13b;" u2="]" k="6" />
    <hkern u1="&#x13b;" u2="\" k="71" />
    <hkern u1="&#x13b;" u2="V" k="57" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="6" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="81" />
    <hkern u1="&#x13c;" u2="&#xec;" k="-9" />
    <hkern u1="&#x13c;" u2="&#xb7;" k="60" />
    <hkern u1="&#x13d;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x13d;" g2="periodcentered.cap" k="73" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="69" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="62" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="59" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="62" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="59" />
    <hkern u1="&#x13d;" u2="&#x1ef8;" k="18" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="32" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="32" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="32" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="29" />
    <hkern u1="&#x13d;" u2="&#x178;" k="20" />
    <hkern u1="&#x13d;" u2="&#x176;" k="20" />
    <hkern u1="&#x13d;" u2="&#x174;" k="32" />
    <hkern u1="&#x13d;" u2="&#x166;" k="29" />
    <hkern u1="&#x13d;" u2="&#x164;" k="29" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="20" />
    <hkern u1="&#x13d;" u2="&#xb7;" k="24" />
    <hkern u1="&#x13d;" u2="&#xae;" k="65" />
    <hkern u1="&#x13d;" u2="&#x7d;" k="5" />
    <hkern u1="&#x13d;" u2="v" k="38" />
    <hkern u1="&#x13d;" u2="f" k="6" />
    <hkern u1="&#x13d;" u2="]" k="6" />
    <hkern u1="&#x13d;" u2="\" k="48" />
    <hkern u1="&#x13d;" u2="Y" k="20" />
    <hkern u1="&#x13d;" u2="W" k="32" />
    <hkern u1="&#x13d;" u2="V" k="32" />
    <hkern u1="&#x13d;" u2="T" k="29" />
    <hkern u1="&#x13d;" u2="&#x3f;" k="6" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="34" />
    <hkern u1="&#x13d;" u2="&#x27;" k="74" />
    <hkern u1="&#x13d;" u2="&#x22;" k="74" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-15" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-18" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-13" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-12" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-16" />
    <hkern u1="&#x13e;" u2="&#xe1;" k="-7" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-22" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-35" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-10" />
    <hkern u1="&#x13e;" u2="x" k="-20" />
    <hkern u1="&#x13e;" u2="v" k="-18" />
    <hkern u1="&#x13e;" u2="f" k="-6" />
    <hkern u1="&#x13e;" u2="]" k="-36" />
    <hkern u1="&#x13e;" u2="\" k="-49" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-46" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="26" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-44" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-27" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-10" />
    <hkern u1="&#x141;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x141;" g2="periodcentered.cap" k="73" />
    <hkern u1="&#x141;" u2="&#x2122;" k="82" />
    <hkern u1="&#x141;" u2="&#xb7;" k="24" />
    <hkern u1="&#x141;" u2="&#xae;" k="71" />
    <hkern u1="&#x141;" u2="&#x7d;" k="5" />
    <hkern u1="&#x141;" u2="v" k="38" />
    <hkern u1="&#x141;" u2="f" k="6" />
    <hkern u1="&#x141;" u2="]" k="6" />
    <hkern u1="&#x141;" u2="\" k="71" />
    <hkern u1="&#x141;" u2="V" k="57" />
    <hkern u1="&#x141;" u2="&#x3f;" k="6" />
    <hkern u1="&#x141;" u2="&#x2a;" k="81" />
    <hkern u1="&#x142;" u2="&#xec;" k="-9" />
    <hkern u1="&#x142;" u2="&#xb7;" k="60" />
    <hkern u1="&#x143;" g2="braceright.cap" k="4" />
    <hkern u1="&#x143;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x143;" u2="&#xf0;" k="6" />
    <hkern u1="&#x143;" u2="&#xec;" k="-6" />
    <hkern u1="&#x143;" u2="f" k="4" />
    <hkern u1="&#x144;" u2="&#x2122;" k="15" />
    <hkern u1="&#x144;" u2="&#x7d;" k="10" />
    <hkern u1="&#x144;" u2="v" k="5" />
    <hkern u1="&#x144;" u2="]" k="11" />
    <hkern u1="&#x144;" u2="\" k="38" />
    <hkern u1="&#x144;" u2="V" k="28" />
    <hkern u1="&#x144;" u2="&#x3f;" k="17" />
    <hkern u1="&#x144;" u2="&#x2a;" k="6" />
    <hkern u1="&#x144;" u2="&#x29;" k="8" />
    <hkern u1="&#x145;" g2="braceright.cap" k="4" />
    <hkern u1="&#x145;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x145;" u2="&#xf0;" k="6" />
    <hkern u1="&#x145;" u2="&#xec;" k="-6" />
    <hkern u1="&#x145;" u2="f" k="4" />
    <hkern u1="&#x146;" u2="&#x2122;" k="15" />
    <hkern u1="&#x146;" u2="&#x7d;" k="10" />
    <hkern u1="&#x146;" u2="v" k="5" />
    <hkern u1="&#x146;" u2="]" k="11" />
    <hkern u1="&#x146;" u2="\" k="38" />
    <hkern u1="&#x146;" u2="V" k="28" />
    <hkern u1="&#x146;" u2="&#x3f;" k="17" />
    <hkern u1="&#x146;" u2="&#x2a;" k="6" />
    <hkern u1="&#x146;" u2="&#x29;" k="8" />
    <hkern u1="&#x147;" g2="braceright.cap" k="4" />
    <hkern u1="&#x147;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x147;" u2="&#xf0;" k="6" />
    <hkern u1="&#x147;" u2="&#xec;" k="-6" />
    <hkern u1="&#x147;" u2="f" k="4" />
    <hkern u1="&#x148;" u2="&#x2122;" k="15" />
    <hkern u1="&#x148;" u2="&#x7d;" k="10" />
    <hkern u1="&#x148;" u2="v" k="5" />
    <hkern u1="&#x148;" u2="]" k="11" />
    <hkern u1="&#x148;" u2="\" k="38" />
    <hkern u1="&#x148;" u2="V" k="28" />
    <hkern u1="&#x148;" u2="&#x3f;" k="17" />
    <hkern u1="&#x148;" u2="&#x2a;" k="6" />
    <hkern u1="&#x148;" u2="&#x29;" k="8" />
    <hkern u1="&#x14a;" g2="braceright.cap" k="4" />
    <hkern u1="&#x14a;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x14a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x14a;" u2="&#xec;" k="-6" />
    <hkern u1="&#x14a;" u2="f" k="4" />
    <hkern u1="&#x14b;" u2="&#x2122;" k="15" />
    <hkern u1="&#x14b;" u2="&#x7d;" k="10" />
    <hkern u1="&#x14b;" u2="v" k="5" />
    <hkern u1="&#x14b;" u2="]" k="11" />
    <hkern u1="&#x14b;" u2="\" k="38" />
    <hkern u1="&#x14b;" u2="V" k="28" />
    <hkern u1="&#x14b;" u2="&#x3f;" k="17" />
    <hkern u1="&#x14b;" u2="&#x2a;" k="6" />
    <hkern u1="&#x14b;" u2="&#x29;" k="8" />
    <hkern u1="&#x14c;" g2="braceright.cap" k="19" />
    <hkern u1="&#x14c;" g2="bracketright.cap" k="24" />
    <hkern u1="&#x14c;" g2="parenright.cap" k="13" />
    <hkern u1="&#x14c;" u2="&#xc6;" k="14" />
    <hkern u1="&#x14c;" u2="&#x7d;" k="17" />
    <hkern u1="&#x14c;" u2="]" k="24" />
    <hkern u1="&#x14c;" u2="\" k="14" />
    <hkern u1="&#x14c;" u2="X" k="18" />
    <hkern u1="&#x14c;" u2="V" k="11" />
    <hkern u1="&#x14c;" u2="&#x3f;" k="3" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="10" />
    <hkern u1="&#x14c;" u2="&#x29;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2122;" k="15" />
    <hkern u1="&#x14d;" u2="&#xc6;" k="7" />
    <hkern u1="&#x14d;" u2="&#x7d;" k="25" />
    <hkern u1="&#x14d;" u2="x" k="11" />
    <hkern u1="&#x14d;" u2="v" k="8" />
    <hkern u1="&#x14d;" u2="]" k="32" />
    <hkern u1="&#x14d;" u2="\" k="39" />
    <hkern u1="&#x14d;" u2="X" k="24" />
    <hkern u1="&#x14d;" u2="V" k="30" />
    <hkern u1="&#x14d;" u2="&#x3f;" k="19" />
    <hkern u1="&#x14d;" u2="&#x2a;" k="6" />
    <hkern u1="&#x14d;" u2="&#x29;" k="19" />
    <hkern u1="&#x14e;" g2="braceright.cap" k="19" />
    <hkern u1="&#x14e;" g2="bracketright.cap" k="24" />
    <hkern u1="&#x14e;" g2="parenright.cap" k="13" />
    <hkern u1="&#x14e;" u2="&#xc6;" k="14" />
    <hkern u1="&#x14e;" u2="&#x7d;" k="17" />
    <hkern u1="&#x14e;" u2="]" k="24" />
    <hkern u1="&#x14e;" u2="\" k="14" />
    <hkern u1="&#x14e;" u2="X" k="18" />
    <hkern u1="&#x14e;" u2="V" k="11" />
    <hkern u1="&#x14e;" u2="&#x3f;" k="3" />
    <hkern u1="&#x14e;" u2="&#x2f;" k="10" />
    <hkern u1="&#x14e;" u2="&#x29;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2122;" k="15" />
    <hkern u1="&#x14f;" u2="&#xc6;" k="7" />
    <hkern u1="&#x14f;" u2="&#x7d;" k="25" />
    <hkern u1="&#x14f;" u2="x" k="11" />
    <hkern u1="&#x14f;" u2="v" k="8" />
    <hkern u1="&#x14f;" u2="]" k="32" />
    <hkern u1="&#x14f;" u2="\" k="39" />
    <hkern u1="&#x14f;" u2="X" k="24" />
    <hkern u1="&#x14f;" u2="V" k="30" />
    <hkern u1="&#x14f;" u2="&#x3f;" k="19" />
    <hkern u1="&#x14f;" u2="&#x2a;" k="6" />
    <hkern u1="&#x14f;" u2="&#x29;" k="19" />
    <hkern u1="&#x150;" g2="braceright.cap" k="19" />
    <hkern u1="&#x150;" g2="bracketright.cap" k="24" />
    <hkern u1="&#x150;" g2="parenright.cap" k="13" />
    <hkern u1="&#x150;" u2="&#xc6;" k="14" />
    <hkern u1="&#x150;" u2="&#x7d;" k="17" />
    <hkern u1="&#x150;" u2="]" k="24" />
    <hkern u1="&#x150;" u2="\" k="14" />
    <hkern u1="&#x150;" u2="X" k="18" />
    <hkern u1="&#x150;" u2="V" k="11" />
    <hkern u1="&#x150;" u2="&#x3f;" k="3" />
    <hkern u1="&#x150;" u2="&#x2f;" k="10" />
    <hkern u1="&#x150;" u2="&#x29;" k="10" />
    <hkern u1="&#x151;" u2="&#x2122;" k="15" />
    <hkern u1="&#x151;" u2="&#xc6;" k="7" />
    <hkern u1="&#x151;" u2="&#x7d;" k="25" />
    <hkern u1="&#x151;" u2="x" k="11" />
    <hkern u1="&#x151;" u2="v" k="8" />
    <hkern u1="&#x151;" u2="]" k="32" />
    <hkern u1="&#x151;" u2="\" k="39" />
    <hkern u1="&#x151;" u2="X" k="24" />
    <hkern u1="&#x151;" u2="V" k="30" />
    <hkern u1="&#x151;" u2="&#x3f;" k="19" />
    <hkern u1="&#x151;" u2="&#x2a;" k="6" />
    <hkern u1="&#x151;" u2="&#x29;" k="19" />
    <hkern u1="&#x152;" u2="&#x135;" k="-16" />
    <hkern u1="&#x152;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x152;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x152;" u2="&#x129;" k="-23" />
    <hkern u1="&#x152;" u2="&#xf0;" k="7" />
    <hkern u1="&#x152;" u2="&#xef;" k="-24" />
    <hkern u1="&#x152;" u2="&#xee;" k="-21" />
    <hkern u1="&#x152;" u2="&#xec;" k="-39" />
    <hkern u1="&#x152;" u2="v" k="5" />
    <hkern u1="&#x153;" u2="&#x2122;" k="13" />
    <hkern u1="&#x153;" u2="&#xc6;" k="5" />
    <hkern u1="&#x153;" u2="&#x7d;" k="18" />
    <hkern u1="&#x153;" u2="v" k="7" />
    <hkern u1="&#x153;" u2="]" k="11" />
    <hkern u1="&#x153;" u2="\" k="36" />
    <hkern u1="&#x153;" u2="X" k="4" />
    <hkern u1="&#x153;" u2="V" k="28" />
    <hkern u1="&#x153;" u2="&#x3f;" k="15" />
    <hkern u1="&#x153;" u2="&#x29;" k="9" />
    <hkern u1="&#x154;" g2="braceright.cap" k="5" />
    <hkern u1="&#x154;" g2="bracketright.cap" k="7" />
    <hkern u1="&#x154;" u2="&#xf0;" k="13" />
    <hkern u1="&#x154;" u2="&#xc6;" k="9" />
    <hkern u1="&#x154;" u2="&#x7d;" k="7" />
    <hkern u1="&#x154;" u2="]" k="8" />
    <hkern u1="&#x154;" u2="\" k="11" />
    <hkern u1="&#x154;" u2="X" k="4" />
    <hkern u1="&#x154;" u2="V" k="10" />
    <hkern u1="&#x155;" u2="&#xf0;" k="28" />
    <hkern u1="&#x155;" u2="&#xc6;" k="36" />
    <hkern u1="&#x155;" u2="&#x7d;" k="17" />
    <hkern u1="&#x155;" u2="]" k="23" />
    <hkern u1="&#x155;" u2="\" k="8" />
    <hkern u1="&#x155;" u2="X" k="28" />
    <hkern u1="&#x155;" u2="&#x2f;" k="33" />
    <hkern u1="&#x155;" u2="&#x29;" k="7" />
    <hkern u1="&#x155;" u2="&#x26;" k="4" />
    <hkern u1="&#x156;" g2="braceright.cap" k="5" />
    <hkern u1="&#x156;" g2="bracketright.cap" k="7" />
    <hkern u1="&#x156;" u2="&#xf0;" k="13" />
    <hkern u1="&#x156;" u2="&#xc6;" k="9" />
    <hkern u1="&#x156;" u2="&#x7d;" k="7" />
    <hkern u1="&#x156;" u2="]" k="8" />
    <hkern u1="&#x156;" u2="\" k="11" />
    <hkern u1="&#x156;" u2="X" k="4" />
    <hkern u1="&#x156;" u2="V" k="10" />
    <hkern u1="&#x157;" u2="&#xf0;" k="28" />
    <hkern u1="&#x157;" u2="&#xc6;" k="36" />
    <hkern u1="&#x157;" u2="&#x7d;" k="17" />
    <hkern u1="&#x157;" u2="]" k="23" />
    <hkern u1="&#x157;" u2="\" k="8" />
    <hkern u1="&#x157;" u2="X" k="28" />
    <hkern u1="&#x157;" u2="&#x2f;" k="33" />
    <hkern u1="&#x157;" u2="&#x29;" k="7" />
    <hkern u1="&#x157;" u2="&#x26;" k="4" />
    <hkern u1="&#x158;" g2="braceright.cap" k="5" />
    <hkern u1="&#x158;" g2="bracketright.cap" k="7" />
    <hkern u1="&#x158;" u2="&#xf0;" k="13" />
    <hkern u1="&#x158;" u2="&#xc6;" k="9" />
    <hkern u1="&#x158;" u2="&#x7d;" k="7" />
    <hkern u1="&#x158;" u2="]" k="8" />
    <hkern u1="&#x158;" u2="\" k="11" />
    <hkern u1="&#x158;" u2="X" k="4" />
    <hkern u1="&#x158;" u2="V" k="10" />
    <hkern u1="&#x159;" u2="&#xf0;" k="28" />
    <hkern u1="&#x159;" u2="&#xc6;" k="36" />
    <hkern u1="&#x159;" u2="&#x7d;" k="17" />
    <hkern u1="&#x159;" u2="]" k="23" />
    <hkern u1="&#x159;" u2="\" k="8" />
    <hkern u1="&#x159;" u2="X" k="28" />
    <hkern u1="&#x159;" u2="&#x2f;" k="33" />
    <hkern u1="&#x159;" u2="&#x29;" k="7" />
    <hkern u1="&#x159;" u2="&#x26;" k="4" />
    <hkern u1="&#x15a;" g2="braceright.cap" k="3" />
    <hkern u1="&#x15a;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x15a;" u2="&#x129;" k="-7" />
    <hkern u1="&#x15a;" u2="&#xef;" k="-15" />
    <hkern u1="&#x15a;" u2="&#xee;" k="-6" />
    <hkern u1="&#x15a;" u2="&#xec;" k="-24" />
    <hkern u1="&#x15a;" u2="&#xc6;" k="11" />
    <hkern u1="&#x15a;" u2="x" k="9" />
    <hkern u1="&#x15a;" u2="v" k="8" />
    <hkern u1="&#x15a;" u2="f" k="8" />
    <hkern u1="&#x15a;" u2="X" k="5" />
    <hkern u1="&#x15a;" u2="V" k="9" />
    <hkern u1="&#x15b;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15b;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15b;" u2="&#x7d;" k="19" />
    <hkern u1="&#x15b;" u2="v" k="6" />
    <hkern u1="&#x15b;" u2="]" k="24" />
    <hkern u1="&#x15b;" u2="\" k="26" />
    <hkern u1="&#x15b;" u2="X" k="6" />
    <hkern u1="&#x15b;" u2="V" k="20" />
    <hkern u1="&#x15b;" u2="&#x3f;" k="5" />
    <hkern u1="&#x15b;" u2="&#x29;" k="10" />
    <hkern u1="&#x15c;" g2="braceright.cap" k="3" />
    <hkern u1="&#x15c;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x15c;" u2="&#x129;" k="-7" />
    <hkern u1="&#x15c;" u2="&#xef;" k="-15" />
    <hkern u1="&#x15c;" u2="&#xee;" k="-6" />
    <hkern u1="&#x15c;" u2="&#xec;" k="-24" />
    <hkern u1="&#x15c;" u2="&#xc6;" k="11" />
    <hkern u1="&#x15c;" u2="x" k="9" />
    <hkern u1="&#x15c;" u2="v" k="8" />
    <hkern u1="&#x15c;" u2="f" k="8" />
    <hkern u1="&#x15c;" u2="X" k="5" />
    <hkern u1="&#x15c;" u2="V" k="9" />
    <hkern u1="&#x15d;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15d;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15d;" u2="&#x7d;" k="19" />
    <hkern u1="&#x15d;" u2="v" k="6" />
    <hkern u1="&#x15d;" u2="]" k="24" />
    <hkern u1="&#x15d;" u2="\" k="26" />
    <hkern u1="&#x15d;" u2="X" k="6" />
    <hkern u1="&#x15d;" u2="V" k="20" />
    <hkern u1="&#x15d;" u2="&#x3f;" k="5" />
    <hkern u1="&#x15d;" u2="&#x29;" k="10" />
    <hkern u1="&#x15e;" g2="braceright.cap" k="3" />
    <hkern u1="&#x15e;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x15e;" u2="&#x129;" k="-7" />
    <hkern u1="&#x15e;" u2="&#xef;" k="-15" />
    <hkern u1="&#x15e;" u2="&#xee;" k="-6" />
    <hkern u1="&#x15e;" u2="&#xec;" k="-24" />
    <hkern u1="&#x15e;" u2="&#xc6;" k="11" />
    <hkern u1="&#x15e;" u2="x" k="9" />
    <hkern u1="&#x15e;" u2="v" k="8" />
    <hkern u1="&#x15e;" u2="f" k="8" />
    <hkern u1="&#x15e;" u2="X" k="5" />
    <hkern u1="&#x15e;" u2="V" k="9" />
    <hkern u1="&#x15f;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15f;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15f;" u2="&#x7d;" k="19" />
    <hkern u1="&#x15f;" u2="v" k="6" />
    <hkern u1="&#x15f;" u2="]" k="24" />
    <hkern u1="&#x15f;" u2="\" k="26" />
    <hkern u1="&#x15f;" u2="X" k="6" />
    <hkern u1="&#x15f;" u2="V" k="20" />
    <hkern u1="&#x15f;" u2="&#x3f;" k="5" />
    <hkern u1="&#x15f;" u2="&#x29;" k="10" />
    <hkern u1="&#x160;" g2="braceright.cap" k="3" />
    <hkern u1="&#x160;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x160;" u2="&#x129;" k="-7" />
    <hkern u1="&#x160;" u2="&#xef;" k="-15" />
    <hkern u1="&#x160;" u2="&#xee;" k="-6" />
    <hkern u1="&#x160;" u2="&#xec;" k="-24" />
    <hkern u1="&#x160;" u2="&#xc6;" k="11" />
    <hkern u1="&#x160;" u2="x" k="9" />
    <hkern u1="&#x160;" u2="v" k="8" />
    <hkern u1="&#x160;" u2="f" k="8" />
    <hkern u1="&#x160;" u2="X" k="5" />
    <hkern u1="&#x160;" u2="V" k="9" />
    <hkern u1="&#x161;" u2="&#x2122;" k="11" />
    <hkern u1="&#x161;" u2="&#xc6;" k="5" />
    <hkern u1="&#x161;" u2="&#x7d;" k="19" />
    <hkern u1="&#x161;" u2="v" k="6" />
    <hkern u1="&#x161;" u2="]" k="24" />
    <hkern u1="&#x161;" u2="\" k="26" />
    <hkern u1="&#x161;" u2="X" k="6" />
    <hkern u1="&#x161;" u2="V" k="20" />
    <hkern u1="&#x161;" u2="&#x3f;" k="5" />
    <hkern u1="&#x161;" u2="&#x29;" k="10" />
    <hkern u1="&#x164;" u2="&#x1ef9;" k="55" />
    <hkern u1="&#x164;" u2="&#x1eab;" k="63" />
    <hkern u1="&#x164;" u2="&#x16d;" k="66" />
    <hkern u1="&#x164;" u2="&#x169;" k="66" />
    <hkern u1="&#x164;" u2="&#x15d;" k="68" />
    <hkern u1="&#x164;" u2="&#x159;" k="42" />
    <hkern u1="&#x164;" u2="&#x155;" k="51" />
    <hkern u1="&#x164;" u2="&#x151;" k="59" />
    <hkern u1="&#x164;" u2="&#x135;" k="-36" />
    <hkern u1="&#x164;" u2="&#x131;" k="67" />
    <hkern u1="&#x164;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x164;" u2="&#x12b;" k="-30" />
    <hkern u1="&#x164;" u2="&#x129;" k="-52" />
    <hkern u1="&#x164;" u2="&#x11f;" k="83" />
    <hkern u1="&#x164;" u2="&#x109;" k="59" />
    <hkern u1="&#x164;" u2="&#xf0;" k="25" />
    <hkern u1="&#x164;" u2="&#xef;" k="-51" />
    <hkern u1="&#x164;" u2="&#xee;" k="-41" />
    <hkern u1="&#x164;" u2="&#xec;" k="-69" />
    <hkern u1="&#x164;" u2="&#xe4;" k="66" />
    <hkern u1="&#x164;" u2="&#xe3;" k="54" />
    <hkern u1="&#x164;" u2="&#xc6;" k="51" />
    <hkern u1="&#x164;" u2="&#xae;" k="4" />
    <hkern u1="&#x164;" u2="x" k="59" />
    <hkern u1="&#x164;" u2="v" k="57" />
    <hkern u1="&#x164;" u2="f" k="15" />
    <hkern u1="&#x164;" u2="&#x40;" k="20" />
    <hkern u1="&#x164;" u2="&#x2f;" k="50" />
    <hkern u1="&#x164;" u2="&#x26;" k="15" />
    <hkern u1="&#x165;" u2="&#x203a;" k="11" />
    <hkern u1="&#x165;" u2="&#x2039;" k="51" />
    <hkern u1="&#x165;" u2="&#x2026;" k="38" />
    <hkern u1="&#x165;" u2="&#x201e;" k="38" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-6" />
    <hkern u1="&#x165;" u2="&#x201a;" k="38" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-6" />
    <hkern u1="&#x165;" u2="&#x2014;" k="62" />
    <hkern u1="&#x165;" u2="&#x2013;" k="62" />
    <hkern u1="&#x165;" u2="&#x1ef9;" k="-19" />
    <hkern u1="&#x165;" u2="&#x1ef3;" k="-19" />
    <hkern u1="&#x165;" u2="&#x1ed7;" k="12" />
    <hkern u1="&#x165;" u2="&#x1ec5;" k="12" />
    <hkern u1="&#x165;" u2="&#x1e85;" k="-13" />
    <hkern u1="&#x165;" u2="&#x1e83;" k="-13" />
    <hkern u1="&#x165;" u2="&#x1e81;" k="-13" />
    <hkern u1="&#x165;" u2="&#x21b;" k="-14" />
    <hkern u1="&#x165;" u2="&#x1ff;" k="12" />
    <hkern u1="&#x165;" u2="&#x177;" k="-19" />
    <hkern u1="&#x165;" u2="&#x175;" k="-13" />
    <hkern u1="&#x165;" u2="&#x167;" k="-14" />
    <hkern u1="&#x165;" u2="&#x165;" k="-14" />
    <hkern u1="&#x165;" u2="&#x153;" k="12" />
    <hkern u1="&#x165;" u2="&#x151;" k="12" />
    <hkern u1="&#x165;" u2="&#x14f;" k="12" />
    <hkern u1="&#x165;" u2="&#x14d;" k="12" />
    <hkern u1="&#x165;" u2="&#x142;" k="-6" />
    <hkern u1="&#x165;" u2="&#x13e;" k="-6" />
    <hkern u1="&#x165;" u2="&#x13c;" k="-6" />
    <hkern u1="&#x165;" u2="&#x13a;" k="-6" />
    <hkern u1="&#x165;" u2="&#x137;" k="-10" />
    <hkern u1="&#x165;" u2="&#x135;" k="-10" />
    <hkern u1="&#x165;" u2="&#x131;" k="-10" />
    <hkern u1="&#x165;" u2="&#x12f;" k="-10" />
    <hkern u1="&#x165;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x165;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x165;" u2="&#x129;" k="-10" />
    <hkern u1="&#x165;" u2="&#x127;" k="-10" />
    <hkern u1="&#x165;" u2="&#x125;" k="-10" />
    <hkern u1="&#x165;" u2="&#x123;" k="7" />
    <hkern u1="&#x165;" u2="&#x121;" k="7" />
    <hkern u1="&#x165;" u2="&#x11f;" k="7" />
    <hkern u1="&#x165;" u2="&#x11d;" k="7" />
    <hkern u1="&#x165;" u2="&#x11b;" k="12" />
    <hkern u1="&#x165;" u2="&#x119;" k="12" />
    <hkern u1="&#x165;" u2="&#x117;" k="12" />
    <hkern u1="&#x165;" u2="&#x115;" k="12" />
    <hkern u1="&#x165;" u2="&#x113;" k="12" />
    <hkern u1="&#x165;" u2="&#x111;" k="12" />
    <hkern u1="&#x165;" u2="&#x10f;" k="12" />
    <hkern u1="&#x165;" u2="&#x10d;" k="12" />
    <hkern u1="&#x165;" u2="&#x10b;" k="12" />
    <hkern u1="&#x165;" u2="&#x109;" k="12" />
    <hkern u1="&#x165;" u2="&#x107;" k="12" />
    <hkern u1="&#x165;" u2="&#xff;" k="-19" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-10" />
    <hkern u1="&#x165;" u2="&#xfd;" k="-19" />
    <hkern u1="&#x165;" u2="&#xf8;" k="12" />
    <hkern u1="&#x165;" u2="&#xf6;" k="12" />
    <hkern u1="&#x165;" u2="&#xf5;" k="12" />
    <hkern u1="&#x165;" u2="&#xf4;" k="12" />
    <hkern u1="&#x165;" u2="&#xf3;" k="12" />
    <hkern u1="&#x165;" u2="&#xf2;" k="12" />
    <hkern u1="&#x165;" u2="&#xf0;" k="16" />
    <hkern u1="&#x165;" u2="&#xef;" k="-10" />
    <hkern u1="&#x165;" u2="&#xee;" k="-10" />
    <hkern u1="&#x165;" u2="&#xed;" k="-10" />
    <hkern u1="&#x165;" u2="&#xec;" k="-10" />
    <hkern u1="&#x165;" u2="&#xeb;" k="12" />
    <hkern u1="&#x165;" u2="&#xea;" k="12" />
    <hkern u1="&#x165;" u2="&#xe9;" k="12" />
    <hkern u1="&#x165;" u2="&#xe8;" k="12" />
    <hkern u1="&#x165;" u2="&#xe7;" k="12" />
    <hkern u1="&#x165;" u2="&#xe4;" k="-8" />
    <hkern u1="&#x165;" u2="&#xdf;" k="-10" />
    <hkern u1="&#x165;" u2="&#xbb;" k="11" />
    <hkern u1="&#x165;" u2="&#xab;" k="51" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-6" />
    <hkern u1="&#x165;" u2="y" k="-19" />
    <hkern u1="&#x165;" u2="x" k="-20" />
    <hkern u1="&#x165;" u2="w" k="-13" />
    <hkern u1="&#x165;" u2="v" k="-20" />
    <hkern u1="&#x165;" u2="t" k="-14" />
    <hkern u1="&#x165;" u2="q" k="12" />
    <hkern u1="&#x165;" u2="o" k="12" />
    <hkern u1="&#x165;" u2="l" k="-6" />
    <hkern u1="&#x165;" u2="k" k="-10" />
    <hkern u1="&#x165;" u2="j" k="-10" />
    <hkern u1="&#x165;" u2="i" k="-10" />
    <hkern u1="&#x165;" u2="h" k="-10" />
    <hkern u1="&#x165;" u2="g" k="7" />
    <hkern u1="&#x165;" u2="f" k="-7" />
    <hkern u1="&#x165;" u2="e" k="12" />
    <hkern u1="&#x165;" u2="d" k="12" />
    <hkern u1="&#x165;" u2="c" k="12" />
    <hkern u1="&#x165;" u2="b" k="-10" />
    <hkern u1="&#x165;" u2="]" k="-7" />
    <hkern u1="&#x165;" u2="\" k="-10" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x165;" u2="&#x2f;" k="27" />
    <hkern u1="&#x165;" u2="&#x2e;" k="38" />
    <hkern u1="&#x165;" u2="&#x2d;" k="62" />
    <hkern u1="&#x165;" u2="&#x2c;" k="38" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-21" />
    <hkern u1="&#x166;" u2="&#x1ef9;" k="55" />
    <hkern u1="&#x166;" u2="&#x1eab;" k="63" />
    <hkern u1="&#x166;" u2="&#x16d;" k="66" />
    <hkern u1="&#x166;" u2="&#x169;" k="66" />
    <hkern u1="&#x166;" u2="&#x15d;" k="68" />
    <hkern u1="&#x166;" u2="&#x159;" k="42" />
    <hkern u1="&#x166;" u2="&#x155;" k="51" />
    <hkern u1="&#x166;" u2="&#x151;" k="59" />
    <hkern u1="&#x166;" u2="&#x135;" k="-36" />
    <hkern u1="&#x166;" u2="&#x131;" k="67" />
    <hkern u1="&#x166;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x166;" u2="&#x12b;" k="-30" />
    <hkern u1="&#x166;" u2="&#x129;" k="-52" />
    <hkern u1="&#x166;" u2="&#x11f;" k="83" />
    <hkern u1="&#x166;" u2="&#x109;" k="59" />
    <hkern u1="&#x166;" u2="&#xf0;" k="25" />
    <hkern u1="&#x166;" u2="&#xef;" k="-51" />
    <hkern u1="&#x166;" u2="&#xee;" k="-41" />
    <hkern u1="&#x166;" u2="&#xec;" k="-69" />
    <hkern u1="&#x166;" u2="&#xe4;" k="66" />
    <hkern u1="&#x166;" u2="&#xe3;" k="54" />
    <hkern u1="&#x166;" u2="&#xc6;" k="51" />
    <hkern u1="&#x166;" u2="&#xae;" k="4" />
    <hkern u1="&#x166;" u2="x" k="59" />
    <hkern u1="&#x166;" u2="v" k="57" />
    <hkern u1="&#x166;" u2="f" k="15" />
    <hkern u1="&#x166;" u2="&#x40;" k="20" />
    <hkern u1="&#x166;" u2="&#x2f;" k="50" />
    <hkern u1="&#x166;" u2="&#x26;" k="15" />
    <hkern u1="&#x167;" u2="&#x7d;" k="4" />
    <hkern u1="&#x167;" u2="]" k="5" />
    <hkern u1="&#x167;" u2="\" k="13" />
    <hkern u1="&#x167;" u2="V" k="4" />
    <hkern u1="&#x168;" g2="braceright.cap" k="5" />
    <hkern u1="&#x168;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x168;" u2="&#xf0;" k="6" />
    <hkern u1="&#x168;" u2="&#xec;" k="-10" />
    <hkern u1="&#x168;" u2="&#xc6;" k="8" />
    <hkern u1="&#x168;" u2="f" k="4" />
    <hkern u1="&#x168;" u2="&#x2f;" k="12" />
    <hkern u1="&#x169;" u2="&#x2122;" k="10" />
    <hkern u1="&#x169;" u2="&#x7d;" k="10" />
    <hkern u1="&#x169;" u2="]" k="11" />
    <hkern u1="&#x169;" u2="\" k="26" />
    <hkern u1="&#x169;" u2="X" k="4" />
    <hkern u1="&#x169;" u2="V" k="23" />
    <hkern u1="&#x169;" u2="&#x3f;" k="5" />
    <hkern u1="&#x169;" u2="&#x29;" k="8" />
    <hkern u1="&#x16a;" g2="braceright.cap" k="5" />
    <hkern u1="&#x16a;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x16a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x16a;" u2="&#xec;" k="-10" />
    <hkern u1="&#x16a;" u2="&#xc6;" k="8" />
    <hkern u1="&#x16a;" u2="f" k="4" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="12" />
    <hkern u1="&#x16b;" u2="&#x2122;" k="10" />
    <hkern u1="&#x16b;" u2="&#x7d;" k="10" />
    <hkern u1="&#x16b;" u2="]" k="11" />
    <hkern u1="&#x16b;" u2="\" k="26" />
    <hkern u1="&#x16b;" u2="X" k="4" />
    <hkern u1="&#x16b;" u2="V" k="23" />
    <hkern u1="&#x16b;" u2="&#x3f;" k="5" />
    <hkern u1="&#x16b;" u2="&#x29;" k="8" />
    <hkern u1="&#x16c;" g2="braceright.cap" k="5" />
    <hkern u1="&#x16c;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x16c;" u2="&#xf0;" k="6" />
    <hkern u1="&#x16c;" u2="&#xec;" k="-10" />
    <hkern u1="&#x16c;" u2="&#xc6;" k="8" />
    <hkern u1="&#x16c;" u2="f" k="4" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="12" />
    <hkern u1="&#x16d;" u2="&#x2122;" k="10" />
    <hkern u1="&#x16d;" u2="&#x7d;" k="10" />
    <hkern u1="&#x16d;" u2="]" k="11" />
    <hkern u1="&#x16d;" u2="\" k="26" />
    <hkern u1="&#x16d;" u2="X" k="4" />
    <hkern u1="&#x16d;" u2="V" k="23" />
    <hkern u1="&#x16d;" u2="&#x3f;" k="5" />
    <hkern u1="&#x16d;" u2="&#x29;" k="8" />
    <hkern u1="&#x16e;" g2="braceright.cap" k="5" />
    <hkern u1="&#x16e;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x16e;" u2="&#xf0;" k="6" />
    <hkern u1="&#x16e;" u2="&#xec;" k="-10" />
    <hkern u1="&#x16e;" u2="&#xc6;" k="8" />
    <hkern u1="&#x16e;" u2="f" k="4" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="12" />
    <hkern u1="&#x16f;" u2="&#x2122;" k="10" />
    <hkern u1="&#x16f;" u2="&#x7d;" k="10" />
    <hkern u1="&#x16f;" u2="]" k="11" />
    <hkern u1="&#x16f;" u2="\" k="26" />
    <hkern u1="&#x16f;" u2="X" k="4" />
    <hkern u1="&#x16f;" u2="V" k="23" />
    <hkern u1="&#x16f;" u2="&#x3f;" k="5" />
    <hkern u1="&#x16f;" u2="&#x29;" k="8" />
    <hkern u1="&#x170;" g2="braceright.cap" k="5" />
    <hkern u1="&#x170;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x170;" u2="&#xf0;" k="6" />
    <hkern u1="&#x170;" u2="&#xec;" k="-10" />
    <hkern u1="&#x170;" u2="&#xc6;" k="8" />
    <hkern u1="&#x170;" u2="f" k="4" />
    <hkern u1="&#x170;" u2="&#x2f;" k="12" />
    <hkern u1="&#x171;" u2="&#x2122;" k="10" />
    <hkern u1="&#x171;" u2="&#x7d;" k="10" />
    <hkern u1="&#x171;" u2="]" k="11" />
    <hkern u1="&#x171;" u2="\" k="26" />
    <hkern u1="&#x171;" u2="X" k="4" />
    <hkern u1="&#x171;" u2="V" k="23" />
    <hkern u1="&#x171;" u2="&#x3f;" k="5" />
    <hkern u1="&#x171;" u2="&#x29;" k="8" />
    <hkern u1="&#x172;" g2="braceright.cap" k="5" />
    <hkern u1="&#x172;" g2="bracketright.cap" k="5" />
    <hkern u1="&#x172;" u2="&#xf0;" k="6" />
    <hkern u1="&#x172;" u2="&#xec;" k="-10" />
    <hkern u1="&#x172;" u2="&#xc6;" k="8" />
    <hkern u1="&#x172;" u2="f" k="4" />
    <hkern u1="&#x172;" u2="&#x2f;" k="12" />
    <hkern u1="&#x173;" u2="&#x2122;" k="10" />
    <hkern u1="&#x173;" u2="&#x7d;" k="10" />
    <hkern u1="&#x173;" u2="j" k="-8" />
    <hkern u1="&#x173;" u2="]" k="11" />
    <hkern u1="&#x173;" u2="\" k="26" />
    <hkern u1="&#x173;" u2="X" k="4" />
    <hkern u1="&#x173;" u2="V" k="23" />
    <hkern u1="&#x173;" u2="&#x3f;" k="5" />
    <hkern u1="&#x173;" u2="&#x29;" k="8" />
    <hkern u1="&#x174;" g2="braceright.cap" k="4" />
    <hkern u1="&#x174;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x174;" u2="&#x135;" k="-20" />
    <hkern u1="&#x174;" u2="&#x131;" k="14" />
    <hkern u1="&#x174;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x174;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x174;" u2="&#x129;" k="-37" />
    <hkern u1="&#x174;" u2="&#xf0;" k="15" />
    <hkern u1="&#x174;" u2="&#xef;" k="-39" />
    <hkern u1="&#x174;" u2="&#xee;" k="-22" />
    <hkern u1="&#x174;" u2="&#xec;" k="-51" />
    <hkern u1="&#x174;" u2="&#xc6;" k="25" />
    <hkern u1="&#x174;" u2="&#x2f;" k="28" />
    <hkern u1="&#x174;" u2="&#x26;" k="3" />
    <hkern u1="&#x175;" u2="&#xf0;" k="8" />
    <hkern u1="&#x175;" u2="&#xc6;" k="16" />
    <hkern u1="&#x175;" u2="&#x7d;" k="21" />
    <hkern u1="&#x175;" u2="]" k="26" />
    <hkern u1="&#x175;" u2="\" k="13" />
    <hkern u1="&#x175;" u2="X" k="24" />
    <hkern u1="&#x175;" u2="V" k="9" />
    <hkern u1="&#x175;" u2="&#x3f;" k="4" />
    <hkern u1="&#x175;" u2="&#x2f;" k="15" />
    <hkern u1="&#x175;" u2="&#x29;" k="10" />
    <hkern u1="&#x176;" g2="braceright.cap" k="5" />
    <hkern u1="&#x176;" g2="bracketright.cap" k="6" />
    <hkern u1="&#x176;" u2="&#x1ef9;" k="26" />
    <hkern u1="&#x176;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#x176;" u2="&#x159;" k="32" />
    <hkern u1="&#x176;" u2="&#x155;" k="38" />
    <hkern u1="&#x176;" u2="&#x151;" k="54" />
    <hkern u1="&#x176;" u2="&#x142;" k="6" />
    <hkern u1="&#x176;" u2="&#x135;" k="-11" />
    <hkern u1="&#x176;" u2="&#x131;" k="60" />
    <hkern u1="&#x176;" u2="&#x12d;" k="-45" />
    <hkern u1="&#x176;" u2="&#x12b;" k="-34" />
    <hkern u1="&#x176;" u2="&#x129;" k="-47" />
    <hkern u1="&#x176;" u2="&#x103;" k="59" />
    <hkern u1="&#x176;" u2="&#xff;" k="28" />
    <hkern u1="&#x176;" u2="&#xf0;" k="32" />
    <hkern u1="&#x176;" u2="&#xef;" k="-59" />
    <hkern u1="&#x176;" u2="&#xee;" k="-16" />
    <hkern u1="&#x176;" u2="&#xec;" k="-63" />
    <hkern u1="&#x176;" u2="&#xeb;" k="64" />
    <hkern u1="&#x176;" u2="&#xe4;" k="49" />
    <hkern u1="&#x176;" u2="&#xe3;" k="42" />
    <hkern u1="&#x176;" u2="&#xdf;" k="10" />
    <hkern u1="&#x176;" u2="&#xc6;" k="56" />
    <hkern u1="&#x176;" u2="&#xae;" k="20" />
    <hkern u1="&#x176;" u2="x" k="37" />
    <hkern u1="&#x176;" u2="v" k="36" />
    <hkern u1="&#x176;" u2="f" k="21" />
    <hkern u1="&#x176;" u2="&#x40;" k="36" />
    <hkern u1="&#x176;" u2="&#x2f;" k="65" />
    <hkern u1="&#x176;" u2="&#x2a;" k="-4" />
    <hkern u1="&#x176;" u2="&#x26;" k="32" />
    <hkern u1="&#x177;" u2="&#xf0;" k="13" />
    <hkern u1="&#x177;" u2="&#xc6;" k="19" />
    <hkern u1="&#x177;" u2="&#x7d;" k="17" />
    <hkern u1="&#x177;" u2="]" k="23" />
    <hkern u1="&#x177;" u2="\" k="13" />
    <hkern u1="&#x177;" u2="X" k="24" />
    <hkern u1="&#x177;" u2="V" k="7" />
    <hkern u1="&#x177;" u2="&#x3f;" k="4" />
    <hkern u1="&#x177;" u2="&#x2f;" k="20" />
    <hkern u1="&#x178;" g2="braceright.cap" k="5" />
    <hkern u1="&#x178;" g2="bracketright.cap" k="6" />
    <hkern u1="&#x178;" u2="&#x1ef9;" k="26" />
    <hkern u1="&#x178;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#x178;" u2="&#x159;" k="32" />
    <hkern u1="&#x178;" u2="&#x155;" k="38" />
    <hkern u1="&#x178;" u2="&#x151;" k="54" />
    <hkern u1="&#x178;" u2="&#x142;" k="6" />
    <hkern u1="&#x178;" u2="&#x135;" k="-11" />
    <hkern u1="&#x178;" u2="&#x131;" k="60" />
    <hkern u1="&#x178;" u2="&#x12d;" k="-45" />
    <hkern u1="&#x178;" u2="&#x12b;" k="-34" />
    <hkern u1="&#x178;" u2="&#x129;" k="-47" />
    <hkern u1="&#x178;" u2="&#x103;" k="59" />
    <hkern u1="&#x178;" u2="&#xff;" k="28" />
    <hkern u1="&#x178;" u2="&#xf0;" k="32" />
    <hkern u1="&#x178;" u2="&#xef;" k="-59" />
    <hkern u1="&#x178;" u2="&#xee;" k="-16" />
    <hkern u1="&#x178;" u2="&#xec;" k="-63" />
    <hkern u1="&#x178;" u2="&#xeb;" k="64" />
    <hkern u1="&#x178;" u2="&#xe4;" k="49" />
    <hkern u1="&#x178;" u2="&#xe3;" k="42" />
    <hkern u1="&#x178;" u2="&#xdf;" k="10" />
    <hkern u1="&#x178;" u2="&#xc6;" k="56" />
    <hkern u1="&#x178;" u2="&#xae;" k="20" />
    <hkern u1="&#x178;" u2="x" k="37" />
    <hkern u1="&#x178;" u2="v" k="36" />
    <hkern u1="&#x178;" u2="f" k="21" />
    <hkern u1="&#x178;" u2="&#x40;" k="36" />
    <hkern u1="&#x178;" u2="&#x2f;" k="65" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-4" />
    <hkern u1="&#x178;" u2="&#x26;" k="32" />
    <hkern u1="&#x179;" u2="&#x135;" k="-18" />
    <hkern u1="&#x179;" u2="&#x12d;" k="-5" />
    <hkern u1="&#x179;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x179;" u2="&#x129;" k="-23" />
    <hkern u1="&#x179;" u2="&#xf0;" k="8" />
    <hkern u1="&#x179;" u2="&#xef;" k="-22" />
    <hkern u1="&#x179;" u2="&#xee;" k="-23" />
    <hkern u1="&#x179;" u2="&#xec;" k="-40" />
    <hkern u1="&#x179;" u2="&#xae;" k="3" />
    <hkern u1="&#x179;" u2="v" k="9" />
    <hkern u1="&#x179;" u2="f" k="5" />
    <hkern u1="&#x17a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x17a;" u2="&#x7d;" k="8" />
    <hkern u1="&#x17a;" u2="]" k="9" />
    <hkern u1="&#x17a;" u2="\" k="19" />
    <hkern u1="&#x17a;" u2="V" k="13" />
    <hkern u1="&#x17a;" u2="&#x3f;" k="4" />
    <hkern u1="&#x17b;" u2="&#x135;" k="-18" />
    <hkern u1="&#x17b;" u2="&#x12d;" k="-5" />
    <hkern u1="&#x17b;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x17b;" u2="&#x129;" k="-23" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="8" />
    <hkern u1="&#x17b;" u2="&#xef;" k="-22" />
    <hkern u1="&#x17b;" u2="&#xee;" k="-23" />
    <hkern u1="&#x17b;" u2="&#xec;" k="-40" />
    <hkern u1="&#x17b;" u2="&#xae;" k="3" />
    <hkern u1="&#x17b;" u2="v" k="9" />
    <hkern u1="&#x17b;" u2="f" k="5" />
    <hkern u1="&#x17c;" u2="&#xf0;" k="6" />
    <hkern u1="&#x17c;" u2="&#x7d;" k="8" />
    <hkern u1="&#x17c;" u2="]" k="9" />
    <hkern u1="&#x17c;" u2="\" k="19" />
    <hkern u1="&#x17c;" u2="V" k="13" />
    <hkern u1="&#x17c;" u2="&#x3f;" k="4" />
    <hkern u1="&#x17d;" u2="&#x135;" k="-18" />
    <hkern u1="&#x17d;" u2="&#x12d;" k="-5" />
    <hkern u1="&#x17d;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x17d;" u2="&#x129;" k="-23" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="8" />
    <hkern u1="&#x17d;" u2="&#xef;" k="-22" />
    <hkern u1="&#x17d;" u2="&#xee;" k="-23" />
    <hkern u1="&#x17d;" u2="&#xec;" k="-40" />
    <hkern u1="&#x17d;" u2="&#xae;" k="3" />
    <hkern u1="&#x17d;" u2="v" k="9" />
    <hkern u1="&#x17d;" u2="f" k="5" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="6" />
    <hkern u1="&#x17e;" u2="&#x7d;" k="8" />
    <hkern u1="&#x17e;" u2="]" k="9" />
    <hkern u1="&#x17e;" u2="\" k="19" />
    <hkern u1="&#x17e;" u2="V" k="13" />
    <hkern u1="&#x17e;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1fa;" g2="braceright.cap" k="6" />
    <hkern u1="&#x1fa;" g2="bracketright.cap" k="8" />
    <hkern u1="&#x1fa;" u2="&#x2122;" k="35" />
    <hkern u1="&#x1fa;" u2="&#xf0;" k="5" />
    <hkern u1="&#x1fa;" u2="&#xae;" k="21" />
    <hkern u1="&#x1fa;" u2="&#x7d;" k="9" />
    <hkern u1="&#x1fa;" u2="v" k="16" />
    <hkern u1="&#x1fa;" u2="f" k="8" />
    <hkern u1="&#x1fa;" u2="]" k="10" />
    <hkern u1="&#x1fa;" u2="\" k="44" />
    <hkern u1="&#x1fa;" u2="V" k="28" />
    <hkern u1="&#x1fa;" u2="&#x3f;" k="19" />
    <hkern u1="&#x1fa;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1fb;" u2="&#x2122;" k="14" />
    <hkern u1="&#x1fb;" u2="&#x7d;" k="4" />
    <hkern u1="&#x1fb;" u2="v" k="5" />
    <hkern u1="&#x1fb;" u2="]" k="5" />
    <hkern u1="&#x1fb;" u2="\" k="40" />
    <hkern u1="&#x1fb;" u2="V" k="27" />
    <hkern u1="&#x1fb;" u2="&#x3f;" k="14" />
    <hkern u1="&#x1fb;" u2="&#x2a;" k="6" />
    <hkern u1="&#x1fc;" u2="&#x135;" k="-16" />
    <hkern u1="&#x1fc;" u2="&#x12d;" k="-8" />
    <hkern u1="&#x1fc;" u2="&#x12b;" k="-3" />
    <hkern u1="&#x1fc;" u2="&#x129;" k="-23" />
    <hkern u1="&#x1fc;" u2="&#xf0;" k="7" />
    <hkern u1="&#x1fc;" u2="&#xef;" k="-24" />
    <hkern u1="&#x1fc;" u2="&#xee;" k="-21" />
    <hkern u1="&#x1fc;" u2="&#xec;" k="-39" />
    <hkern u1="&#x1fc;" u2="v" k="5" />
    <hkern u1="&#x1fd;" u2="&#x2122;" k="13" />
    <hkern u1="&#x1fd;" u2="&#xc6;" k="5" />
    <hkern u1="&#x1fd;" u2="&#x7d;" k="18" />
    <hkern u1="&#x1fd;" u2="v" k="7" />
    <hkern u1="&#x1fd;" u2="]" k="11" />
    <hkern u1="&#x1fd;" u2="\" k="36" />
    <hkern u1="&#x1fd;" u2="X" k="4" />
    <hkern u1="&#x1fd;" u2="V" k="28" />
    <hkern u1="&#x1fd;" u2="&#x3f;" k="15" />
    <hkern u1="&#x1fd;" u2="&#x29;" k="9" />
    <hkern u1="&#x1fe;" g2="braceright.cap" k="19" />
    <hkern u1="&#x1fe;" g2="bracketright.cap" k="24" />
    <hkern u1="&#x1fe;" g2="parenright.cap" k="13" />
    <hkern u1="&#x1fe;" u2="&#xc6;" k="14" />
    <hkern u1="&#x1fe;" u2="&#x7d;" k="17" />
    <hkern u1="&#x1fe;" u2="]" k="24" />
    <hkern u1="&#x1fe;" u2="\" k="14" />
    <hkern u1="&#x1fe;" u2="X" k="18" />
    <hkern u1="&#x1fe;" u2="V" k="11" />
    <hkern u1="&#x1fe;" u2="&#x3f;" k="3" />
    <hkern u1="&#x1fe;" u2="&#x2f;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x29;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2122;" k="15" />
    <hkern u1="&#x1ff;" u2="&#xc6;" k="7" />
    <hkern u1="&#x1ff;" u2="&#x7d;" k="25" />
    <hkern u1="&#x1ff;" u2="x" k="11" />
    <hkern u1="&#x1ff;" u2="v" k="8" />
    <hkern u1="&#x1ff;" u2="]" k="32" />
    <hkern u1="&#x1ff;" u2="\" k="39" />
    <hkern u1="&#x1ff;" u2="X" k="24" />
    <hkern u1="&#x1ff;" u2="V" k="30" />
    <hkern u1="&#x1ff;" u2="&#x3f;" k="19" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="6" />
    <hkern u1="&#x1ff;" u2="&#x29;" k="19" />
    <hkern u1="&#x218;" g2="braceright.cap" k="3" />
    <hkern u1="&#x218;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x218;" u2="&#x129;" k="-7" />
    <hkern u1="&#x218;" u2="&#xef;" k="-15" />
    <hkern u1="&#x218;" u2="&#xee;" k="-6" />
    <hkern u1="&#x218;" u2="&#xec;" k="-24" />
    <hkern u1="&#x218;" u2="&#xc6;" k="11" />
    <hkern u1="&#x218;" u2="x" k="9" />
    <hkern u1="&#x218;" u2="v" k="8" />
    <hkern u1="&#x218;" u2="f" k="8" />
    <hkern u1="&#x218;" u2="X" k="5" />
    <hkern u1="&#x218;" u2="V" k="9" />
    <hkern u1="&#x219;" u2="&#x2122;" k="11" />
    <hkern u1="&#x219;" u2="&#xc6;" k="5" />
    <hkern u1="&#x219;" u2="&#x7d;" k="19" />
    <hkern u1="&#x219;" u2="v" k="6" />
    <hkern u1="&#x219;" u2="]" k="24" />
    <hkern u1="&#x219;" u2="\" k="26" />
    <hkern u1="&#x219;" u2="X" k="6" />
    <hkern u1="&#x219;" u2="V" k="20" />
    <hkern u1="&#x219;" u2="&#x3f;" k="5" />
    <hkern u1="&#x219;" u2="&#x29;" k="10" />
    <hkern u1="&#x21a;" u2="&#x1ef9;" k="55" />
    <hkern u1="&#x21a;" u2="&#x1eab;" k="63" />
    <hkern u1="&#x21a;" u2="&#x16d;" k="66" />
    <hkern u1="&#x21a;" u2="&#x169;" k="66" />
    <hkern u1="&#x21a;" u2="&#x15d;" k="68" />
    <hkern u1="&#x21a;" u2="&#x159;" k="42" />
    <hkern u1="&#x21a;" u2="&#x155;" k="51" />
    <hkern u1="&#x21a;" u2="&#x151;" k="59" />
    <hkern u1="&#x21a;" u2="&#x135;" k="-36" />
    <hkern u1="&#x21a;" u2="&#x131;" k="67" />
    <hkern u1="&#x21a;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x21a;" u2="&#x12b;" k="-30" />
    <hkern u1="&#x21a;" u2="&#x129;" k="-52" />
    <hkern u1="&#x21a;" u2="&#x11f;" k="83" />
    <hkern u1="&#x21a;" u2="&#x109;" k="59" />
    <hkern u1="&#x21a;" u2="&#xf0;" k="25" />
    <hkern u1="&#x21a;" u2="&#xef;" k="-51" />
    <hkern u1="&#x21a;" u2="&#xee;" k="-41" />
    <hkern u1="&#x21a;" u2="&#xec;" k="-69" />
    <hkern u1="&#x21a;" u2="&#xe4;" k="66" />
    <hkern u1="&#x21a;" u2="&#xe3;" k="54" />
    <hkern u1="&#x21a;" u2="&#xc6;" k="51" />
    <hkern u1="&#x21a;" u2="&#xae;" k="4" />
    <hkern u1="&#x21a;" u2="x" k="59" />
    <hkern u1="&#x21a;" u2="v" k="57" />
    <hkern u1="&#x21a;" u2="f" k="15" />
    <hkern u1="&#x21a;" u2="&#x40;" k="20" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="50" />
    <hkern u1="&#x21a;" u2="&#x26;" k="15" />
    <hkern u1="&#x21b;" u2="&#x7d;" k="4" />
    <hkern u1="&#x21b;" u2="]" k="5" />
    <hkern u1="&#x21b;" u2="\" k="13" />
    <hkern u1="&#x21b;" u2="V" k="4" />
    <hkern u1="&#x1e80;" g2="braceright.cap" k="4" />
    <hkern u1="&#x1e80;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x1e80;" u2="&#x135;" k="-20" />
    <hkern u1="&#x1e80;" u2="&#x131;" k="14" />
    <hkern u1="&#x1e80;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e80;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e80;" u2="&#x129;" k="-37" />
    <hkern u1="&#x1e80;" u2="&#xf0;" k="15" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-39" />
    <hkern u1="&#x1e80;" u2="&#xee;" k="-22" />
    <hkern u1="&#x1e80;" u2="&#xec;" k="-51" />
    <hkern u1="&#x1e80;" u2="&#xc6;" k="25" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="28" />
    <hkern u1="&#x1e80;" u2="&#x26;" k="3" />
    <hkern u1="&#x1e81;" u2="&#xf0;" k="8" />
    <hkern u1="&#x1e81;" u2="&#xc6;" k="16" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="21" />
    <hkern u1="&#x1e81;" u2="]" k="26" />
    <hkern u1="&#x1e81;" u2="\" k="13" />
    <hkern u1="&#x1e81;" u2="X" k="24" />
    <hkern u1="&#x1e81;" u2="V" k="9" />
    <hkern u1="&#x1e81;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="15" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="10" />
    <hkern u1="&#x1e82;" g2="braceright.cap" k="4" />
    <hkern u1="&#x1e82;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x1e82;" u2="&#x135;" k="-20" />
    <hkern u1="&#x1e82;" u2="&#x131;" k="14" />
    <hkern u1="&#x1e82;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e82;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e82;" u2="&#x129;" k="-37" />
    <hkern u1="&#x1e82;" u2="&#xf0;" k="15" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-39" />
    <hkern u1="&#x1e82;" u2="&#xee;" k="-22" />
    <hkern u1="&#x1e82;" u2="&#xec;" k="-51" />
    <hkern u1="&#x1e82;" u2="&#xc6;" k="25" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="28" />
    <hkern u1="&#x1e82;" u2="&#x26;" k="3" />
    <hkern u1="&#x1e83;" u2="&#xf0;" k="8" />
    <hkern u1="&#x1e83;" u2="&#xc6;" k="16" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="21" />
    <hkern u1="&#x1e83;" u2="]" k="26" />
    <hkern u1="&#x1e83;" u2="\" k="13" />
    <hkern u1="&#x1e83;" u2="X" k="24" />
    <hkern u1="&#x1e83;" u2="V" k="9" />
    <hkern u1="&#x1e83;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="15" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="10" />
    <hkern u1="&#x1e84;" g2="braceright.cap" k="4" />
    <hkern u1="&#x1e84;" g2="bracketright.cap" k="4" />
    <hkern u1="&#x1e84;" u2="&#x135;" k="-20" />
    <hkern u1="&#x1e84;" u2="&#x131;" k="14" />
    <hkern u1="&#x1e84;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e84;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e84;" u2="&#x129;" k="-37" />
    <hkern u1="&#x1e84;" u2="&#xf0;" k="15" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-39" />
    <hkern u1="&#x1e84;" u2="&#xee;" k="-22" />
    <hkern u1="&#x1e84;" u2="&#xec;" k="-51" />
    <hkern u1="&#x1e84;" u2="&#xc6;" k="25" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="28" />
    <hkern u1="&#x1e84;" u2="&#x26;" k="3" />
    <hkern u1="&#x1e85;" u2="&#xf0;" k="8" />
    <hkern u1="&#x1e85;" u2="&#xc6;" k="16" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="21" />
    <hkern u1="&#x1e85;" u2="]" k="26" />
    <hkern u1="&#x1e85;" u2="\" k="13" />
    <hkern u1="&#x1e85;" u2="X" k="24" />
    <hkern u1="&#x1e85;" u2="V" k="9" />
    <hkern u1="&#x1e85;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="15" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="10" />
    <hkern u1="&#x1eab;" u2="&#x2122;" k="14" />
    <hkern u1="&#x1eab;" u2="&#x7d;" k="4" />
    <hkern u1="&#x1eab;" u2="v" k="5" />
    <hkern u1="&#x1eab;" u2="]" k="5" />
    <hkern u1="&#x1eab;" u2="\" k="40" />
    <hkern u1="&#x1eab;" u2="V" k="27" />
    <hkern u1="&#x1eab;" u2="&#x3f;" k="14" />
    <hkern u1="&#x1eab;" u2="&#x2a;" k="6" />
    <hkern u1="&#x1eb0;" g2="braceright.cap" k="6" />
    <hkern u1="&#x1eb0;" g2="bracketright.cap" k="8" />
    <hkern u1="&#x1eb0;" u2="&#x2122;" k="35" />
    <hkern u1="&#x1eb0;" u2="&#xf0;" k="5" />
    <hkern u1="&#x1eb0;" u2="&#xae;" k="21" />
    <hkern u1="&#x1eb0;" u2="&#x7d;" k="9" />
    <hkern u1="&#x1eb0;" u2="v" k="16" />
    <hkern u1="&#x1eb0;" u2="f" k="8" />
    <hkern u1="&#x1eb0;" u2="]" k="10" />
    <hkern u1="&#x1eb0;" u2="\" k="44" />
    <hkern u1="&#x1eb0;" u2="V" k="28" />
    <hkern u1="&#x1eb0;" u2="&#x3f;" k="19" />
    <hkern u1="&#x1eb0;" u2="&#x2a;" k="31" />
    <hkern u1="&#x1ec5;" u2="&#x2122;" k="13" />
    <hkern u1="&#x1ec5;" u2="&#xc6;" k="5" />
    <hkern u1="&#x1ec5;" u2="&#x7d;" k="18" />
    <hkern u1="&#x1ec5;" u2="v" k="7" />
    <hkern u1="&#x1ec5;" u2="]" k="11" />
    <hkern u1="&#x1ec5;" u2="\" k="36" />
    <hkern u1="&#x1ec5;" u2="X" k="4" />
    <hkern u1="&#x1ec5;" u2="V" k="28" />
    <hkern u1="&#x1ec5;" u2="&#x3f;" k="15" />
    <hkern u1="&#x1ec5;" u2="&#x29;" k="9" />
    <hkern u1="&#x1ed7;" u2="&#x2122;" k="15" />
    <hkern u1="&#x1ed7;" u2="&#xc6;" k="7" />
    <hkern u1="&#x1ed7;" u2="&#x7d;" k="25" />
    <hkern u1="&#x1ed7;" u2="x" k="11" />
    <hkern u1="&#x1ed7;" u2="v" k="8" />
    <hkern u1="&#x1ed7;" u2="]" k="32" />
    <hkern u1="&#x1ed7;" u2="\" k="39" />
    <hkern u1="&#x1ed7;" u2="X" k="24" />
    <hkern u1="&#x1ed7;" u2="V" k="30" />
    <hkern u1="&#x1ed7;" u2="&#x3f;" k="19" />
    <hkern u1="&#x1ed7;" u2="&#x2a;" k="6" />
    <hkern u1="&#x1ed7;" u2="&#x29;" k="19" />
    <hkern u1="&#x1ef2;" g2="braceright.cap" k="5" />
    <hkern u1="&#x1ef2;" g2="bracketright.cap" k="6" />
    <hkern u1="&#x1ef2;" u2="&#x1ef9;" k="26" />
    <hkern u1="&#x1ef2;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#x1ef2;" u2="&#x159;" k="32" />
    <hkern u1="&#x1ef2;" u2="&#x155;" k="38" />
    <hkern u1="&#x1ef2;" u2="&#x151;" k="54" />
    <hkern u1="&#x1ef2;" u2="&#x142;" k="6" />
    <hkern u1="&#x1ef2;" u2="&#x135;" k="-11" />
    <hkern u1="&#x1ef2;" u2="&#x131;" k="60" />
    <hkern u1="&#x1ef2;" u2="&#x12d;" k="-45" />
    <hkern u1="&#x1ef2;" u2="&#x12b;" k="-34" />
    <hkern u1="&#x1ef2;" u2="&#x129;" k="-47" />
    <hkern u1="&#x1ef2;" u2="&#x103;" k="59" />
    <hkern u1="&#x1ef2;" u2="&#xff;" k="28" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="32" />
    <hkern u1="&#x1ef2;" u2="&#xef;" k="-59" />
    <hkern u1="&#x1ef2;" u2="&#xee;" k="-16" />
    <hkern u1="&#x1ef2;" u2="&#xec;" k="-63" />
    <hkern u1="&#x1ef2;" u2="&#xeb;" k="64" />
    <hkern u1="&#x1ef2;" u2="&#xe4;" k="49" />
    <hkern u1="&#x1ef2;" u2="&#xe3;" k="42" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1ef2;" u2="&#xc6;" k="56" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="20" />
    <hkern u1="&#x1ef2;" u2="x" k="37" />
    <hkern u1="&#x1ef2;" u2="v" k="36" />
    <hkern u1="&#x1ef2;" u2="f" k="21" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="36" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="65" />
    <hkern u1="&#x1ef2;" u2="&#x2a;" k="-4" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="32" />
    <hkern u1="&#x1ef3;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1ef3;" u2="&#xc6;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="17" />
    <hkern u1="&#x1ef3;" u2="]" k="23" />
    <hkern u1="&#x1ef3;" u2="\" k="13" />
    <hkern u1="&#x1ef3;" u2="X" k="24" />
    <hkern u1="&#x1ef3;" u2="V" k="7" />
    <hkern u1="&#x1ef3;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="20" />
    <hkern u1="&#x1ef8;" g2="braceright.cap" k="5" />
    <hkern u1="&#x1ef8;" g2="bracketright.cap" k="6" />
    <hkern u1="&#x1ef8;" u2="&#x1ef9;" k="26" />
    <hkern u1="&#x1ef8;" u2="&#x1ef3;" k="31" />
    <hkern u1="&#x1ef8;" u2="&#x159;" k="32" />
    <hkern u1="&#x1ef8;" u2="&#x155;" k="38" />
    <hkern u1="&#x1ef8;" u2="&#x151;" k="54" />
    <hkern u1="&#x1ef8;" u2="&#x142;" k="6" />
    <hkern u1="&#x1ef8;" u2="&#x135;" k="-11" />
    <hkern u1="&#x1ef8;" u2="&#x131;" k="60" />
    <hkern u1="&#x1ef8;" u2="&#x12d;" k="-45" />
    <hkern u1="&#x1ef8;" u2="&#x12b;" k="-34" />
    <hkern u1="&#x1ef8;" u2="&#x129;" k="-47" />
    <hkern u1="&#x1ef8;" u2="&#x103;" k="59" />
    <hkern u1="&#x1ef8;" u2="&#xff;" k="28" />
    <hkern u1="&#x1ef8;" u2="&#xf0;" k="32" />
    <hkern u1="&#x1ef8;" u2="&#xef;" k="-59" />
    <hkern u1="&#x1ef8;" u2="&#xee;" k="-16" />
    <hkern u1="&#x1ef8;" u2="&#xec;" k="-63" />
    <hkern u1="&#x1ef8;" u2="&#xeb;" k="64" />
    <hkern u1="&#x1ef8;" u2="&#xe4;" k="49" />
    <hkern u1="&#x1ef8;" u2="&#xe3;" k="42" />
    <hkern u1="&#x1ef8;" u2="&#xdf;" k="10" />
    <hkern u1="&#x1ef8;" u2="&#xc6;" k="56" />
    <hkern u1="&#x1ef8;" u2="&#xae;" k="20" />
    <hkern u1="&#x1ef8;" u2="x" k="37" />
    <hkern u1="&#x1ef8;" u2="v" k="36" />
    <hkern u1="&#x1ef8;" u2="f" k="21" />
    <hkern u1="&#x1ef8;" u2="&#x40;" k="36" />
    <hkern u1="&#x1ef8;" u2="&#x2f;" k="65" />
    <hkern u1="&#x1ef8;" u2="&#x2a;" k="-4" />
    <hkern u1="&#x1ef8;" u2="&#x26;" k="32" />
    <hkern u1="&#x1ef9;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1ef9;" u2="&#xc6;" k="19" />
    <hkern u1="&#x1ef9;" u2="&#x7d;" k="17" />
    <hkern u1="&#x1ef9;" u2="]" k="23" />
    <hkern u1="&#x1ef9;" u2="\" k="13" />
    <hkern u1="&#x1ef9;" u2="X" k="24" />
    <hkern u1="&#x1ef9;" u2="V" k="7" />
    <hkern u1="&#x1ef9;" u2="&#x3f;" k="4" />
    <hkern u1="&#x1ef9;" u2="&#x2f;" k="20" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="13" />
    <hkern u1="&#x2013;" u2="x" k="26" />
    <hkern u1="&#x2013;" u2="v" k="11" />
    <hkern u1="&#x2013;" u2="f" k="11" />
    <hkern u1="&#x2013;" u2="X" k="35" />
    <hkern u1="&#x2013;" u2="V" k="27" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="13" />
    <hkern u1="&#x2014;" u2="x" k="26" />
    <hkern u1="&#x2014;" u2="v" k="11" />
    <hkern u1="&#x2014;" u2="f" k="11" />
    <hkern u1="&#x2014;" u2="X" k="35" />
    <hkern u1="&#x2014;" u2="V" k="27" />
    <hkern u1="&#x2018;" u2="&#x135;" k="-6" />
    <hkern u1="&#x2018;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x2018;" u2="&#x129;" k="-21" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="4" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-27" />
    <hkern u1="&#x2018;" u2="&#xee;" k="-11" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-36" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="49" />
    <hkern u1="&#x2019;" u2="&#x12d;" k="-19" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="-9" />
    <hkern u1="&#x2019;" u2="&#x129;" k="-26" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="4" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-34" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-7" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-39" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="53" />
    <hkern u1="&#x2019;" u2="&#x40;" k="24" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="72" />
    <hkern u1="&#x2019;" u2="&#x26;" k="27" />
    <hkern u1="&#x201a;" u2="v" k="28" />
    <hkern u1="&#x201a;" u2="f" k="10" />
    <hkern u1="&#x201a;" u2="V" k="45" />
    <hkern u1="&#x201c;" u2="&#x135;" k="-6" />
    <hkern u1="&#x201c;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x201c;" u2="&#x129;" k="-21" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="4" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-27" />
    <hkern u1="&#x201c;" u2="&#xee;" k="-11" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-36" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="49" />
    <hkern u1="&#x201d;" u2="&#x135;" k="-5" />
    <hkern u1="&#x201d;" u2="&#x12d;" k="-19" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="-9" />
    <hkern u1="&#x201d;" u2="&#x129;" k="-26" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="4" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-34" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-7" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-39" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="53" />
    <hkern u1="&#x201d;" u2="&#x40;" k="24" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="72" />
    <hkern u1="&#x201d;" u2="&#x26;" k="27" />
    <hkern u1="&#x201e;" u2="v" k="28" />
    <hkern u1="&#x201e;" u2="f" k="10" />
    <hkern u1="&#x201e;" u2="V" k="45" />
    <hkern u1="&#x2039;" u2="V" k="15" />
    <hkern u1="&#x203a;" u2="&#x141;" k="-5" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="3" />
    <hkern u1="&#x203a;" u2="x" k="23" />
    <hkern u1="&#x203a;" u2="v" k="3" />
    <hkern u1="&#x203a;" u2="f" k="7" />
    <hkern u1="&#x203a;" u2="X" k="22" />
    <hkern u1="&#x203a;" u2="V" k="25" />
    <hkern u1="&#x2122;" u2="&#x1eb0;" k="25" />
    <hkern u1="&#x2122;" u2="&#x1fc;" k="25" />
    <hkern u1="&#x2122;" u2="&#x1fa;" k="25" />
    <hkern u1="&#x2122;" u2="&#x135;" k="-22" />
    <hkern u1="&#x2122;" u2="&#x134;" k="16" />
    <hkern u1="&#x2122;" u2="&#x12d;" k="-12" />
    <hkern u1="&#x2122;" u2="&#x129;" k="-15" />
    <hkern u1="&#x2122;" u2="&#x104;" k="25" />
    <hkern u1="&#x2122;" u2="&#x102;" k="25" />
    <hkern u1="&#x2122;" u2="&#x100;" k="25" />
    <hkern u1="&#x2122;" u2="&#xef;" k="-26" />
    <hkern u1="&#x2122;" u2="&#xee;" k="-26" />
    <hkern u1="&#x2122;" u2="&#xec;" k="-33" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="31" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="25" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="25" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="25" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="25" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="25" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="25" />
    <hkern u1="&#x2122;" u2="J" k="16" />
    <hkern u1="&#x2122;" u2="A" k="25" />
    <hkern g1="questiondown.cap" u2="&#x1ef8;" k="27" />
    <hkern g1="questiondown.cap" u2="&#x1ef2;" k="27" />
    <hkern g1="questiondown.cap" u2="&#x1e84;" k="6" />
    <hkern g1="questiondown.cap" u2="&#x1e82;" k="6" />
    <hkern g1="questiondown.cap" u2="&#x1e80;" k="6" />
    <hkern g1="questiondown.cap" u2="&#x21a;" k="13" />
    <hkern g1="questiondown.cap" u2="&#x1fe;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x178;" k="27" />
    <hkern g1="questiondown.cap" u2="&#x176;" k="27" />
    <hkern g1="questiondown.cap" u2="&#x174;" k="6" />
    <hkern g1="questiondown.cap" u2="&#x166;" k="13" />
    <hkern g1="questiondown.cap" u2="&#x164;" k="13" />
    <hkern g1="questiondown.cap" u2="&#x152;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x150;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x14e;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x14c;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x122;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x120;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x11e;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x11c;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x10c;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x10a;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x108;" k="3" />
    <hkern g1="questiondown.cap" u2="&#x106;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xdd;" k="27" />
    <hkern g1="questiondown.cap" u2="&#xd8;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xd6;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xd5;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xd4;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xd3;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xd2;" k="3" />
    <hkern g1="questiondown.cap" u2="&#xc7;" k="3" />
    <hkern g1="questiondown.cap" u2="Y" k="27" />
    <hkern g1="questiondown.cap" u2="W" k="6" />
    <hkern g1="questiondown.cap" u2="V" k="19" />
    <hkern g1="questiondown.cap" u2="T" k="13" />
    <hkern g1="questiondown.cap" u2="Q" k="3" />
    <hkern g1="questiondown.cap" u2="O" k="3" />
    <hkern g1="questiondown.cap" u2="G" k="3" />
    <hkern g1="questiondown.cap" u2="C" k="3" />
    <hkern g1="endash.cap" u2="&#xc6;" k="16" />
    <hkern g1="endash.cap" u2="X" k="41" />
    <hkern g1="endash.cap" u2="V" k="24" />
    <hkern g1="emdash.cap" u2="&#xc6;" k="16" />
    <hkern g1="emdash.cap" u2="X" k="41" />
    <hkern g1="emdash.cap" u2="V" k="24" />
    <hkern g1="parenleft.cap" u2="&#x1fe;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x152;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x150;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x14e;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x14c;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x134;" k="-17" />
    <hkern g1="parenleft.cap" u2="&#x128;" k="-22" />
    <hkern g1="parenleft.cap" u2="&#x122;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x120;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x11e;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x11c;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x10c;" k="13" />
    <hkern g1="parenleft.cap" u2="&#x10a;" k="13" />
    <hkern g1="parenleft.cap" u2="&#x108;" k="13" />
    <hkern g1="parenleft.cap" u2="&#x106;" k="13" />
    <hkern g1="parenleft.cap" u2="&#xd8;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd6;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd5;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd4;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd3;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd2;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xcf;" k="-23" />
    <hkern g1="parenleft.cap" u2="&#xce;" k="-34" />
    <hkern g1="parenleft.cap" u2="&#xc7;" k="13" />
    <hkern g1="parenleft.cap" u2="Q" k="14" />
    <hkern g1="parenleft.cap" u2="O" k="14" />
    <hkern g1="parenleft.cap" u2="G" k="14" />
    <hkern g1="parenleft.cap" u2="C" k="13" />
    <hkern g1="bracketleft.cap" u2="&#x1ef8;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x1ef2;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x1eb0;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#x1e84;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x1e82;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x1e80;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x218;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x1fe;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x1fc;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#x1fa;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#x178;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x176;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x174;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x172;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x170;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x16e;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x16c;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x16a;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x168;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#x160;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x15e;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x15c;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x15a;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#x158;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x156;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x154;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x152;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x150;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x14e;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x14c;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x14a;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x147;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x145;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x143;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x141;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x13d;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x13b;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x139;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x136;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x134;" k="-14" />
    <hkern g1="bracketleft.cap" u2="&#x130;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x12e;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x12c;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x12a;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x128;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x126;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x124;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x122;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x120;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x11e;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x11c;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#x11a;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x118;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x116;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x114;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x112;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x110;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x10e;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#x10c;" k="22" />
    <hkern g1="bracketleft.cap" u2="&#x10a;" k="22" />
    <hkern g1="bracketleft.cap" u2="&#x108;" k="22" />
    <hkern g1="bracketleft.cap" u2="&#x106;" k="22" />
    <hkern g1="bracketleft.cap" u2="&#x104;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#x102;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#x100;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xde;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xdd;" k="6" />
    <hkern g1="bracketleft.cap" u2="&#xdc;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#xdb;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#xda;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#xd9;" k="5" />
    <hkern g1="bracketleft.cap" u2="&#xd8;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd6;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd5;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd4;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd3;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd2;" k="24" />
    <hkern g1="bracketleft.cap" u2="&#xd1;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xd0;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xcf;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xce;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xcd;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xcc;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xcb;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xca;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xc9;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xc8;" k="4" />
    <hkern g1="bracketleft.cap" u2="&#xc7;" k="22" />
    <hkern g1="bracketleft.cap" u2="&#xc6;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc5;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc4;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc3;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc2;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc1;" k="8" />
    <hkern g1="bracketleft.cap" u2="&#xc0;" k="8" />
    <hkern g1="bracketleft.cap" u2="Y" k="6" />
    <hkern g1="bracketleft.cap" u2="W" k="5" />
    <hkern g1="bracketleft.cap" u2="V" k="5" />
    <hkern g1="bracketleft.cap" u2="U" k="5" />
    <hkern g1="bracketleft.cap" u2="S" k="6" />
    <hkern g1="bracketleft.cap" u2="R" k="4" />
    <hkern g1="bracketleft.cap" u2="Q" k="24" />
    <hkern g1="bracketleft.cap" u2="P" k="4" />
    <hkern g1="bracketleft.cap" u2="O" k="24" />
    <hkern g1="bracketleft.cap" u2="N" k="4" />
    <hkern g1="bracketleft.cap" u2="M" k="4" />
    <hkern g1="bracketleft.cap" u2="L" k="4" />
    <hkern g1="bracketleft.cap" u2="K" k="4" />
    <hkern g1="bracketleft.cap" u2="I" k="4" />
    <hkern g1="bracketleft.cap" u2="H" k="4" />
    <hkern g1="bracketleft.cap" u2="G" k="24" />
    <hkern g1="bracketleft.cap" u2="F" k="4" />
    <hkern g1="bracketleft.cap" u2="E" k="4" />
    <hkern g1="bracketleft.cap" u2="D" k="4" />
    <hkern g1="bracketleft.cap" u2="C" k="22" />
    <hkern g1="bracketleft.cap" u2="B" k="4" />
    <hkern g1="bracketleft.cap" u2="A" k="8" />
    <hkern g1="braceleft.cap" u2="&#x1ef8;" k="6" />
    <hkern g1="braceleft.cap" u2="&#x1ef2;" k="6" />
    <hkern g1="braceleft.cap" u2="&#x1eb0;" k="7" />
    <hkern g1="braceleft.cap" u2="&#x1e84;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x1e82;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x1e80;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x218;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x1fe;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x1fc;" k="7" />
    <hkern g1="braceleft.cap" u2="&#x1fa;" k="7" />
    <hkern g1="braceleft.cap" u2="&#x178;" k="6" />
    <hkern g1="braceleft.cap" u2="&#x176;" k="6" />
    <hkern g1="braceleft.cap" u2="&#x174;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x172;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x170;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x16e;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x16c;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x16a;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x168;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x160;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x15e;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x15c;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x15a;" k="5" />
    <hkern g1="braceleft.cap" u2="&#x158;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x156;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x154;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x152;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x150;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x14e;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x14c;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x14a;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x147;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x145;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x143;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x141;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x13d;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x13b;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x139;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x136;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x134;" k="-13" />
    <hkern g1="braceleft.cap" u2="&#x130;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x12e;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x12c;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x12a;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x128;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x126;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x124;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x122;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x120;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x11e;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x11c;" k="19" />
    <hkern g1="braceleft.cap" u2="&#x11a;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x118;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x116;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x114;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x112;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x110;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x10e;" k="4" />
    <hkern g1="braceleft.cap" u2="&#x10c;" k="18" />
    <hkern g1="braceleft.cap" u2="&#x10a;" k="18" />
    <hkern g1="braceleft.cap" u2="&#x108;" k="18" />
    <hkern g1="braceleft.cap" u2="&#x106;" k="18" />
    <hkern g1="braceleft.cap" u2="&#x104;" k="7" />
    <hkern g1="braceleft.cap" u2="&#x102;" k="7" />
    <hkern g1="braceleft.cap" u2="&#x100;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xde;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xdd;" k="6" />
    <hkern g1="braceleft.cap" u2="&#xdc;" k="5" />
    <hkern g1="braceleft.cap" u2="&#xdb;" k="5" />
    <hkern g1="braceleft.cap" u2="&#xda;" k="5" />
    <hkern g1="braceleft.cap" u2="&#xd9;" k="5" />
    <hkern g1="braceleft.cap" u2="&#xd8;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd6;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd5;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd4;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd3;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd2;" k="19" />
    <hkern g1="braceleft.cap" u2="&#xd1;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xd0;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xcf;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xce;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xcd;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xcc;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xcb;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xca;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xc9;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xc8;" k="4" />
    <hkern g1="braceleft.cap" u2="&#xc7;" k="18" />
    <hkern g1="braceleft.cap" u2="&#xc6;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc5;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc4;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc3;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc2;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc1;" k="7" />
    <hkern g1="braceleft.cap" u2="&#xc0;" k="7" />
    <hkern g1="braceleft.cap" u2="Y" k="6" />
    <hkern g1="braceleft.cap" u2="W" k="4" />
    <hkern g1="braceleft.cap" u2="V" k="5" />
    <hkern g1="braceleft.cap" u2="U" k="5" />
    <hkern g1="braceleft.cap" u2="S" k="5" />
    <hkern g1="braceleft.cap" u2="R" k="4" />
    <hkern g1="braceleft.cap" u2="Q" k="19" />
    <hkern g1="braceleft.cap" u2="P" k="4" />
    <hkern g1="braceleft.cap" u2="O" k="19" />
    <hkern g1="braceleft.cap" u2="N" k="4" />
    <hkern g1="braceleft.cap" u2="M" k="4" />
    <hkern g1="braceleft.cap" u2="L" k="4" />
    <hkern g1="braceleft.cap" u2="K" k="4" />
    <hkern g1="braceleft.cap" u2="I" k="4" />
    <hkern g1="braceleft.cap" u2="H" k="4" />
    <hkern g1="braceleft.cap" u2="G" k="19" />
    <hkern g1="braceleft.cap" u2="F" k="4" />
    <hkern g1="braceleft.cap" u2="E" k="4" />
    <hkern g1="braceleft.cap" u2="D" k="4" />
    <hkern g1="braceleft.cap" u2="C" k="18" />
    <hkern g1="braceleft.cap" u2="B" k="4" />
    <hkern g1="braceleft.cap" u2="A" k="7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="hyphen,endash,emdash"
	k="4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="endash.cap,emdash.cap"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quoteleft,quotedblleft"
	k="32" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quoteright,quotedblright"
	k="33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quotedbl,quotesingle"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="t,tcaron,tbar,tcommaaccent"
	k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="d,q,dcaron,dcroat"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="24" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="endash.cap,emdash.cap"
	k="29" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotleft,guilsinglleft"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="13" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="3" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="d,q,dcaron,dcroat"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="hyphen,endash,emdash"
	k="17" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="endash.cap,emdash.cap"
	k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="t,tcaron,tbar,tcommaaccent"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="J,Jcircumflex"
	k="4" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="6" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="J,Jcircumflex"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="J,Jcircumflex"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="J,Jcircumflex"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="J,Jcircumflex"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="K,Kcommaaccent"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="9" />
    <hkern g1="K,Kcommaaccent"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="11" />
    <hkern g1="K,Kcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="14" />
    <hkern g1="K,Kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="endash.cap,emdash.cap"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="22" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="21" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="19" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="4" />
    <hkern g1="K,Kcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="84" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="9" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="44" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="87" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="endash.cap,emdash.cap"
	k="71" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="82" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="81" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="82" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="t,tcaron,tbar,tcommaaccent"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="26" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="guillemotleft,guilsinglleft"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="guillemotright,guilsinglright"
	k="6" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="13" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="28" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="11" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="4" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="22" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="d,q,dcaron,dcroat"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="4" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotleft,guilsinglleft"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="9" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="6" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="12" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="77" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="86" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="endash.cap,emdash.cap"
	k="60" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="79" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="17" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="58" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="58" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="58" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="68" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="41" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="59" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="79" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="67" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="guillemotright,guilsinglright"
	k="52" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="b,thorn"
	k="3" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="colon,semicolon"
	k="51" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="77" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="77" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="d,q,dcaron,dcroat"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="d,q,dcaron,dcroat"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="17" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="endash.cap,emdash.cap"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="11" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="d,q,dcaron,dcroat"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="73" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="hyphen,endash,emdash"
	k="67" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="endash.cap,emdash.cap"
	k="62" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="t,tcaron,tbar,tcommaaccent"
	k="17" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="38" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="guillemotleft,guilsinglleft"
	k="62" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="J,Jcircumflex"
	k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="64" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="guillemotright,guilsinglright"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="z,zacute,zdotaccent,zcaron"
	k="49" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="d,q,dcaron,dcroat"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="24" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="endash.cap,emdash.cap"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="11" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="73" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="7" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="65" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="quotedbl,quotesingle"
	k="7" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="6" />
    <hkern g1="b,p,thorn"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="6" />
    <hkern g1="b,p,thorn"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="78" />
    <hkern g1="b,p,thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="b,p,thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="18" />
    <hkern g1="b,p,thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="67" />
    <hkern g1="b,p,thorn"
	g2="quoteleft,quotedblleft"
	k="7" />
    <hkern g1="b,p,thorn"
	g2="quoteright,quotedblright"
	k="7" />
    <hkern g1="b,p,thorn"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="b,p,thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="8" />
    <hkern g1="b,p,thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="6" />
    <hkern g1="b,p,thorn"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="b,p,thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="9" />
    <hkern g1="b,p,thorn"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="86" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="49" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="27" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotleft,guilsinglleft"
	k="21" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="colon,semicolon"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="51" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="40" />
    <hkern g1="d,dcroat"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="81" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="81" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="60" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="32" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="hyphen,endash,emdash"
	k="11" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="55" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="43" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,tcaron,tbar,tcommaaccent"
	k="3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="58" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="60" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quoteright,quotedblright"
	k="37" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedbl,quotesingle"
	k="42" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t,tcaron,tbar,tcommaaccent"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zacute,zdotaccent,zcaron"
	k="18" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="22" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="60" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="67" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteright,quotedblright"
	k="61" />
    <hkern g1="hyphen,endash,emdash"
	g2="quotedbl,quotesingle"
	k="66" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcaron,tbar,tcommaaccent"
	k="12" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="3" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="12" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="4" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="22" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="23" />
    <hkern g1="hyphen,endash,emdash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="4" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="24" />
    <hkern g1="endash.cap,emdash.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="18" />
    <hkern g1="endash.cap,emdash.cap"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="60" />
    <hkern g1="endash.cap,emdash.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="endash.cap,emdash.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="61" />
    <hkern g1="endash.cap,emdash.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="12" />
    <hkern g1="endash.cap,emdash.cap"
	g2="J,Jcircumflex"
	k="22" />
    <hkern g1="endash.cap,emdash.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="29" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="k,kcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="63" />
    <hkern g1="k,kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="k,kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="38" />
    <hkern g1="k,kcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="9" />
    <hkern g1="k,kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="29" />
    <hkern g1="k,kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="11" />
    <hkern g1="k,kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="l,lacute,lcommaaccent,lslash"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="dcaron,lcaron"
	g2="d,q,dcaron,dcroat"
	k="9" />
    <hkern g1="dcaron,lcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="7" />
    <hkern g1="dcaron,lcaron"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="dcaron,lcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="quotedbl,quotesingle"
	k="-22" />
    <hkern g1="dcaron,lcaron"
	g2="t,tcaron,tbar,tcommaaccent"
	k="-19" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="-17" />
    <hkern g1="dcaron,lcaron"
	g2="guillemotleft,guilsinglleft"
	k="33" />
    <hkern g1="dcaron,lcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="39" />
    <hkern g1="dcaron,lcaron"
	g2="b,thorn"
	k="-48" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="3" />
    <hkern g1="dcaron,lcaron"
	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent"
	k="-48" />
    <hkern g1="dcaron,lcaron"
	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex"
	k="-48" />
    <hkern g1="dcaron,lcaron"
	g2="l,lacute,lcommaaccent,lcaron,lslash"
	k="-42" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="81" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="68" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quoteleft,quotedblleft"
	k="3" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="5" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="8" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="81" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="70" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="9" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="9" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="72" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="127" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteright,quotedblright"
	k="130" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="135" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="t,tcaron,tbar,tcommaaccent"
	k="16" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="30" />
    <hkern g1="quoteleft,quotedblleft"
	g2="d,q,dcaron,dcroat"
	k="23" />
    <hkern g1="quoteleft,quotedblleft"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="17" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="139" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="12" />
    <hkern g1="quoteright,quotedblright"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="d,q,dcaron,dcroat"
	k="29" />
    <hkern g1="quoteright,quotedblright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="20" />
    <hkern g1="quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="78" />
    <hkern g1="quoteright,quotedblright"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="24" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotleft,guilsinglleft"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="43" />
    <hkern g1="quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="147" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="6" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotright,guilsinglright"
	k="27" />
    <hkern g1="quoteright,quotedblright"
	g2="colon,semicolon"
	k="9" />
    <hkern g1="quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="17" />
    <hkern g1="quotedbl,quotesingle"
	g2="d,q,dcaron,dcroat"
	k="16" />
    <hkern g1="quotedbl,quotesingle"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="hyphen,endash,emdash"
	k="67" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="13" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotleft,guilsinglleft"
	k="45" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="36" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="135" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="54" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="3" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="4" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="32" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="27" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="J,Jcircumflex"
	k="21" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="11" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="53" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="78" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="53" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="hyphen,endash,emdash"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="7" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="J,Jcircumflex"
	k="9" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="59" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="31" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="19" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="68" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="60" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="58" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="42" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="3" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="3" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="22" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="58" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="37" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="d,q,dcaron,dcroat"
	k="9" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="hyphen,endash,emdash"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="9" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="11" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="78" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="51" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="22" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="J,Jcircumflex"
	k="3" />
  </font>
</defs></svg>
